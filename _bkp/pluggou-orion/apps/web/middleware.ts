import { routing } from "@i18n/routing";
import { config as appConfig } from "@repo/config";
import {
	getOrganizationsForSession,
	getSession,
	getOrganizationStatus,
} from "@shared/lib/middleware-helpers";
import createMiddleware from "next-intl/middleware";
import { type NextRequest, NextResponse } from "next/server";

const intlMiddleware = createMiddleware(routing);

export default async function middleware(req: NextRequest) {
	const { pathname, origin } = req.nextUrl;

	// console.log("Middleware processando rota:", pathname);

	// Verificar se a rota é de convite
	if (pathname.includes("organization-invitation") || pathname.startsWith("/invitation/")) {
		console.log("Rota de convite detectada, permitindo acesso sem autenticação");
		return NextResponse.next();
	}

	if (pathname.startsWith("/app")) {
		const response = NextResponse.next();

		if (!appConfig.ui.saas.enabled) {
			return NextResponse.redirect(new URL("/", origin));
		}

		// Permitir acesso à página de convite e onboarding para convidados sem autenticação
		// Usando includes para garantir que todas as rotas de convite sejam capturadas
		if (pathname.includes("/organization-invitation/") || pathname.includes("/onboarding/invited")) {
			console.log("Permitindo acesso sem autenticação para:", pathname);
			return response;
		}

		const session = await getSession(req);
		let locale = req.cookies.get(appConfig.i18n.localeCookieName)?.value;

		if (!session) {
			return NextResponse.redirect(new URL("/auth/login", origin));
		}

		if (
			appConfig.users.enableOnboarding &&
			!session.user.onboardingComplete &&
			!pathname.startsWith("/app/onboarding")
		) {
			// Preservar parâmetros da URL original ao redirecionar para onboarding
			const redirectUrl = new URL("/app/onboarding", origin);
			// Copiar todos os parâmetros de query da URL original
			req.nextUrl.searchParams.forEach((value, key) => {
				redirectUrl.searchParams.set(key, value);
			});
			return NextResponse.redirect(redirectUrl);
		}

		if (!locale || (session.user.locale && locale !== session.user.locale)) {
			locale = session.user.locale ?? appConfig.i18n.defaultLocale;
			response.cookies.set(appConfig.i18n.localeCookieName, locale);
		}

		// Verificar se a rota é específica de uma organização
		const organizationSlugMatch = pathname.match(/^\/app\/([^\/]+)/);
		if (organizationSlugMatch && organizationSlugMatch[1]) {
			const organizationSlug = organizationSlugMatch[1];

			// Ignorar rotas que não são específicas de organizações
			const nonOrgRoutes = ["new-organization", "onboarding", "admin", "account", "settings"];
			if (!nonOrgRoutes.includes(organizationSlug)) {
				console.log("Verificando status da organização:", organizationSlug);

				// Verificar o status da organização
				const orgStatus = await getOrganizationStatus(req, organizationSlug);

				// Se não puder acessar, redirecionar para /app com mensagem de erro
				if (!orgStatus.canAccess) {
					console.log("Acesso negado à organização:", organizationSlug, "Status:", orgStatus.status);

					// Adicionar parâmetros de erro na URL
					const redirectUrl = new URL("/app", origin);
					if (orgStatus.message) {
						redirectUrl.searchParams.set("error", "organization_status");
						redirectUrl.searchParams.set("message", orgStatus.message);
						redirectUrl.searchParams.set("org", organizationSlug);
					}

					return NextResponse.redirect(redirectUrl);
				}
			}
		}

		if (
			appConfig.organizations.enable &&
			appConfig.organizations.requireOrganization &&
			pathname === "/app"
		) {
			const organizations = await getOrganizationsForSession(req);
			const organization =
				organizations.find(
					(org) => org.id === session?.session.activeOrganizationId,
				) || organizations[0];

			return NextResponse.redirect(
				new URL(
					organization ? `/app/${organization.slug}` : "/app/new-organization",
					origin,
				),
			);
		}

		return response;
	}

	if (pathname.startsWith("/auth/")) {
		if (!appConfig.ui.saas.enabled) {
			return NextResponse.redirect(new URL("/", origin));
		}

		const session = await getSession(req);

		if (session) {
			return NextResponse.redirect(new URL("/app", origin));
		}

		return NextResponse.next();
	}

	if (!appConfig.ui.marketing.enabled) {
		return NextResponse.redirect(new URL("/app", origin));
	}

	return intlMiddleware(req);
}

export const config = {
	matcher: [
		"/((?!invitation|api/public|api|baas|image-proxy|images|fonts|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|api/webhooks).*)",
	],
};
