"use client";

import { Card } from "@ui/components/card";
import { ReactNode } from "react";

interface AdminTableProps {
  children: ReactNode;
  className?: string;
}

export function AdminTable({ children, className = "" }: AdminTableProps) {
  return (
    <Card className={`overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm ${className}`}>
      <div className="p-6">
        {children}
      </div>
    </Card>
  );
}
