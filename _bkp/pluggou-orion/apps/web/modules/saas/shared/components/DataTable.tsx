"use client";

import { ReactNode } from "react";
import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import { Eye } from "lucide-react";

interface Column<T> {
  header: string;
  accessorKey?: keyof T;
  cell?: (item: T) => ReactNode;
  className?: string;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T) => void;
  keyField: keyof T;
  highlightColor?: string;
  showViewButton?: boolean;
  onViewButtonClick?: (item: T) => void;
}

export function DataTable<T>({
  data,
  columns,
  onRowClick,
  keyField,
  highlightColor = "#4caf50",
  showViewButton = true,
  onViewButtonClick,
}: DataTableProps<T>) {
  const handleRowClick = (item: T) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  const handleViewButtonClick = (e: React.MouseEvent, item: T) => {
    e.stopPropagation();
    if (onViewButtonClick) {
      onViewButtonClick(item);
    } else if (onRowClick) {
      onRowClick(item);
    }
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-800 text-left">
            {showViewButton && (
              <th className="pb-2 font-medium text-muted-foreground text-sm" />
            )}
            {columns.map((column, index) => (
              <th
                key={index}
                className={cn(
                  "pb-2 font-medium text-muted-foreground text-sm",
                  column.className
                )}
              >
                {column.header}
              </th>
            ))}
            {showViewButton && (
              <th className="pb-2 font-medium text-muted-foreground text-sm">
                Ações
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {data.map((item) => (
            <tr
              key={String(item[keyField])}
              className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
              onClick={() => handleRowClick(item)}
            >
              {/* Visual indicator on hover */}
              {showViewButton && (
                <td
                  className={`absolute inset-y-0 left-0 w-1 opacity-0 group-hover:opacity-100 transition-opacity`}
                  style={{ backgroundColor: highlightColor }}
                  aria-hidden="true"
                ></td>
              )}

              {columns.map((column, index) => (
                <td key={index} className={cn("py-3 text-sm", column.className)}>
                  {column.cell
                    ? column.cell(item)
                    : column.accessorKey
                    ? String(item[column.accessorKey] || "")
                    : ""}
                </td>
              ))}

              {showViewButton && (
                <td className="py-3 text-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    style={{
                      "--highlight-color": highlightColor
                    } as React.CSSProperties}
                    className="h-8 px-3 text-xs hover:bg-opacity-10 transition-colors"
                    onClick={(e) => handleViewButtonClick(e, item)}
                  >
                    <Eye
                      className="h-3.5 w-3.5 mr-1"
                      style={{ color: highlightColor }}
                    />
                  </Button>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
