'use client';
import { config } from '@repo/config';
import { useSession } from '@saas/auth/hooks/use-session';
import { useActiveOrganization } from '@saas/organizations/hooks/use-active-organization';
import { UserMenu } from '@saas/shared/components/UserMenu';
import { Logo } from '@shared/components/Logo';
import { cn } from '@ui/lib';
import {
	ChevronRightIcon,
	HomeIcon,
	SettingsIcon,
	UserCog2Icon,
	UserCogIcon,
	Wand2Icon,
	LayoutDashboardIcon,
	CreditCardIcon,
	ArrowLeftRightIcon,
	BarChartIcon,
	ReceiptIcon,
	BanknoteIcon,
	FileTextIcon,
	ShieldAlertIcon,
	SendIcon,
	CodeIcon,
	WebhookIcon,
	ChevronDownIcon,
	PuzzleIcon,
	RefreshCcwIcon,
	ShieldIcon,
	ArrowRightLeftIcon,
	KeyIcon,
	BellRingIcon,
	PackageIcon,
	Settings2,
	Settings,
	Webhook,
	Building2Icon,
	ArrowRightIcon,
	BuildingIcon,
	MoonIcon,
	SunIcon,
	UserIcon,
	XIcon,
	CalculatorIcon,
	DollarSignIcon,
	CircleDollarSignIcon,
	Ban,
	AlertTriangleIcon,
	EyeIcon,
	ChartPie,
	PiggyBank,
	Wallet2Icon,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { OrganzationSelect } from '../../organizations/components/OrganizationSelect';
import { useState, useEffect } from 'react';

export function NavBar() {
	const t = useTranslations();
	const pathname = usePathname();
	const { user, loaded: sessionLoaded } = useSession();
	const { activeOrganization, loaded: orgLoaded } = useActiveOrganization();
	const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});
	const [isAdmin, setIsAdmin] = useState<boolean>(false);

	const { useSidebarLayout } = config.ui.saas;

	const basePath = activeOrganization
		? `/app/${activeOrganization.slug}`
		: '/app';

	// Dashboard path logic: admins without active organization go to admin dashboard
	const dashboardPath = activeOrganization
		? basePath
		: isAdmin
			? '/app/admin'
			: '/app';

	// Update isAdmin when user data is loaded
	useEffect(() => {
		if (sessionLoaded && user) {
			setIsAdmin(user.role === 'admin');
		}
	}, [sessionLoaded, user]);

	const toggleSubmenu = (href: string) => {
		setOpenMenus((prev) => ({
			...prev,
			[href]: !prev[href],
		}));
	};

	interface MenuItem {
		label: string;
		href: string;
		icon?: any;
		isActive: boolean;
		subItems?: MenuItem[];
	}

	const menuItems: MenuItem[] = [
		{
			label: 'Dashboard',
			href: dashboardPath,
			icon: LayoutDashboardIcon,
			isActive:
				(activeOrganization && pathname === basePath) ||
				(!activeOrganization && isAdmin && pathname === '/app/admin') ||
				(!activeOrganization && !isAdmin && pathname === '/app'),
		},

		...(activeOrganization
			? [
					{
						label: t('app.menu.transactions') || 'Transações',
						href: `${basePath}/transactions`,
						icon: CircleDollarSignIcon,
						isActive: pathname.includes('/transactions'),
					},
					{
						label: 'Transferências',
						href: `${basePath}/statements/transfers`,
						isActive: pathname.includes('/statements/transfers'),
						icon: ArrowRightLeftIcon,
					},

					{
						label: 'Estornos',
						href: `${basePath}/statements/refunds`,
						isActive: pathname.includes('/statements/refunds'),
						icon: RefreshCcwIcon,
					},


					{
						label: t('app.menu.taxes') || 'Taxas',
						href: `${basePath}/taxes`,
						icon: CalculatorIcon,
						isActive: pathname.includes('/taxes'),
					},
					{
						label: 'Integrações',
						href: `${basePath}/integrations`,
						icon: PuzzleIcon,
						isActive: pathname.includes('/integrations'),
						subItems: [


							{
								label: 'Chaves de API',
								href: `${basePath}/integrations/api`,
								isActive: pathname.includes('/integrations/api'),
								icon: KeyIcon,
							},
							{
								label: 'Webhooks',
								href: `${basePath}/integrations/webhooks`,
								isActive: pathname.includes('/integrations/webhooks'),
								icon: WebhookIcon,
							},
						],
					},
					{
						label: 'Webhooks Portal',
						href: `${basePath}/webhooks-portal`,
						isActive: pathname.includes('/webhooks-portal'),
						icon: Webhook,
					},
					...(isAdmin && !pathname.startsWith('/app/admin/organizations')
						? [
								{
									label: t('app.menu.admin') || 'Administração',
									href: '/app/admin',
									icon: UserCogIcon,
									isActive: pathname.startsWith('/app/admin'),
								},
							]
						: []),
				]
			: []),
		...(activeOrganization
			? [
					{
						label: 'Configurações',
						href: `${basePath}/settings`,
						icon: Settings,
						isActive: pathname.startsWith(`${basePath}/settings`),
					},
				]
			: [
					...(isAdmin
						? [
								{
									label: 'Pagamentos',
									href: '/app/admin/charges',
									icon: CircleDollarSignIcon,
									isActive: pathname.startsWith('/app/admin/charges'),
								},
								{
									label: 'Saques',
									href: '/app/admin/transfers',
									icon: SendIcon,
									isActive: pathname.startsWith('/app/admin/transfers'),
								},
								{
									label: 'MED',
									href: '/app/admin/med',
									icon: AlertTriangleIcon,
									isActive: pathname.startsWith('/app/admin/med'),
								},
								{
									label: 'Usuários',
									href: '/app/admin/users',
									icon: UserIcon,
									isActive: pathname.startsWith('/app/admin/users'),
								},
								{
									label: 'Empresas',
									href: '/app/admin/organizations',
									icon: Building2Icon,
									isActive: pathname.startsWith('/app/admin/organizations'),
								},
								{
									label: 'Gateways',
									href: '/app/admin/gateways',
									icon: CreditCardIcon,
									isActive: pathname.startsWith('/app/admin/gateways'),
								},
								{
									label: 'Relatórios',
									href: '/app/admin/reports',
									icon: ChartPie,
									isActive: pathname.startsWith('/app/admin/reports'),
									// subItems: [
									// 	{
									// 		label: 'Faturamento',
									// 		href: '/app/admin/reports',
									// 		isActive: pathname === '/app/admin/reports',
									// 		icon: BarChartIcon,
									// 	},
									// ],
								},
								{
									label: 'Reconciliação',
									href: '/app/admin/balance-reconciliation',
									icon: CalculatorIcon,
									isActive: pathname.startsWith(
										'/app/admin/balance-reconciliation'
									),
								},
								{
									label: 'Controle de Saques',
									href: '/app/admin/withdrawal-control',
									icon: Wallet2Icon,
									isActive: pathname.startsWith(
										'/app/admin/withdrawal-control'
									),
								},
								
							]
						: []),
					{
						label: 'Conta',
						href: '/app/settings',
						icon: Settings,
						isActive: pathname.startsWith('/app/settings'),
					},
				]),
	];

	// Don't render if session isn't loaded yet
	if (!sessionLoaded) {
		return null;
	}

	return (
		<nav
			className={cn('w-full border-b', {
				'w-full md:fixed md:top-0 md:left-0 md:h-full md:w-[280px] md:border-r border-gray-100 dark:border-gray-900 md:border-b-0 bg-background/80 dark:bg-background/80 text-foreground dark:text-foreground':
					useSidebarLayout,
			})}
		>
			<div
				className={cn('container max-w-6xl py-4', {
					'container max-w-6xl py-4 md:flex md:h-full md:flex-col md:px-6 md:pt-6 md:pb-0':
						useSidebarLayout,
				})}
			>
				<div className='flex flex-wrap items-center justify-between gap-4 mb-6'>
					<div
						className={cn('flex items-center gap-4 md:gap-2', {
							'md:flex md:w-full md:flex-col md:items-stretch md:align-stretch':
								useSidebarLayout,
						})}
					>
						<Link href='/app' className='block'>
							<Logo />
						</Link>
					</div>

					<div
						className={cn('mr-0 ml-auto flex items-center justify-end gap-4', {
							'md:hidden': useSidebarLayout,
						})}
					>
						{/* UserMenu removed from here */}
					</div>
				</div>

				<ul
					className={cn(
						'no-scrollbar -mx-4 -mb-4 mt-6 flex list-none items-center justify-start gap-4 overflow-x-auto px-4 text-sm',
						{
							'md:mx-0 md:mt-0 md:flex md:flex-col md:items-stretch md:gap-3 md:px-0':
								useSidebarLayout,
						}
					)}
				>
					{menuItems.map((menuItem) => (
						<li key={menuItem.href} className='w-full'>
							{menuItem.subItems ? (
								<div className='w-full'>
									<button
										onClick={() => toggleSubmenu(menuItem.href)}
										className={cn(
											'flex w-full items-center gap-3 whitespace-nowrap rounded-md px-4 py-3',
											[
												menuItem.isActive
													? 'bg-primary/10 font-medium text-primary'
													: 'text-foreground/70 dark:text-foreground/70 hover:bg-primary/10 hover:text-primary',
											]
										)}
									>
										<menuItem.icon
											className={`size-6 shrink-0 ${
												menuItem.isActive ? 'text-primary' : 'opacity-70'
											}`}
										/>
										<span className='flex-1 text-left'>{menuItem.label}</span>
										<ChevronDownIcon
											className={cn(
												'size-4 transition-transform duration-200',
												openMenus[menuItem.href] ? 'rotate-180' : ''
											)}
										/>
									</button>

									{openMenus[menuItem.href] && (
										<ul className='ml-4 mt-2 space-y-2'>
											{menuItem.subItems.map((subItem) => (
												<li key={subItem.href}>
													<Link
														href={subItem.href}
														className={cn(
															'flex items-center rounded-md px-4 py-2.5 text-sm',
															[
																subItem.isActive
																	? 'bg-primary/10 font-medium text-primary'
																	: 'text-foreground/70 dark:text-foreground/70 hover:bg-primary/10 hover:text-primary',
															]
														)}
													>
														{subItem.icon && (
															<subItem.icon className='size-4 mr-2 opacity-50' />
														)}
														{subItem.label}
													</Link>
												</li>
											))}
										</ul>
									)}
								</div>
							) : (
								<Link
									href={menuItem.href}
									className={cn(
										'flex items-center gap-3 whitespace-nowrap rounded-md px-4 py-3',
										[
											menuItem.isActive
												? 'bg-primary/10 font-medium text-primary'
												: 'text-foreground/70 dark:text-foreground/70 hover:bg-primary/10 hover:text-primary',
										]
									)}
								>
									<menuItem.icon
										className={`size-6 shrink-0 ${
											menuItem.isActive ? 'text-primary' : 'opacity-70'
										}`}
									/>
									<span>{menuItem.label}</span>
								</Link>
							)}
						</li>
					))}
				</ul>

				<div
					className={cn('-mx-4 md:-mx-6 mt-auto mb-0 hidden p-4 md:p-4', {
						'md:block': useSidebarLayout,
					})}
				>
					{config.organizations.enable &&
						!config.organizations.hideOrganization && (
							<div className='mb-3'>
								<OrganzationSelect
									className={cn({
										'': useSidebarLayout,
									})}
								/>
							</div>
						)}
				</div>
			</div>
		</nav>
	);
}
