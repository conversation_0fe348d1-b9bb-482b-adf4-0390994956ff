import { apiClient } from "@shared/lib/api-client";
import { useMutation } from "@tanstack/react-query";
import type { InferRequestType } from "hono";

export const signedUploadUrlMutationKey = ["signed-upload-url"];
export const useSignedUploadUrlMutation = () => {
	return useMutation({
		mutationKey: signedUploadUrlMutationKey,
		mutationFn: async (
			query: { path: string; bucket: string; contentType?: string }
		) => {
			console.log("=== UPLOAD URL MUTATION ===");
			console.log("Request params:", JSON.stringify(query));
			console.log("Endpoint URL:", '/api/uploads/signed-upload-url');

			try {
				console.log("Request body:", JSON.stringify({ query }));

				const response = await fetch('/api/uploads/signed-upload-url', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify({ query }),
				});

				console.log("Response status:", response.status);
				console.log("Response status text:", response.statusText);

				if (!response.ok) {
					console.error("Failed to get signed upload URL, status:", response.status);
					const errorText = await response.text().catch(() => "No error details");
					console.error("Error details:", errorText);
					throw new Error("Failed to get signed upload url");
				}

				const data = await response.json();
				console.log("Signed URL obtained successfully");
				return data;
			} catch (error) {
				console.error("Exception in signed URL mutation:", error);
				throw error;
			}
		},
	});
};
