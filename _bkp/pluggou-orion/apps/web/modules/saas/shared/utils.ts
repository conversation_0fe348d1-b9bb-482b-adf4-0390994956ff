/**
 * Calculates date ranges for different time periods.
 * @param period - "day", "week", "month", or "year"
 * @returns Object with start and end dates for the current and previous periods
 */
export function getPeriodDates(
  period: "day" | "week" | "month" | "year" = "month"
): {
  startDate: Date;
  endDate: Date;
  previousStartDate: Date;
  previousEndDate: Date;
} {
  const now = new Date();
  const endDate = new Date(now);
  let startDate = new Date(now);
  let previousStartDate = new Date(now);
  let previousEndDate = new Date(now);

  switch (period) {
    case "day":
      // Current day: today from 00:00:00 to now
      startDate.setHours(0, 0, 0, 0);
      // Previous day: yesterday from 00:00:00 to 23:59:59
      previousEndDate = new Date(startDate);
      previousEndDate.setMilliseconds(-1);
      previousStartDate = new Date(previousEndDate);
      previousStartDate.setHours(0, 0, 0, 0);
      break;

    case "week":
      // Current week: from the beginning of the week to now
      const dayOfWeek = startDate.getDay() || 7; // Convert Sunday (0) to 7
      startDate.setDate(startDate.getDate() - dayOfWeek + 1); // Move to Monday
      startDate.setHours(0, 0, 0, 0);
      // Previous week: from beginning of previous week to end of previous week
      previousEndDate = new Date(startDate);
      previousEndDate.setMilliseconds(-1);
      previousStartDate = new Date(previousEndDate);
      previousStartDate.setDate(previousStartDate.getDate() - 6);
      previousStartDate.setHours(0, 0, 0, 0);
      break;

    case "year":
      // Current year: from the beginning of the year to now
      startDate = new Date(now.getFullYear(), 0, 1);
      // Previous year: from beginning of previous year to end of previous year
      previousEndDate = new Date(now.getFullYear(), 0, 1);
      previousEndDate.setMilliseconds(-1);
      previousStartDate = new Date(now.getFullYear() - 1, 0, 1);
      break;

    case "month":
    default:
      // Current month: from the beginning of the month to now
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      // Previous month: from beginning of previous month to end of previous month
      previousEndDate = new Date(startDate);
      previousEndDate.setMilliseconds(-1);
      previousStartDate = new Date(
        previousEndDate.getFullYear(),
        previousEndDate.getMonth(),
        1
      );
      break;
  }

  return {
    startDate,
    endDate,
    previousStartDate,
    previousEndDate,
  };
}



export function gerarCPF() {
  // Gera 9 números aleatórios
  let cpf = Array.from({length: 9}, () => Math.floor(Math.random() * 10));

  // Calcula o primeiro dígito verificador
  let soma = 0;
  for (let i = 0; i < 9; i++) {
      soma += cpf[i] * (10 - i);
  }
  let resto = soma % 11;
  cpf[9] = resto < 2 ? 0 : 11 - resto;

  // Calcula o segundo dígito verificador
  soma = 0;
  for (let i = 0; i < 10; i++) {
      soma += cpf[i] * (11 - i);
  }
  resto = soma % 11;
  cpf[10] = resto < 2 ? 0 : 11 - resto;

  // Formata o CPF como string
  return cpf.join('')
      .replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}
