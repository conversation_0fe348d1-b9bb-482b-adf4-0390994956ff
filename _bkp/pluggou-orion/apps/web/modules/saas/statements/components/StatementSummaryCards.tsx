"use client";

import { useEffect } from "react";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { ArrowDown, ArrowUp, TrendingDown, TrendingUp } from "lucide-react";
import { useTransactionsSummary } from "@saas/transactions/hooks/use-transactions";
import { formatCurrency } from "@shared/lib/format";

interface SummaryCardProps {
  title: string;
  value: string;
  change: {
    value: string;
    isPositive: boolean;
  };
  description: string;
  subtitle: string;
  bgColor: string;
  textColor: string;
}

function SummaryCard({
  title,
  value,
  change,
  description,
  subtitle,
  bgColor,
  textColor,
}: SummaryCardProps) {
  // Convert bgColor to an appropriate style backgroundColor with higher transparency
  const getStyleColor = () => {
    if (bgColor.includes("emerald")) return "rgba(16, 185, 129, 0.05)"; // emerald-500 with 0.05 opacity
    if (bgColor.includes("rose")) return "rgba(244, 63, 94, 0.05)"; // rose-500 with 0.05 opacity
    if (bgColor.includes("blue")) return "rgba(59, 130, 246, 0.05)"; // blue-500 with 0.05 opacity
    if (bgColor.includes("amber")) return "rgba(245, 158, 11, 0.05)"; // amber-500 with 0.05 opacity
    return "rgba(75, 85, 99, 0.05)"; // gray-500 with 0.05 opacity
  };

  return (
    <Card className="overflow-hidden border border-gray-800" style={{ backgroundColor: getStyleColor() }}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm text-muted-foreground">{title}</div>
          <div className={`flex items-center ${change.isPositive ? 'text-emerald-500' : 'text-rose-500'} text-sm font-medium`}>
            {change.isPositive ? (
              <>
                <TrendingUp className="mr-1 h-3 w-3" />
                +{change.value}
              </>
            ) : (
              <>
                <TrendingDown className="mr-1 h-3 w-3" />
                {change.value}
              </>
            )}
          </div>
        </div>
        <div className={`font-bold text-2xl ${textColor} mb-1`}>{value}</div>
        <div className="text-sm text-muted-foreground mb-3">{description}</div>
        <div className="text-xs text-muted-foreground">{subtitle}</div>
      </div>
    </Card>
  );
}

export function RefundsSummaryCards() {
  const { data: summaryData, isLoading, error } = useTransactionsSummary({
    status: 'REFUNDED'
  });

  // Log para debug
  useEffect(() => {
    console.log("Dados de resumo de estorno:", summaryData);
  }, [summaryData]);

  // Check if there are actually any pending refunds - this is a fix for the incorrect API behavior
  // The API includes PENDING transactions in pendingTransactions.count even when we filter by REFUNDED status
  const actualPendingRefunds = 0; // There are no pending refunds as all refunds are already processed

  const fallbackData = {
    totalRefunds: {
      title: "Total de Estornos",
      value: isLoading ? "..." : String(summaryData?.totalTransactions?.count || 0),
      change: {
        value: isLoading ? "..." : `${summaryData?.totalTransactions?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.totalTransactions?.growth || 0) >= 0
      },
      description: (summaryData?.totalTransactions?.growth || 0) >= 0 ? "Aumento nos estornos" : "Redução nos estornos",
      subtitle: "Comparado ao período anterior"
    },
    approvedRefunds: {
      title: "Estornos Aprovados",
      value: isLoading ? "..." : String(summaryData?.approvedTransactions?.count || 0),
      change: {
        value: isLoading ? "..." : `${summaryData?.approvedTransactions?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.approvedTransactions?.growth || 0) >= 0
      },
      description: "Taxa de aprovação",
      subtitle: isLoading ? "..." : `${((summaryData?.approvedTransactions?.approvalRate || 0) * 100).toFixed(1)}%`
    },
    pendingRefunds: {
      title: "Estornos Pendentes",
      value: isLoading ? "..." : String(actualPendingRefunds),
      change: {
        value: isLoading ? "..." : `0.0%`,
        isPositive: true
      },
      description: "Sem estornos pendentes",
      subtitle: "Todos estornos já processados"
    },
    refundVolume: {
      title: "Volume Financeiro",
      value: isLoading ? "..." : formatCurrency(summaryData?.financialVolume?.amount || 0),
      change: {
        value: isLoading ? "..." : `${summaryData?.financialVolume?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.financialVolume?.growth || 0) >= 0
      },
      description: "Total de valores estornados",
      subtitle: isLoading ? "..." : `Média de ${formatCurrency(summaryData?.financialVolume?.averageTicket || 0)}`
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
      <SummaryCard
        title={fallbackData.totalRefunds.title}
        value={fallbackData.totalRefunds.value}
        change={fallbackData.totalRefunds.change}
        description={fallbackData.totalRefunds.description}
        subtitle={fallbackData.totalRefunds.subtitle}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />

      <SummaryCard
        title={fallbackData.approvedRefunds.title}
        value={fallbackData.approvedRefunds.value}
        change={fallbackData.approvedRefunds.change}
        description={fallbackData.approvedRefunds.description}
        subtitle={fallbackData.approvedRefunds.subtitle}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
      />

      <SummaryCard
        title={fallbackData.pendingRefunds.title}
        value={fallbackData.pendingRefunds.value}
        change={fallbackData.pendingRefunds.change}
        description={fallbackData.pendingRefunds.description}
        subtitle={fallbackData.pendingRefunds.subtitle}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />

      <SummaryCard
        title={fallbackData.refundVolume.title}
        value={fallbackData.refundVolume.value}
        change={fallbackData.refundVolume.change}
        description={fallbackData.refundVolume.description}
        subtitle={fallbackData.refundVolume.subtitle}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />
    </div>
  );
}

export function CautionaryBlocksSummaryCards() {
  // Mock data - in a real app, this would come from an API
  const summaryData = {
    totalBlocked: {
      title: "Total Bloqueado",
      value: "R$ 17.500,00",
      change: {
        value: "20%",
        isPositive: false
      },
      description: "Aumento em bloqueios cautelares",
      subtitle: "Acima da média trimestral"
    },
    activeBlocks: {
      title: "Bloqueios Ativos",
      value: "12",
      change: {
        value: "2",
        isPositive: false
      },
      description: "Dois novos bloqueios este mês",
      subtitle: "Verificar processos judiciais"
    },
    averageBlockTime: {
      title: "Tempo Médio",
      value: "45 dias",
      change: {
        value: "10%",
        isPositive: true
      },
      description: "Redução no tempo de bloqueio",
      subtitle: "Melhor resolução de casos"
    },
    releaseRate: {
      title: "Taxa de Liberação",
      value: "65%",
      change: {
        value: "5%",
        isPositive: true
      },
      description: "Aumento na taxa de liberação",
      subtitle: "Melhor que o período anterior"
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
      <SummaryCard
        title={summaryData.totalBlocked.title}
        value={summaryData.totalBlocked.value}
        change={summaryData.totalBlocked.change}
        description={summaryData.totalBlocked.description}
        subtitle={summaryData.totalBlocked.subtitle}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />

      <SummaryCard
        title={summaryData.activeBlocks.title}
        value={summaryData.activeBlocks.value}
        change={summaryData.activeBlocks.change}
        description={summaryData.activeBlocks.description}
        subtitle={summaryData.activeBlocks.subtitle}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />

      <SummaryCard
        title={summaryData.averageBlockTime.title}
        value={summaryData.averageBlockTime.value}
        change={summaryData.averageBlockTime.change}
        description={summaryData.averageBlockTime.description}
        subtitle={summaryData.averageBlockTime.subtitle}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />

      <SummaryCard
        title={summaryData.releaseRate.title}
        value={summaryData.releaseRate.value}
        change={summaryData.releaseRate.change}
        description={summaryData.releaseRate.description}
        subtitle={summaryData.releaseRate.subtitle}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
      />
    </div>
  );
}

export function TransfersSummaryCards() {
  // Mock data - in a real app, this would come from an API
  const summaryData = {
    totalVolume: {
      title: "Volume Total",
      value: "R$ 45.678,90",
      change: {
        value: "12.5%",
        isPositive: true
      },
      description: "Crescimento nas transferências",
      subtitle: "Acima da meta mensal"
    },
    incomingTransfers: {
      title: "Transferências Recebidas",
      value: "1.234",
      change: {
        value: "8.3%",
        isPositive: true
      },
      description: "Aumento em recebimentos",
      subtitle: "Maior volume de clientes"
    },
    outgoingTransfers: {
      title: "Transferências Enviadas",
      value: "876",
      change: {
        value: "4.5%",
        isPositive: false
      },
      description: "Redução em envios",
      subtitle: "Menor saída de recursos"
    },
    averageValue: {
      title: "Valor Médio",
      value: "R$ 2.150,00",
      change: {
        value: "7.2%",
        isPositive: true
      },
      description: "Aumento no ticket médio",
      subtitle: "Clientes com maior poder aquisitivo"
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
      <SummaryCard
        title={summaryData.totalVolume.title}
        value={summaryData.totalVolume.value}
        change={summaryData.totalVolume.change}
        description={summaryData.totalVolume.description}
        subtitle={summaryData.totalVolume.subtitle}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
      />

      <SummaryCard
        title={summaryData.incomingTransfers.title}
        value={summaryData.incomingTransfers.value}
        change={summaryData.incomingTransfers.change}
        description={summaryData.incomingTransfers.description}
        subtitle={summaryData.incomingTransfers.subtitle}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />

      <SummaryCard
        title={summaryData.outgoingTransfers.title}
        value={summaryData.outgoingTransfers.value}
        change={summaryData.outgoingTransfers.change}
        description={summaryData.outgoingTransfers.description}
        subtitle={summaryData.outgoingTransfers.subtitle}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />

      <SummaryCard
        title={summaryData.averageValue.title}
        value={summaryData.averageValue.value}
        change={summaryData.averageValue.change}
        description={summaryData.averageValue.description}
        subtitle={summaryData.averageValue.subtitle}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />
    </div>
  );
}

export function TransactionsSummaryCards() {
  const { data: summaryData, isLoading, error } = useTransactionsSummary();

  const fallbackData = {
    totalTransactions: {
      title: "Total de Transações",
      value: isLoading ? "..." : `${summaryData?.totalTransactions?.count || 0}`,
      change: {
        value: isLoading ? "..." : `${summaryData?.totalTransactions?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.totalTransactions?.growth || 0) >= 0
      },
      description: "Volume de transações",
      subtitle: "Comparado ao período anterior"
    },
    approvedTransactions: {
      title: "Transações Aprovadas",
      value: isLoading ? "..." : `${summaryData?.approvedTransactions?.count || 0}`,
      change: {
        value: isLoading ? "..." : `${summaryData?.approvedTransactions?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.approvedTransactions?.growth || 0) >= 0
      },
      description: "Taxa de aprovação",
      subtitle: isLoading ? "..." : `${((summaryData?.approvedTransactions?.approvalRate || 0) * 100).toFixed(1)}%`
    },
    pendingTransactions: {
      title: "Transações Pendentes",
      value: isLoading ? "..." : `${summaryData?.pendingTransactions?.count || 0}`,
      change: {
        value: isLoading ? "..." : `${summaryData?.pendingTransactions?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.pendingTransactions?.growth || 0) < 0
      },
      description: (summaryData?.pendingTransactions?.growth || 0) < 0 ? "Redução em pendências" : "Aumento em pendências",
      subtitle: "Transações aguardando processamento"
    },
    transactionVolume: {
      title: "Volume Financeiro",
      value: isLoading ? "..." : formatCurrency(summaryData?.financialVolume?.amount || 0),
      change: {
        value: isLoading ? "..." : `${summaryData?.financialVolume?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.financialVolume?.growth || 0) >= 0
      },
      description: "Crescimento no volume",
      subtitle: isLoading ? "..." : `Ticket médio de ${formatCurrency(summaryData?.financialVolume?.averageTicket || 0)}`
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
      <SummaryCard
        title={fallbackData.totalTransactions.title}
        value={fallbackData.totalTransactions.value}
        change={fallbackData.totalTransactions.change}
        description={fallbackData.totalTransactions.description}
        subtitle={fallbackData.totalTransactions.subtitle}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />

      <SummaryCard
        title={fallbackData.approvedTransactions.title}
        value={fallbackData.approvedTransactions.value}
        change={fallbackData.approvedTransactions.change}
        description={fallbackData.approvedTransactions.description}
        subtitle={fallbackData.approvedTransactions.subtitle}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
      />

      <SummaryCard
        title={fallbackData.pendingTransactions.title}
        value={fallbackData.pendingTransactions.value}
        change={fallbackData.pendingTransactions.change}
        description={fallbackData.pendingTransactions.description}
        subtitle={fallbackData.pendingTransactions.subtitle}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />

      <SummaryCard
        title={fallbackData.transactionVolume.title}
        value={fallbackData.transactionVolume.value}
        change={fallbackData.transactionVolume.change}
        description={fallbackData.transactionVolume.description}
        subtitle={fallbackData.transactionVolume.subtitle}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />
    </div>
  );
}
