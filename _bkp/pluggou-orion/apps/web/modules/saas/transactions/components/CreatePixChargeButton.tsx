"use client";

import { useState } from "react";
import { Button } from "@ui/components/button";
// import { useTranslations } from "next-intl";
import { PixChargeModal } from "./PixChargeModal";
import { QrCode, Loader2 } from "lucide-react";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useToast } from "@ui/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";

export function CreatePixChargeButton() {
  // const t = useTranslations();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { activeOrganization, loaded: organizationLoaded } = useActiveOrganization();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleClick = () => {
    if (!organizationLoaded) {
      toast({
        title: "Carregando",
        description: "Aguarde enquanto carregamos os dados da organização...",
        variant: "default"
      });
      return;
    }

    if (!activeOrganization?.id) {
      toast({
        title: "Erro",
        description: "Nenhuma organização selecionada. Por favor, selecione uma organização primeiro.",
        variant: "error"
      });
      return;
    }

    setIsModalOpen(true);
  };

  return (
    <>
      <Button
        onClick={handleClick}
        className="bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-11 rounded-lg shadow-sm"
        disabled={!organizationLoaded}
      >
        {!organizationLoaded ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <QrCode className="h-4 w-4" />
        )}
        Criar Cobrança PIX
      </Button>

      <PixChargeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={(data) => {
          // Não recarregamos a página imediatamente para permitir que o usuário veja o QR code
          console.log("Transação criada com sucesso:", data);
          // A página será recarregada quando o usuário fechar o modal

          if (activeOrganization?.id) {
            // Invalidate queries related to transactions for the active organization
            queryClient.invalidateQueries({
              queryKey: [
                "organization",
                "transactions",
                activeOrganization.id,
              ]
            });
            // Also invalidate the potentially old "purchases" query key if it's still in use elsewhere
            queryClient.invalidateQueries({
              queryKey: [
                "organization",
                "purchases",
                activeOrganization.id,
              ]
            });
          }
        }}
      />
    </>
  );
}
