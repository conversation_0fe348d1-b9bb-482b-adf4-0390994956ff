"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { useTranslations } from "next-intl";
import { CreateTransactionModal } from "./CreateTransactionModal";
import { Plus } from "lucide-react";

export function CreateTransactionButton() {
  const t = useTranslations();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        className="bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-11 rounded-lg shadow-sm"
      >
        <Plus className="h-4 w-4" />
        Criar transação
      </Button>

      <CreateTransactionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}
