"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Info, Loader2, QrCode, Copy, Check } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@ui/components/form";
import Image from "next/image";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Alert, AlertDescription } from "@ui/components/alert";
import { useToast } from "@ui/hooks/use-toast";
import { apiClient } from "@shared/lib/api-client";

interface CreateTransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
}

// Esquema de validação do formulário
const formSchema = z.object({
  amount: z.string().min(1, "Valor é obrigatório"),
  description: z.string().min(1, "Nome do produto ou serviço é obrigatório"),
});

export function CreateTransactionModal({ isOpen, onClose, onSuccess }: CreateTransactionModalProps) {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const [transactionCreated, setTransactionCreated] = useState(false);
  const [pixData, setPixData] = useState<{
    pixQrCode?: string;
    pixPayload?: string;
    id?: string;
    gatewayType?: string;
  }>({});
  const [isCopied, setIsCopied] = useState(false);
  const [defaultGateway, setDefaultGateway] = useState<{id: string; name: string; type: string} | null>(null);
  const [isLoadingGateways, setIsLoadingGateways] = useState(false);
  const { toast } = useToast();
  const { activeOrganization } = useActiveOrganization();

  // Carregar gateway padrão quando o modal é aberto
  useEffect(() => {
    if (isOpen && activeOrganization?.id) {
      loadDefaultGateway();
    }
  }, [isOpen, activeOrganization?.id]);

  // Função para configurar um gateway padrão se não existir
  const setupDefaultGateway = async () => {
    if (!activeOrganization?.id) return;

    setIsLoadingGateways(true);
    try {
      const response = await fetch('/api/payments/gateways/setup-default', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId: activeOrganization.id,
          type: 'ASAAS', // Usar Asaas como padrão
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao configurar gateway de pagamento');
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Gateway configurado',
          description: 'Gateway de pagamento configurado com sucesso',
          variant: 'success'
        });

        // Recarregar o gateway
        await loadDefaultGateway();
      }
    } catch (error) {
      console.error('Erro ao configurar gateway:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível configurar o gateway de pagamento',
        variant: 'error'
      });
    } finally {
      setIsLoadingGateways(false);
    }
  };

  // Função para carregar o gateway padrão
  const loadDefaultGateway = async () => {
    if (!activeOrganization?.id) return;

    setIsLoadingGateways(true);
    try {
      const response = await fetch(`/api/payments/gateways/list?organizationId=${activeOrganization.id}`);

      if (!response.ok) {
        throw new Error('Erro ao carregar gateways de pagamento');
      }

      const data = await response.json();

      if (data.success && data.data.length > 0) {
        // Encontrar o gateway padrão
        const defaultGateway = data.data.find((g: any) => g.isDefault);
        if (defaultGateway) {
          setDefaultGateway(defaultGateway);
        } else if (data.data.length > 0) {
          // Se não houver padrão, usar o primeiro ativo
          const activeGateway = data.data.find((g: any) => g.isActive);
          if (activeGateway) {
            setDefaultGateway(activeGateway);
          }
        } else {
          // Se não há gateways configurados, configurar um padrão
          await setupDefaultGateway();
        }
      } else {
        // Se não há gateways configurados, configurar um padrão
        await setupDefaultGateway();
      }
    } catch (error) {
      console.error('Erro ao carregar gateway padrão:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar o gateway de pagamento padrão',
        variant: 'error'
      });

      // Tentar configurar um gateway padrão
      await setupDefaultGateway();
    } finally {
      setIsLoadingGateways(false);
    }
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: "",
      description: ""
    }
  });

  // Formatar valor como moeda
  const formatCurrency = (value: string) => {
    // Remove todos os caracteres não numéricos
    let numericValue = value.replace(/\D/g, "");

    // Converte para número e divide por 100 para obter o valor em reais
    const floatValue = parseInt(numericValue || "0") / 100;

    // Formata como moeda brasileira
    return floatValue.toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
    });
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/\D/g, "");
    const formattedValue = formatCurrency(rawValue);
    form.setValue("amount", formattedValue);
  };

  // Função para copiar o código PIX para a área de transferência
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
    toast({
      title: "Código PIX copiado",
      description: "O código PIX foi copiado para a área de transferência",
      variant: "success"
    });
  };

  // Função para fechar o modal e resetar o estado
  const handleClose = () => {
    if (!transactionCreated) {
      onClose();
      return;
    }

    // Se a transação foi criada, perguntar se deseja fechar
    if (confirm("Tem certeza que deseja fechar? O QR Code não estará mais disponível.")) {
      setTransactionCreated(false);
      setPixData({});
      form.reset();
      onClose();
    }
  };

  // Função para converter valor formatado em número (mesma lógica do PixTransferModal)
  const parseAmountValue = (formattedAmount: string): number => {
    if (!formattedAmount) return 0;

    // Para valores formatados pelo IMaskInput com formato brasileiro:
    // - Remover espaços e caracteres especiais exceto dígitos, vírgulas e pontos
    // - Se há vírgula, ela é o separador decimal
    // - Pontos antes da vírgula são separadores de milhares
    let cleanValue = formattedAmount.replace(/[^\d.,]/g, '');

    // Se há vírgula, tratar como separador decimal brasileiro
    if (cleanValue.includes(',')) {
      // Dividir em parte inteira e decimal
      const parts = cleanValue.split(',');
      if (parts.length === 2) {
        // Remover pontos da parte inteira (separadores de milhares)
        const integerPart = parts[0].replace(/\./g, '');
        const decimalPart = parts[1];
        cleanValue = `${integerPart}.${decimalPart}`;
      }
    } else {
      // Se não há vírgula, remover pontos (podem ser separadores de milhares)
      // Mas manter se for o último ponto com 2 dígitos após (separador decimal)
      const lastDotIndex = cleanValue.lastIndexOf('.');
      if (lastDotIndex !== -1 && cleanValue.length - lastDotIndex === 3) {
        // Último ponto com 2 dígitos após - provavelmente separador decimal
        const beforeDot = cleanValue.substring(0, lastDotIndex).replace(/\./g, '');
        const afterDot = cleanValue.substring(lastDotIndex + 1);
        cleanValue = `${beforeDot}.${afterDot}`;
      } else {
        // Remover todos os pontos (separadores de milhares)
        cleanValue = cleanValue.replace(/\./g, '');
      }
    }

    const result = parseFloat(cleanValue);
    return isNaN(result) ? 0 : result;
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!activeOrganization?.id) {
      toast({
        title: "Erro",
        description: "Organização não encontrada",
        variant: "error"
      });
      return;
    }

    if (!defaultGateway) {
      toast({
        title: "Erro",
        description: "Nenhum gateway de pagamento disponível",
        variant: "error"
      });
      return;
    }

    setIsLoading(true);

    try {
      // Formatar os valores para envio usando a função corrigida
      const numericAmount = parseAmountValue(values.amount);

      // Chamar a API para processar a criação da transação
      const response = await apiClient.payments.transactions.$post({
        json: {
          amount: numericAmount,
          description: values.description,
          customerName: "Cliente",
          customerEmail: "<EMAIL>",
          customerPhone: "",
          organizationId: activeOrganization.id
        }
      });

      if (!response.ok) {
        throw new Error("Failed to create transaction");
      }

      const data = await response.json();

      // Log dos dados recebidos para debug
      console.log('Dados da transação recebidos:', {
        id: data.id,
        gatewayType: data.gatewayType,
        pixPayloadLength: data.pixPayload?.length,
        pixQrCodeLength: data.pixQrCode?.length,
        pixQrCodePrefix: data.pixQrCode?.substring(0, 30) + '...',
      });

      // Armazenar os dados do QR Code
      setPixData({
        pixQrCode: data.pixQrCode,
        pixPayload: data.pixPayload,
        id: data.id,
        gatewayType: data.gatewayType
      });

      setTransactionCreated(true);

      toast({
        title: "Transação criada com sucesso",
        description: "O QR Code foi gerado com sucesso",
        variant: "success"
      });

      if (onSuccess) {
        onSuccess(data);
      }
    } catch (error) {
      console.error("Erro ao criar transação:", error);
      toast({
        title: "Erro ao criar transação",
        description: error instanceof Error ? error.message : "Ocorreu um erro ao processar sua solicitação",
        variant: "error"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[700px] p-0 overflow-hidden">
        <div className="bg-background px-6 pt-6">
          <DialogHeader className="mb-0">
            <DialogTitle className="text-center text-2xl font-bold flex items-center justify-center gap-2">
              <QrCode className="h-6 w-6 text-primary" />
              {transactionCreated ? "QR Code PIX" : "Criar transação"}
            </DialogTitle>
            <p className="text-center text-sm text-muted-foreground">
              {transactionCreated ? "Escaneie o QR Code para pagar" : "Crie uma cobrança para seus clientes"}
            </p>
          </DialogHeader>
        </div>
        <div className="p-6 bg-card">
          {!transactionCreated ? (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          placeholder="R$ 0,00"
                          {...field}
                          onChange={handleAmountChange}
                          className="h-12 text-lg"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          placeholder="Digite o nome do produto ou serviço"
                          {...field}
                          className="h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-center">
                  <div className="flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-md w-full justify-center">
                    <QrCode className="h-4 w-4" />
                    Pagamento via PIX
                  </div>
                </div>

                {/* {!defaultGateway && (
                  <div className="space-y-4">
                    <Alert variant="default" className="bg-amber-500/10 text-amber-500 border-amber-500/20">
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        Nenhum gateway de pagamento configurado. Clique no botão abaixo para configurar um gateway padrão.
                      </AlertDescription>
                    </Alert>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={setupDefaultGateway}
                      disabled={isLoadingGateways}
                    >
                      {isLoadingGateways ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Configurando...
                        </>
                      ) : (
                        "Configurar Gateway Padrão"
                      )}
                    </Button>
                  </div>
                )} */}

                {defaultGateway && (
                  <div className="flex items-center justify-between px-4 py-2 bg-muted/50 rounded-md">
                    <span className="text-sm">Gateway de pagamento:</span>
                    <span className="text-sm font-medium">{defaultGateway.name}</span>
                  </div>
                )}

                <Alert variant="default" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Após criar a transação, será mostrado o QR Code para pagamento.
                  </AlertDescription>
                </Alert>

                <DialogFooter className="flex flex-row gap-3 sm:justify-between pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="flex-1 h-12 rounded-lg"
                    disabled={isLoading}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-12 rounded-lg shadow-sm"
                    disabled={isLoading || !defaultGateway}
                    title={!defaultGateway ? "Configure um gateway de pagamento primeiro" : ""}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <QrCode className="h-4 w-4" />
                    )}
                    {isLoading ? "Processando..." : "Criar"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          ) : (
            <div className="space-y-6">
              {pixData.pixQrCode && (
                <div className="flex flex-col items-center justify-center">
                  <div className="bg-white p-4 rounded-lg mb-4">
                    <Image
                      src={pixData.pixQrCode.startsWith('data:') ? pixData.pixQrCode : `data:image/png;base64,${pixData.pixQrCode}`}
                      alt="QR Code PIX"
                      width={200}
                      height={200}
                    />
                  </div>

                  {pixData.pixPayload && (
                    <div className="w-full">
                      <p className="text-sm text-muted-foreground mb-2 text-center">Código PIX copia e cola:</p>
                      <div className="relative">
                        <div className="p-3 bg-gray-800/30 rounded-lg text-xs break-all overflow-hidden">
                          {pixData.pixPayload}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                          onClick={() => copyToClipboard(pixData.pixPayload || "")}
                        >
                          {isCopied ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  )}

                  {pixData.gatewayType && (
                    <div className="mt-2 text-xs text-muted-foreground text-center">
                      Processado via {pixData.gatewayType}
                    </div>
                  )}
                </div>
              )}

              <Alert variant="default" className="bg-emerald-500/10 text-emerald-500 border-emerald-500/20">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Transação criada com sucesso! O pagamento será confirmado automaticamente após o cliente realizar o pagamento.
                </AlertDescription>
              </Alert>

              <DialogFooter className="flex flex-row gap-3 sm:justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1 h-12 rounded-lg"
                >
                  Fechar
                </Button>
                {pixData.pixPayload && (
                  <Button
                    type="button"
                    className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-12 rounded-lg shadow-sm"
                    onClick={() => copyToClipboard(pixData.pixPayload || "")}
                  >
                    <Copy className="h-4 w-4" />
                    Copiar código PIX
                  </Button>
                )}
              </DialogFooter>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
