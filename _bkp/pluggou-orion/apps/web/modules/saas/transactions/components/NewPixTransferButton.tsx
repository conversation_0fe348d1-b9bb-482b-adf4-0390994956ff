"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { useTranslations } from "next-intl";
import { PixTransferModal } from "./PixTransferModal";
import { Zap } from "lucide-react";

export function NewPixTransferButton() {
  const t = useTranslations();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>

     <Button
            onClick={() => setIsModalOpen(true)}
            className="bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-11 rounded-lg shadow-sm"
          >
            <Zap className="h-4 w-4" />
            Fazer Transferência Pix
          </Button>

      {/* <Button
        onClick={() => setIsModalOpen(true)}
        className="bg-[#4caf50] border-none hover:bg-[#43a047] text-white shadow-sm transition-all hover:shadow-md flex items-center gap-2 relative overflow-hidden group rounded-lg h-10"
      >
        <div className="bg-white/20 rounded-full p-1 relative z-10">
          <Zap className="h-3.5 w-3.5 text-white" />
        </div>
        <span className="relative z-10 font-medium">{t("transactions.newPixTransfer") || "Nova Transferência PIX"}</span>
      </Button> */}

      <PixTransferModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}
