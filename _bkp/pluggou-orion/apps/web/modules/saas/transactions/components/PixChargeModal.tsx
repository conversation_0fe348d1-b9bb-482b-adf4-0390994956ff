'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { useTranslations } from 'next-intl';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from '@ui/components/dialog';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Alert, AlertDescription } from '@ui/components/alert';
import {
	Info,
	Wallet,
	Loader2,
	QrCode,
	Check,
	Copy,
	QrCodeIcon,
	RefreshCw,
} from 'lucide-react';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import { cn } from '@ui/lib';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@ui/components/form';
import { useActiveOrganization } from '@saas/organizations/hooks/use-active-organization';
import { IMaskInput } from 'react-imask';
import { useToast } from '@ui/hooks/use-toast';
import { QRCodeSVG } from 'qrcode.react';
import { formatCurrency } from '@shared/lib/format';
import { parseAmountValue } from '@repo/utils/src/financial/amount-parser';

type PixChargeModalProps = {
	isOpen: boolean;
	onClose: () => void;
	onSuccess?: (data: any) => void;
};

type Gateway = {
	id: string;
	name: string;
	type: string;
	isDefault: boolean;
	isActive: boolean;
	canReceive: boolean;
	canSend: boolean;
};

export function PixChargeModal({
	isOpen,
	onClose,
	onSuccess,
}: PixChargeModalProps) {
	// Usar o hook de traduções
	const translations = useTranslations();
	// Função de tradução com fallback para evitar erros

	const [isLoading, setIsLoading] = useState(false);
	const [defaultGateway, setDefaultGateway] = useState<Gateway | null>(null);
	const [isLoadingGateways, setIsLoadingGateways] = useState(false);
	const [transactionCreated, setTransactionCreated] = useState(false);
	const [isLoadingQrCode, setIsLoadingQrCode] = useState(false);
	const [transactionId, setTransactionId] = useState<string | null>(null);
	const [timeoutOccurred, setTimeoutOccurred] = useState(false);
	const [retryCount, setRetryCount] = useState(0);
	const maxRetries = 3;
	const requestDataRef = useRef<any>(null);

	// Estado para controlar se a transação está em processamento assíncrono
	const [isProcessingAsync, setIsProcessingAsync] = useState(false);

	// Estado para controlar o intervalo de polling
	const [pollingCount, setPollingCount] = useState(0);
	const maxPollingAttempts = 15; // Aumentado de 10 para 15 tentativas máximas de polling

	// Ref para armazenar o intervalo de polling
	const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

	// Ref para rastrear se uma consulta de transaction já está em andamento
	const isCheckingTransactionRef = useRef(false);

	const [pixData, setPixData] = useState<{
		pixQrCode?: string;
		pixPayload?: string;
		id?: string;
		gatewayType?: string;
		expirationDate?: string;
		txid?: string;
		amount?: number;
	}>({});
	const { toast } = useToast();
	const { activeOrganization, loaded: organizationLoaded } =
		useActiveOrganization();

	// Estado para armazenar todos os gateways disponíveis
	const [availableGateways, setAvailableGateways] = useState<Gateway[]>([]);

	// Função para carregar todos os gateways ativos
	const loadGateways = useCallback(async () => {
		if (!organizationLoaded) {
			console.log('Organization context not loaded yet');
			return;
		}

		if (!activeOrganization?.id) {
			console.log('No active organization');
			toast({
				title: 'Erro',
				description:
					'Nenhuma organização selecionada. Por favor, selecione uma organização primeiro.',
				variant: 'error',
			});
			onClose();
			return;
		}

		setIsLoadingGateways(true);
		try {
			console.log(
				`Loading gateways for organization: ${activeOrganization.id}`
			);

			// Fetch all active gateways
			const response = await fetch(
				`/api/payments/gateways/list?organizationId=${activeOrganization.id}`,
				{
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
				}
			);

			// console.log(`Response status: ${response.status}`);

			if (!response.ok) {
				const errorText = await response.text();
				console.error(
					`Error loading gateways: ${response.status} - ${errorText}`
				);
				throw new Error(`Error loading payment gateways: ${response.status}`);
			}

			const data = await response.json();
			console.log("📋 Dados dos gateways recebidos:", data);

			if (
				data.success &&
				data.receiveEnabledGateways &&
				data.receiveEnabledGateways.length > 0
			) {
				// Use the receive-enabled gateways
				const receiveEnabledGateways = data.receiveEnabledGateways;

				console.log("🔧 Gateways que podem receber:", receiveEnabledGateways.map((g: Gateway) => ({
					id: g.id,
					name: g.name,
					type: g.type,
					isDefault: g.isDefault,
					isActive: g.isActive,
					canReceive: g.canReceive,
					canSend: g.canSend
				})));

				if (receiveEnabledGateways.length > 0) {
					setAvailableGateways(receiveEnabledGateways);

					// Encontra o gateway padrão com isDefault=true
					const defaultGateway = receiveEnabledGateways.find(
						(g: Gateway) => g.isDefault === true
					);

					if (defaultGateway) {
						console.log("✅ Usando gateway padrão (isDefault=true):", {
							id: defaultGateway.id,
							name: defaultGateway.name,
							type: defaultGateway.type,
							isDefault: defaultGateway.isDefault
						});
						setDefaultGateway(defaultGateway);
					} else {
						// Se nenhum gateway tiver isDefault=true, tenta evitar Reflowpay
						console.log('⚠️ Nenhum gateway com isDefault=true encontrado');
						const nonReflowpayGateway = receiveEnabledGateways.find(
							(g: Gateway) => g.type.toUpperCase() !== 'REFLOWPAY'
						);

						if (nonReflowpayGateway) {
							console.log("✅ Usando primeiro gateway não-Reflowpay:", {
								id: nonReflowpayGateway.id,
								name: nonReflowpayGateway.name,
								type: nonReflowpayGateway.type
							});
							setDefaultGateway(nonReflowpayGateway);
						} else {
							// Se só tiver Reflowpay, usa o primeiro gateway da lista
							console.log("✅ Usando primeiro gateway disponível:", {
								id: receiveEnabledGateways[0].id,
								name: receiveEnabledGateways[0].name,
								type: receiveEnabledGateways[0].type
							});
							setDefaultGateway(receiveEnabledGateways[0]);
						}
					}
				} else {
					setAvailableGateways([]);
					setDefaultGateway(null);
					toast({
						title: 'Nenhum gateway para cobranças',
						description:
							'Entre em contato com o administrador para configurar um gateway que suporte recebimento de dinheiro para cobranças PIX.',
						variant: 'error',
					});
				}
			} else {
				setAvailableGateways([]);
				setDefaultGateway(null);
				toast({
					title: 'Nenhum gateway configurado',
					description:
						'Entre em contato com o administrador para configurar um gateway de pagamento para cobranças PIX.',
					variant: 'error',
				});
			}
		} catch (error) {
			console.error('Error loading payment gateways:', error);

			// Check if it's an authentication error
			if (error instanceof Error && error.message.includes('401')) {
				toast({
					title: 'Sessão expirada',
					description: 'Sua sessão expirou. Por favor, faça login novamente.',
					variant: 'error',
				});

				// Redirect to the login page after a brief delay
				setTimeout(() => {
					window.location.href = '/auth/login';
				}, 2000);
			} else {
				toast({
					title: 'Erro',
					description:
						error instanceof Error
							? error.message
							: 'Não foi possível carregar os gateways de pagamento',
					variant: 'error',
				});
			}
		} finally {
			setIsLoadingGateways(false);
		}
	}, [activeOrganization?.id, organizationLoaded, toast, onClose]);

	// Função para inicializar os dados
	const initializeData = useCallback(async () => {
		if (isOpen && organizationLoaded) {
			try {
				await loadGateways();
			} catch (error) {
				console.error('Erro ao inicializar dados:', error);
			}
		}
	}, [isOpen, organizationLoaded, loadGateways]);

	// Carregar gateways quando o modal é aberto
	useEffect(() => {
		let isMounted = true;

		if (isMounted) {
			initializeData();
		}

		return () => {
			isMounted = false;
		};
	}, [isOpen, organizationLoaded, initializeData]);

	// Limpar intervalos de polling quando o componente for desmontado
	useEffect(() => {
		return () => {
			// Limpar qualquer intervalo de polling existente
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
				pollingIntervalRef.current = null;
			}
		};
	}, []);

	// Modificar o formSchema para remover gatewayId
	const formSchema = z.object({
		customerName: z
			.string()
			.min(1, { message: 'Nome do cliente é obrigatório' }),
		customerEmail: z
			.string()
			.email({ message: 'Email inválido' })
			.min(1, { message: 'Email é obrigatório' }),
		customerPhone: z.string().optional(),
		customerDocument: z
			.string()
			.min(11, { message: 'CPF/CNPJ é obrigatório' })
			.refine(
				(val) => {
					// Validação básica de CPF/CNPJ (apenas comprimento)
					const digits = val.replace(/\D/g, '');
					return digits.length === 11 || digits.length === 14;
				},
				{ message: 'CPF/CNPJ inválido' }
			),
		amount: z
			.string()
			.min(1, { message: 'Valor é obrigatório' })
			.refine(
				(val) => {
					const numValue = parseAmountValue(val);
					return !isNaN(numValue) && numValue > 0;
				},
				{ message: 'Valor inválido' }
			),
		description: z.string().min(1, { message: 'Descrição é obrigatória' }),
	});

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			customerName: '',
			customerEmail: '',
			customerPhone: '',
			customerDocument: '',
			amount: '',
			description: '',
		},
	});

	// Função para processar o formulário
	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		if (isLoading || !activeOrganization?.id) return;

		console.log('🚀 Iniciando criação de cobrança PIX');
		console.log('📋 Dados da requisição:', {
			organizationId: activeOrganization.id,
			organizationName: activeOrganization.name,
			customerName: values.customerName,
			customerEmail: values.customerEmail,
			amount: values.amount,
			description: values.description
		});

		// Log do gateway selecionado
		if (defaultGateway) {
			console.log('🔧 Gateway selecionado:', {
				id: defaultGateway.id,
				name: defaultGateway.name,
				type: defaultGateway.type,
				isDefault: defaultGateway.isDefault,
				isActive: defaultGateway.isActive,
				canReceive: defaultGateway.canReceive,
				canSend: defaultGateway.canSend
			});
		} else {
			console.log('⚠️ Nenhum gateway padrão selecionado');
		}

		setIsLoading(true);

		// Armazenar os dados da requisição para possível retry
		requestDataRef.current = {
			...values,
			organizationId: activeOrganization.id,
		};

		try {
			// Converter o valor para número usando a função utilitária existente
			const amount =
				typeof values.amount === 'string'
					? parseAmountValue(values.amount)
					: values.amount;

			console.log('💰 Valor convertido:', amount);

			// Preparar payload da requisição
			const requestPayload = {
				amount,
				customerName: values.customerName,
				customerEmail: values.customerEmail,
				customerPhone: values.customerPhone,
				customerDocument: values.customerDocument || undefined,
				description:
					values.description || `Pagamento de ${formatCurrency(amount)}`,
				organizationId: activeOrganization.id,
				metadata: {
					createdBy: 'web',
					createdAt: new Date().toISOString(),
					// Adicionar informações do gateway usado
					gatewayId: defaultGateway?.id,
					gatewayType: defaultGateway?.type,
					gatewayName: defaultGateway?.name
				},
			};

			console.log('📤 Enviando requisição para /api/payments/transactions');
			console.log('📦 Payload:', JSON.stringify(requestPayload, null, 2));

			// Chamar a API para criar a cobrança
			const response = await fetch('/api/payments/transactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify(requestPayload),
			});

			// Tentar extrair os dados da resposta mesmo se o status não for OK
			let data: any;
			try {
				data = await response.json();
			} catch (e) {
				console.error('Erro ao analisar resposta JSON:', e);
				data = {
					success: false,
					error: 'Erro de comunicação com o servidor',
					message: 'Resposta inválida do servidor',
				};
			}

			// Log da resposta da API
			console.log('📥 Resposta da API:', {
				status: response.status,
				statusText: response.statusText,
				success: data?.success,
				transactionId: data?.id,
				gatewayType: data?.gatewayType,
				gatewayId: data?.gatewayId,
				clientId: data?.clientId || data?.gateway?.clientId || data?.metadata?.clientId,
				transactionStatus: data?.status
			});

			// Log detalhado da resposta para debug
			console.log('🔍 Resposta completa da API:', JSON.stringify(data, null, 2));

			// Verificar se é uma resposta assíncrona (202 Accepted)
			if (response.status === 202) {
				console.log('Recebida resposta assíncrona, iniciando polling', data);

				// Definir o ID da transação
				setTransactionId(data?.id);
				setTransactionCreated(true);
				setIsProcessingAsync(true);

				// Iniciar polling imediatamente
				if (data?.id) {
					startPollingForQrCode(data.id);
				}

				// Mostrar toast informativo
				toast({
					title: 'PIX sendo gerado',
					description:
						'O QR code PIX está sendo processado. Aguarde um momento...',
					variant: 'default',
				});

				// Aguardar um momento para garantir que o estado foi atualizado
				setTimeout(() => {
					if (onSuccess && data) {
						console.log(
							'Chamando callback de sucesso para resposta assíncrona'
						);
						onSuccess(data);
					}
				}, 100);

				return;
			}

			if (!response.ok) {
				let errorMessage = 'Erro ao processar a cobrança';

				// Extrair mensagem de erro da nova estrutura de resposta
				if (data) {
					if (data.error) {
						errorMessage = data.error;
					} else if (data.message) {
						errorMessage = data.message;
					} else if (typeof data === 'string') {
						errorMessage = data;
					}
				}

				console.error('Erro na API:', errorMessage, data);

				// Verificar se ainda temos dados do QR code mesmo com erro de status
				if (data && (data.pix?.qrCode || data.pix?.copyPasteCode || data.id)) {
					console.log(
						'Recebido dados de QR code mesmo com erro de status, tentando continuar o fluxo'
					);
					// Continuar processando os dados como se fosse sucesso
				} else {
					// Se não temos dados úteis, lançar o erro
					throw new Error(errorMessage);
				}
			}

			// Se chegamos aqui, temos dados (seja da resposta com erro ou sucesso)
			console.log('✅ Cobrança processada:', data);

			// 🚨 DEBUG: Log detalhado da estrutura de dados recebida
			console.log('🔍 Estrutura de dados recebida:', {
				hasPix: !!data?.pix,
				pixKeys: data?.pix ? Object.keys(data.pix) : [],
				hasQrCode: !!data?.pix?.qrCode,
				qrCodeKeys: data?.pix?.qrCode ? Object.keys(data.pix.qrCode) : [],
				hasQrCodeImage: !!data?.qrCodeImage,
				hasQrCodeDirect: !!data?.qrCode,
				hasPixCode: !!data?.pixCode,
				hasPixQrCode: !!data?.pixQrCode,
				hasMetadata: !!data?.metadata,
				metadataKeys: data?.metadata ? Object.keys(data.metadata) : [],
				dataKeys: Object.keys(data || {})
			});

			// Log do gateway usado na transação
			const usedGatewayId = data?.gatewayId || data?.gateway?.id || data?.metadata?.gatewayId;
			const usedGatewayType = data?.gatewayType || data?.gateway?.type || data?.metadata?.gatewayType;
			const usedClientId = data?.clientId || data?.gateway?.clientId || data?.metadata?.clientId;

			console.log('🔧 Gateway usado na transação:', {
				gatewayId: usedGatewayId,
				gatewayType: usedGatewayType,
				clientId: usedClientId ? `${usedClientId.substring(0, 8)}...` : 'N/A',
				clientIdFull: usedClientId || 'N/A',
				expectedGatewayId: defaultGateway?.id,
				expectedGatewayType: defaultGateway?.type,
				match: usedGatewayId === defaultGateway?.id
			});

			// Verificar se o gateway usado é o esperado
			if (usedGatewayId && defaultGateway?.id && usedGatewayId !== defaultGateway.id) {
				console.log('⚠️ ATENÇÃO: Gateway usado é diferente do esperado!', {
					expected: defaultGateway.id,
					used: usedGatewayId
				});
			}

			// Definir o ID da transação
			setTransactionId(data?.id);

			// Extrair informações do gateway e QR code
			const isProcessing = data?.status === 'PROCESSING';

			// Tentar extrair o QR code de diferentes locais possíveis na resposta
			const qrImageString =
				data?.pix?.qrCode?.imagem ||
				data?.pix?.qrCodeImage ||
				data?.pixCode?.imagem ||
				data?.pixQrCode ||
				data?.pixEncodedImage ||
				data?.qrCodeImage ||
				(data?.metadata &&
					(data?.metadata.pixQrCode ||
						data?.metadata.qrCodeImage ||
						data?.metadata.pix?.qrCode?.imagem));

			// Tentar extrair o código PIX de diferentes locais possíveis na resposta
			const emvPayloadString =
				data?.pix?.qrCode?.emv ||
				data?.pix?.copyPasteCode?.emv ||
				data?.pix?.payload ||
				data?.pixCode?.emv ||
				data?.pixPayload ||
				data?.pixCode ||
				data?.pixCopyPaste ||
				data?.copyPasteCode ||
				(data?.metadata &&
					(data?.metadata.pixCode ||
						data?.metadata.pixPayload ||
						data?.metadata.pix?.qrCode?.emv));

			// Função para extrair o valor correto do QR code ou código PIX
			const extractQrImageValue = (qrData: any): string | undefined => {
				if (!qrData) return undefined;

				// Se for uma string, retornar diretamente
				if (typeof qrData === 'string') return qrData;

				// Se for um objeto com propriedade imagem, retornar o valor
				if (typeof qrData === 'object' && qrData.imagem) return qrData.imagem;

				// Caso especial para o formato do Pluggou
				if (typeof qrData === 'object' && qrData.emv && qrData.emv.imagem)
					return qrData.emv.imagem;

				return undefined;
			};

			const extractEmvValue = (emvData: any): string | undefined => {
				if (!emvData) return undefined;

				// Se for uma string, retornar diretamente
				if (typeof emvData === 'string') return emvData;

				// Se for um objeto com propriedade emv, retornar o valor
				if (typeof emvData === 'object' && emvData.emv) return emvData.emv;

				return undefined;
			};

			// Extrair os valores corretos
			const qrImageValue = extractQrImageValue(qrImageString);
			const emvPayloadValue = extractEmvValue(emvPayloadString);

			// Verificar se o processamento foi concluído
			const processingCompleted =
				data?.processingCompleted === true ||
				data?.status === 'COMPLETED' ||
				data?.status === 'PAID' ||
				(data?.metadata && data?.metadata.processingCompleted === true);

			console.log('Dados extraídos:', {
				hasQrImage: !!qrImageString,
				hasEmvCode: !!emvPayloadString,
				qrImageValue: qrImageValue ? 'Disponível' : 'Não disponível',
				emvPayloadValue: emvPayloadValue ? 'Disponível' : 'Não disponível',
				processingCompleted,
				status: data?.status,
			});

			setPixData({
				pixQrCode: qrImageValue,
				pixPayload: emvPayloadValue,
				id: data?.id,
				gatewayType: data?.gatewayType,
				expirationDate: data?.pix?.expirationDate,
				txid: data?.pix?.txid,
				amount: data?.amount,
			});

			// Importante: definir transactionCreated como true ANTES de chamar onSuccess
			setTransactionCreated(true);

			// Se não temos os dados do PIX (raro agora), iniciar o polling
			if (!qrImageValue && !emvPayloadValue) {
				console.log('Dados do PIX não disponíveis, iniciando polling');
				setIsProcessingAsync(true);
				if (data?.id) {
					startPollingForQrCode(data.id);
				} else {
					searchRecentTransaction();
				}
			}

			// Aguardar um momento para garantir que o estado foi atualizado
			setTimeout(() => {
				if (onSuccess && data) {
					console.log('Chamando callback de sucesso');
					onSuccess(data);
				}
			}, 100);

			// Não fechamos o modal para mostrar o QR code
		} catch (error: any) {
			console.error('Erro ao processar cobrança:', error);

			// Verificar se é um erro de timeout
			if (error.message && error.message.includes('Timeout')) {
				setTimeoutOccurred(true);
				setTransactionCreated(true); // Assumimos que a transação foi criada, mas houve timeout na resposta

				// Iniciar polling imediatamente para tentar encontrar a transação
				console.log(
					'Timeout na requisição, iniciando busca automática por transações recentes'
				);
				setIsProcessingAsync(true);
				setTimeout(() => {
					searchRecentTransaction();
				}, 500);

				toast({
					title: 'Processando',
					description:
						'A cobrança está sendo processada. Aguarde um momento enquanto buscamos os dados do PIX.',
					variant: 'default',
				});
			} else {
				toast({
					title: 'Erro na cobrança',
					description:
						error.message ||
						'Não foi possível processar a cobrança. Tente novamente.',
					variant: 'error',
				});

				// Se o erro ocorre após a criação do QR code no gateway, tente buscar a transação recente
				setTimeout(() => {
					searchRecentTransaction();
				}, 1000);
			}
		} finally {
			console.log('Finalizando processamento, definindo isLoading como false');
			setIsLoading(false);
		}
	};

	// Nova função para buscar transações recentes quando ocorrer timeout
	const searchRecentTransaction = async () => {
		if (
			!activeOrganization?.id ||
			!requestDataRef.current ||
			isCheckingTransactionRef.current
		) {
			return;
		}

		isCheckingTransactionRef.current = true;
		setIsLoadingQrCode(true);

		try {
			// Buscar transações recentes, ordenadas por data de criação mais recente
			const response = await fetch(
				`/api/payments/transactions?organizationId=${activeOrganization.id}&page=1&limit=20`, // Aumentando o limite para 20 para pegar mais transações
				{
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
				}
			);

			if (!response.ok) {
				throw new Error(`Erro ao buscar transações: ${response.status}`);
			}

			const data = await response.json();

			// Pegar as transações mais recentes
			const recentTransactions = data.data || [];
			console.log(
				'Transações recentes encontradas:',
				recentTransactions.length
			);

			if (recentTransactions.length > 0) {
				// Converter o valor para número para comparação
				const requestAmount = requestDataRef.current?.amount
					? parseFloat(
							requestDataRef.current.amount
								.replace(',', '.')
								.replace(/\./g, '')
								.replace(',', '.')
						)
					: null;
				const requestEmail =
					requestDataRef.current?.customerEmail?.toLowerCase();
				const requestName = requestDataRef.current?.customerName;

				console.log('Procurando transação com:', {
					amount: requestAmount,
					email: requestEmail,
					customerName: requestName,
				});

				// Procurar pela transação mais recente que corresponda a nossos critérios
				const matchingTransactions = recentTransactions
					.filter((tx: any) => {
						// Verificar se foi criado nos últimos 5 minutos (aumentado de 2 para 5)
						const isRecent =
							new Date(tx.createdAt).getTime() > Date.now() - 5 * 60 * 1000;

						// Verificações relaxadas para encontrar a transação mesmo com dados incompletos
						let matchesEmail =
							!requestEmail || tx.customerEmail?.toLowerCase() === requestEmail;
						let matchesAmount =
							!requestAmount || Math.abs(tx.amount - requestAmount) < 0.01; // Tolerância para diferenças de arredondamento
						let matchesName =
							!requestName ||
							tx.customerName?.includes(requestName) ||
							requestName.includes(tx.customerName);

						// Dar preferência a transações pendentes, mas considerar outras também
						const isPending =
							tx.status === 'PENDING' ||
							tx.status === 'PROCESSING' ||
							tx.status === 'CREATED';

						return (
							isRecent &&
							(matchesEmail || matchesAmount || matchesName) &&
							isPending
						);
					})
					.sort((a: any, b: any) => {
						// Ordenar por data de criação mais recente
						return (
							new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
						);
					});

				if (matchingTransactions.length > 0) {
					const matchingTransaction = matchingTransactions[0]; // Pegar a transação mais recente que corresponde
					console.log(
						'Transação correspondente encontrada:',
						matchingTransaction
					);
					setTransactionId(matchingTransaction.id);

					// Buscar os detalhes completos da transação
					const success = await fetchTransactionQrCode(matchingTransaction.id);

					if (success) {
						console.log('Dados do QR code recuperados com sucesso');
						setTimeoutOccurred(false);
						return;
					}
				}

				console.log(
					'Nenhuma transação correspondente encontrada com os critérios principais'
				);

				// Se não encontrarmos uma correspondência exata, procurar qualquer transação recente
				const veryRecentTransaction = recentTransactions
					.filter((tx: any) => {
						// Verificar se foi criado nos últimos 30 segundos
						return new Date(tx.createdAt).getTime() > Date.now() - 30 * 1000;
					})
					.sort((a: any, b: any) => {
						// Ordenar por data de criação mais recente
						return (
							new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
						);
					})[0];

				if (veryRecentTransaction) {
					console.log(
						'Encontrada transação muito recente, tentando utilizar:',
						veryRecentTransaction
					);
					setTransactionId(veryRecentTransaction.id);

					// Buscar os detalhes completos da transação
					const success = await fetchTransactionQrCode(
						veryRecentTransaction.id
					);

					if (success) {
						console.log(
							'Dados do QR code recuperados com sucesso da transação muito recente'
						);
						setTimeoutOccurred(false);
						return;
					}
				}
			}

			console.log(
				'Nenhuma transação correspondente encontrada, continuando com polling'
			);
			// Se não encontramos uma transação correspondente, vamos continuar com o polling normal
			// que tentará buscar o QR code periodicamente
		} catch (error) {
			console.error('Erro ao buscar transações recentes:', error);
		} finally {
			setIsLoadingQrCode(false);
			isCheckingTransactionRef.current = false;
		}
	};

	// Função para iniciar o polling para buscar os dados do QR code
	const startPollingForQrCode = (transactionId: string) => {
		console.log(`Iniciando polling para a transação ${transactionId}`);
		setPollingCount(0);

		// Limpar qualquer intervalo existente
		if (pollingIntervalRef.current) {
			clearInterval(pollingIntervalRef.current);
			pollingIntervalRef.current = null;
		}

		// Iniciar com um intervalo curto para a primeira verificação
		setTimeout(pollOnce, 1000);

		// Função para executar uma verificação de polling
		async function pollOnce() {
			// Verificar se já estamos consultando uma transação
			if (isCheckingTransactionRef.current) {
				console.log('Já existe uma consulta em andamento, pulando este ciclo');
				if (pollingCount < maxPollingAttempts) {
					setTimeout(pollOnce, 3000);
				}
				return;
			}

			// Incrementar o contador de polling
			setPollingCount((prev) => {
				const newCount = prev + 1;
				console.log(
					`Tentativa de polling ${newCount} de ${maxPollingAttempts}`
				);
				return newCount;
			});

			// Marcar que estamos iniciando uma consulta
			isCheckingTransactionRef.current = true;

			try {
				// Buscar os dados do QR code
				const success = await fetchTransactionQrCode(transactionId);

				// Se atingimos o número máximo de tentativas ou o QR code foi obtido com sucesso, parar o polling
				if (pollingCount >= maxPollingAttempts || success) {
					console.log(
						'Parando polling: ' +
							(success
								? 'QR code obtido'
								: 'Número máximo de tentativas atingido')
					);
					setIsProcessingAsync(false);

					// Se atingimos o máximo de tentativas e não temos o QR code, oferecer retry
					if (
						pollingCount >= maxPollingAttempts &&
						!success &&
						!pixData.pixQrCode &&
						!pixData.pixPayload
					) {
						setTimeoutOccurred(true);
						toast({
							title: 'Não foi possível obter o QR code',
							description:
								"Tente usar os botões de 'Tentar novamente' ou 'Recriar transação'",
							variant: 'error',
						});
					}

					isCheckingTransactionRef.current = false;
					return;
				}

				// Agendar a próxima chamada apenas se não obtivemos sucesso e não atingimos o limite
				if (!success && pollingCount < maxPollingAttempts) {
					// Intervalo progressivo: começar com 2s e aumentar gradualmente
					const interval = Math.min(2000 + pollingCount * 500, 5000);
					setTimeout(pollOnce, interval);
				}
			} finally {
				// Sempre marcar que a consulta terminou
				isCheckingTransactionRef.current = false;
			}
		}
	};

	// Função para buscar os dados do QR code
	const fetchTransactionQrCode = async (id?: string): Promise<boolean> => {
		const txId = id || transactionId;

		if (!txId) {
			console.error('Não há ID de transação para buscar o QR code');
			return false;
		}

		setIsLoadingQrCode(true);
		try {
			console.log(`Buscando dados do QR code para a transação ${txId}`);

			const response = await fetch(`/api/payments/transactions/${txId}`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error(
					`Erro ao buscar dados da transação: ${response.status}`
				);
			}

			const data = await response.json();
			console.log('📥 Dados da transação recuperados:', {
				id: data.id,
				status: data.status,
				gatewayType: data.gatewayType,
				gatewayId: data.gatewayId,
				clientId: data.clientId || data.gateway?.clientId || data.metadata?.clientId,
				amount: data.amount,
				hasPixData: !!(data.pix || data.pixCode || data.pixQrCode)
			});

			// Log do gateway usado na transação (se disponível)
			if (data.gatewayId || data.gatewayType) {
				console.log('🔧 Gateway usado na transação (busca):', {
					gatewayId: data.gatewayId,
					gatewayType: data.gatewayType,
					clientId: data.clientId || data.gateway?.clientId || data.metadata?.clientId,
					expectedGatewayId: defaultGateway?.id,
					match: data.gatewayId === defaultGateway?.id
				});
			}

			// Verificar se temos os dados do QR code
			const transaction = data;

			// Se a transação está em PROCESSING, continuar o polling
			if (transaction.status === 'processing') {
				console.log('Transação ainda em processamento, continuando polling');
				return false; // Continuar polling
			}

			// Tentar extrair o QR code de diferentes locais possíveis na resposta
			const qrImageString =
				transaction.pix?.qrCode?.imagem ||
				transaction.pix?.qrCodeImage ||
				transaction.pixCode?.imagem ||
				transaction.pixQrCode ||
				transaction.pixEncodedImage ||
				transaction.qrCodeImage ||
				(transaction.metadata &&
					(transaction.metadata.pixQrCode ||
						transaction.metadata.qrCodeImage ||
						transaction.metadata.pix?.qrCode?.imagem));

			// Tentar extrair o código PIX de diferentes locais possíveis na resposta
			const emvPayloadString =
				transaction.pix?.qrCode?.emv ||
				transaction.pix?.copyPasteCode?.emv ||
				transaction.pix?.payload ||
				transaction.pixCode?.emv ||
				transaction.pixPayload ||
				transaction.pixCode ||
				transaction.pixCopyPaste ||
				transaction.copyPasteCode ||
				(transaction.metadata &&
					(transaction.metadata.pixCode ||
						transaction.metadata.pixPayload ||
						transaction.metadata.pix?.qrCode?.emv));

			// Função para extrair o valor correto do QR code ou código PIX
			const extractQrImageValue = (qrData: any): string | undefined => {
				if (!qrData) return undefined;

				// Se for uma string, retornar diretamente
				if (typeof qrData === 'string') return qrData;

				// Se for um objeto com propriedade imagem, retornar o valor
				if (typeof qrData === 'object' && qrData.imagem) return qrData.imagem;

				// Caso especial para o formato do Pluggou
				if (typeof qrData === 'object' && qrData.emv && qrData.emv.imagem)
					return qrData.emv.imagem;

				return undefined;
			};

			const extractEmvValue = (emvData: any): string | undefined => {
				if (!emvData) return undefined;

				// Se for uma string, retornar diretamente
				if (typeof emvData === 'string') return emvData;

				// Se for um objeto com propriedade emv, retornar o valor
				if (typeof emvData === 'object' && emvData.emv) return emvData.emv;

				return undefined;
			};

			// Extrair os valores corretos
			const qrImageValue = extractQrImageValue(qrImageString);
			const emvPayloadValue = extractEmvValue(emvPayloadString);

			// Verificar se o processamento foi concluído
			const processingCompleted =
				transaction.processingCompleted === true ||
				transaction.status === 'completed' ||
				transaction.status === 'paid' ||
				(transaction.metadata &&
					transaction.metadata.processingCompleted === true);

			console.log('Dados extraídos:', {
				hasQrImage: !!qrImageString,
				hasEmvCode: !!emvPayloadString,
				qrImageValue: qrImageValue ? 'Disponível' : 'Não disponível',
				emvPayloadValue: emvPayloadValue ? 'Disponível' : 'Não disponível',
				processingCompleted,
				status: transaction.status,
			});

			// Se o processamento foi concluído ou temos os dados do QR code, parar o polling
			if (processingCompleted || qrImageValue || emvPayloadValue) {
				console.log(
					'Dados do QR code recuperados com sucesso ou processamento concluído'
				);

				// Parar o polling
				if (pollingIntervalRef.current) {
					clearInterval(pollingIntervalRef.current);
					pollingIntervalRef.current = null;
				}

				// Atualizar os dados do QR code
				if (qrImageValue || emvPayloadValue) {
					setPixData({
						pixQrCode: qrImageValue,
						pixPayload: emvPayloadValue,
						id: transaction.id,
						gatewayType:
							transaction.gatewayType ||
							(transaction.metadata && transaction.metadata.gatewayType),
						expirationDate:
							transaction.pix?.expirationDate || transaction.expirationDate,
						txid: transaction.pix?.txid || transaction.txid || transaction.id,
						amount: transaction.amount,
					});

					// Garantir que transactionCreated está definido como true
					setTransactionCreated(true);

					// Resetar estados
					setTimeoutOccurred(false);
					setIsProcessingAsync(false);

					// Mostrar toast de sucesso apenas se estávamos em modo de recuperação
					if (timeoutOccurred) {
						toast({
							title: 'QR Code recuperado',
							description: 'O QR code foi recuperado com sucesso',
							variant: 'success',
						});
					}

					// Retornar verdadeiro para indicar que obtivemos os dados do QR code
					return true;
				} else if (processingCompleted) {
					// Se o processamento foi concluído mas não temos os dados do QR code
					setIsProcessingAsync(false);

					// Verificar se a transação foi paga
					if (
						transaction.status === 'paid' ||
						transaction.status === 'completed'
					) {
						toast({
							title: 'Pagamento concluído',
							description: 'O pagamento foi concluído com sucesso',
							variant: 'success',
						});

						// Fechar o modal após um breve atraso
						setTimeout(() => {
							onClose();
							// Recarregar a página para atualizar a lista de transações
							window.location.reload();
						}, 2000);

						return true;
					}
				}
			} else if (isProcessingAsync) {
				// Se ainda estamos em processamento assíncrono, continuar o polling
				console.log('Transação ainda em processamento, continuando polling');
			} else {
				// Se não estamos em processamento assíncrono e não temos os dados do QR code
				console.log('Transação encontrada, mas sem dados do QR code');

				// Se ainda não atingimos o número máximo de tentativas, tentar novamente
				if (retryCount < maxRetries) {
					setRetryCount((prev) => prev + 1);
				}
			}

			// Se chegamos aqui, não obtivemos o QR code ainda
			return false;
		} catch (error) {
			console.error('Erro ao buscar dados do QR code:', error);

			// Se ainda não atingimos o número máximo de tentativas, tentar novamente
			if (retryCount < maxRetries) {
				setRetryCount((prev) => prev + 1);
			}

			return false;
		} finally {
			setIsLoadingQrCode(false);
		}
	};

	// Função para tentar criar a transação novamente após um timeout
	const retryCreateTransaction = async () => {
		if (!requestDataRef.current || !activeOrganization?.id) {
			console.error('Dados insuficientes para recriar a transação');
			return;
		}

		setIsLoading(true);
		try {
			// Tentar criar a transação novamente com os mesmos dados
			const response = await fetch('/api/payments/transactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(requestDataRef.current),
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error(`Erro ao criar transação: ${response.status}`);
			}

			const data = await response.json();
			console.log('Transação recriada com sucesso:', data);

			// Extrair dados do QR code
			const qrImageString =
				data.pixCode?.imagem || data.pixQrCode || data.pix?.encodedImage;
			const emvPayloadString =
				data.pixCode?.emv ||
				data.pixPayload ||
				data.pix?.payload ||
				data.pixCode;

			// Atualizar os dados
			setPixData({
				pixQrCode: qrImageString,
				pixPayload: emvPayloadString,
				id: data.id,
				gatewayType: data.gatewayType,
				amount: data.amount,
			});

			setTransactionId(data.id);
			setTimeoutOccurred(false);
			setTransactionCreated(true);

			// Não exibir toast para não sobrecarregar o usuário
		} catch (error) {
			console.error('Erro ao recriar transação:', error);
			toast({
				title: 'Erro ao recriar transação',
				description:
					error instanceof Error ? error.message : 'Erro desconhecido',
				variant: 'error',
			});
		} finally {
			setIsLoading(false);
		}
	};

	// Impedir que o modal feche quando transactionCreated for true
	const handleOpenChange = (open: boolean) => {
		console.log('Dialog onOpenChange', {
			open,
			transactionCreated,
			hasPixData: !!pixData.pixPayload,
		});

		if (!open) {
			// Se o usuário está tentando fechar o modal
			if (transactionCreated && (pixData.pixQrCode || pixData.pixPayload)) {
				// Se a transação foi criada e temos dados do PIX, não fechamos automaticamente
				// Apenas mostramos uma mensagem para o usuário
				console.log(
					'Impedindo fechamento automático do modal com dados do PIX'
				);
				// Não exibir toast para não sobrecarregar o usuário
				// Não chamamos onClose() aqui para manter o modal aberto
			} else {
				// Caso normal - usuário fechando o modal antes de criar a transação
				// ou após criar mas sem dados do PIX
				console.log('Fechando modal normalmente');
				onClose();
			}
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleOpenChange}>
			<DialogContent className='sm:max-w-[700px] p-0 overflow-hidden'>
				<div className='bg-background px-6 pt-6'>
					<DialogHeader className='mb-0'>
						<DialogTitle className='text-center text-2xl font-bold flex items-center justify-center gap-2'>
							<QrCode className='h-6 w-6 text-primary' />
							{transactionCreated ? 'QR Code PIX' : 'Criar Cobrança PIX'}
						</DialogTitle>
						<p className='text-center text-sm text-muted-foreground'>
							{transactionCreated
								? 'Escaneie o QR Code ou copie o código PIX para pagar'
								: 'Gere um QR Code PIX para receber pagamentos'}
						</p>
					</DialogHeader>
				</div>
				<div className='p-6 bg-card'>
					{!transactionCreated ? (
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className='space-y-4'
							>
								{/* Customer Information */}
								<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
									<FormField
										control={form.control}
										name='customerName'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Nome do Cliente</FormLabel>
												<FormControl>
													<Input
														placeholder='Nome completo'
														{...field}
														className='w-full h-10 rounded-lg'
													/>
												</FormControl>
												<FormMessage className='text-destructive' />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name='customerEmail'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Email</FormLabel>
												<FormControl>
													<Input
														placeholder='<EMAIL>'
														type='email'
														{...field}
														className='w-full h-10 rounded-lg'
													/>
												</FormControl>
												<FormMessage className='text-destructive' />
											</FormItem>
										)}
									/>
								</div>

								<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
									<FormField
										control={form.control}
										name='customerPhone'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Telefone (opcional)</FormLabel>
												<FormControl>
													<IMaskInput
														{...field}
														mask='+{55} (00) 00000-0000'
														unmask={false}
														placeholder='+55 (00) 00000-0000'
														className='flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
														onAccept={(value) => field.onChange(value)}
													/>
												</FormControl>
												<FormMessage className='text-destructive' />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name='customerDocument'
										render={({ field }) => (
											<FormItem>
												<FormLabel>CPF/CNPJ</FormLabel>
												<FormControl>
													<IMaskInput
														{...field}
														mask={[
															{ mask: '000.000.000-00', maxLength: 14 },
															{ mask: '00.000.000/0000-00', maxLength: 18 },
														]}
														unmask={false}
														placeholder='000.000.000-00 ou 00.000.000/0000-00'
														className='flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
														onAccept={(value) => field.onChange(value)}
													/>
												</FormControl>
												<FormMessage className='text-destructive' />
											</FormItem>
										)}
									/>
								</div>

								<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
									<FormField
										control={form.control}
										name='amount'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Valor da Cobrança</FormLabel>
												<FormControl>
													<div className='relative'>
														<span className='absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground font-medium'>
															R$
														</span>
														<IMaskInput
															{...field}
															mask={Number}
															radix=','
															thousandsSeparator='.'
															mapToRadix={['.']}
															scale={2}
															normalizeZeros={true}
															padFractionalZeros={true}
															placeholder='0,00'
															className='flex h-12 w-full rounded-lg border border-input bg-background px-3 py-2 text-lg font-semibold ring-offset-background file:border-0 file:bg-transparent file:text-lg file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-10 text-primary'
															onAccept={(value) => field.onChange(value)}
														/>
													</div>
												</FormControl>
												<FormMessage className='text-destructive' />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name='description'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Descrição</FormLabel>
												<FormControl>
													<Input
														placeholder='Descrição da cobrança'
														{...field}
														className='w-full h-10 rounded-lg'
													/>
												</FormControl>
												<FormMessage className='text-destructive' />
											</FormItem>
										)}
									/>
								</div>

								{/* Gateway Status */}
								{/* {availableGateways.length === 0 && (
                <div className="space-y-4">
                  <Alert variant="default" className="bg-amber-500/10 text-amber-500 border-amber-500/20">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      Nenhum gateway de pagamento configurado. Configure um gateway que suporte recebimento de dinheiro.
                    </AlertDescription>
                  </Alert>
                </div>
              )} */}

								{/* Fee Information */}
								{/* <div className="bg-blue-500/10 dark:bg-blue-500/5 border border-blue-500/20 rounded-lg p-4 text-sm">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <p className="text-foreground">
                    Será gerado um QR Code PIX para pagamento. O cliente poderá pagar usando qualquer aplicativo de banco.
                  </p>
                </div>
              </div> */}

								<div className='bg-muted rounded-lg p-4 text-sm'>
									<div className='flex items-start gap-3'>
										<QrCodeIcon className='h-10 w-10 text-primary flex-shrink-0 mt-0.5' />
										<div>
											<p className='text-muted-foreground'>
												Será gerado um QR Code PIX para pagamento. <br />O
												cliente poderá pagar usando qualquer aplicativo de
												banco.
											</p>
										</div>
									</div>
								</div>

								<DialogFooter className='flex flex-row gap-3 sm:justify-between pt-4'>
									<Button
										type='button'
										variant='outline'
										onClick={onClose}
										className='flex-1 h-12 rounded-lg'
										disabled={isLoading}
									>
										Cancelar
									</Button>
									<Button
										type='submit'
										className='flex-1 bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-12 rounded-lg shadow-sm'
										disabled={
											isLoading ||
											availableGateways.length === 0 ||
											!defaultGateway
										}
										title={
											availableGateways.length === 0
												? 'Configure um gateway para cobranças primeiro'
												: ''
										}
									>
										{isLoading ? (
											<Loader2 className='h-4 w-4 animate-spin' />
										) : (
											<QrCode className='h-4 w-4' />
										)}
										{isLoading ? 'Processando...' : 'Criar Cobrança'}
									</Button>
								</DialogFooter>
							</form>
						</Form>
					) : (
						<div className='space-y-6'>
							{/* Exibir mensagem ou alerta se houver erro */}
							{timeoutOccurred && (
								<Alert variant='default' className='mb-4'>
									<AlertDescription className='flex items-center justify-between gap-4'>
										<span>Estamos tentando obter os dados do QR Code PIX.</span>
										<div className='flex items-center gap-2'>
											<Button
												variant='outline'
												size='sm'
												onClick={() => fetchTransactionQrCode()}
												disabled={isLoadingQrCode}
											>
												{isLoadingQrCode ? (
													<Loader2 className='h-4 w-4 animate-spin mr-1' />
												) : (
													<RefreshCw className='h-4 w-4 mr-1' />
												)}
												Tentar novamente
											</Button>
											<Button
												size='sm'
												onClick={retryCreateTransaction}
												disabled={isLoading}
											>
												{isLoading ? (
													<Loader2 className='h-4 w-4 animate-spin mr-1' />
												) : (
													<QrCodeIcon className='h-4 w-4 mr-1' />
												)}
												Recriar transação
											</Button>
										</div>
									</AlertDescription>
								</Alert>
							)}

							{/* QR Code */}
							<div className='flex justify-center mb-6'>
								{isLoadingQrCode || isProcessingAsync ? (
									<div className='flex flex-col items-center justify-center py-6'>
										<div className='w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin mb-4'></div>
										<p className='text-sm text-muted-foreground'>
											Gerando QR Code PIX...
										</p>
									</div>
								) : pixData.pixQrCode ? (
									<div className='flex flex-col items-center justify-center'>
										{/* Card destacado com o valor da cobrança */}
										{pixData.amount && (
											<div className='mb-6 w-full max-w-sm'>
												<div className='bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-xl p-6 text-center shadow-sm'>
													<div className='flex items-center justify-center gap-2 mb-2'>
														<Wallet className='h-5 w-5 text-primary' />
														<span className='text-sm font-medium text-muted-foreground'>
															Valor da Cobrança
														</span>
													</div>
													<div className='text-3xl font-bold text-primary mb-1'>
														{formatCurrency(pixData.amount)}
													</div>
													<div className='text-xs text-muted-foreground'>
														Pagamento via PIX
													</div>
												</div>
											</div>
										)}

										<div className='bg-white p-4 rounded-lg mb-4 shadow-sm border'>
											{/* Verificar se é uma string base64, URL ou gerar QR code a partir do payload */}
											{typeof pixData.pixQrCode === 'string' && (
												<>
													{pixData.pixQrCode.startsWith('data:') ? (
														// É uma imagem base64 com prefixo data:
														<img
															src={pixData.pixQrCode}
															alt='QR Code PIX'
															width={200}
															height={200}
															className='rounded-lg'
														/>
													) : pixData.pixQrCode.startsWith('http') ? (
														// É uma URL externa
														<img
															src={pixData.pixQrCode}
															alt='QR Code PIX'
															width={200}
															height={200}
															className='rounded-lg'
															onError={(e) => {
																console.error('Erro ao carregar imagem do QR code:', e);
																// Fallback para gerar QR code a partir do payload
																if (pixData.pixPayload) {
																	e.currentTarget.style.display = 'none';
																	e.currentTarget.nextElementSibling?.classList.remove('hidden');
																}
															}}
														/>
													) : (
														// Tentar como base64 sem prefixo
														<img
															src={`data:image/png;base64,${pixData.pixQrCode}`}
															alt='QR Code PIX'
															width={200}
															height={200}
															className='rounded-lg'
															onError={(e) => {
																console.error('Erro ao carregar imagem base64 do QR code:', e);
																// Fallback para gerar QR code a partir do payload
																if (pixData.pixPayload) {
																	e.currentTarget.style.display = 'none';
																	e.currentTarget.nextElementSibling?.classList.remove('hidden');
																}
															}}
														/>
													)}

													{/* Fallback: QR code gerado a partir do payload */}
													{pixData.pixPayload && (
														<div className="hidden">
															<QRCodeSVG
																value={pixData.pixPayload}
																size={200}
																bgColor={'#ffffff'}
																fgColor={'#000000'}
																level={'L'}
																className='rounded-lg'
															/>
														</div>
													)}
												</>
											)}
										</div>

										<div className='flex flex-col gap-2 items-center'>
											<p className='text-sm text-muted-foreground font-medium'>
												Escaneie o QR Code com o app do seu banco
											</p>
											{pixData.txid && (
												<p className='text-xs text-muted-foreground'>
													ID: {pixData.txid}
												</p>
											)}

										</div>
									</div>
								) : pixData.pixPayload ? (
									<div className='flex flex-col items-center justify-center'>
										{/* Card destacado com o valor da cobrança */}
										{pixData.amount && (
											<div className='mb-6 w-full max-w-sm'>
												<div className='bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-xl p-6 text-center shadow-sm'>
													<div className='flex items-center justify-center gap-2 mb-2'>
														<Wallet className='h-5 w-5 text-primary' />
														<span className='text-sm font-medium text-muted-foreground'>
															Valor da Cobrança
														</span>
													</div>
													<div className='text-3xl font-bold text-primary mb-1'>
														{formatCurrency(pixData.amount)}
													</div>
													<div className='text-xs text-muted-foreground'>
														Pagamento via PIX
													</div>
												</div>
											</div>
										)}

										<div className='bg-white p-4 rounded-lg mb-4 shadow-sm border'>
											{/* Verificar se é uma string válida para QR code */}
											{typeof pixData.pixPayload === 'string' && (
												<QRCodeSVG
													value={pixData.pixPayload}
													size={200}
													bgColor={'#ffffff'}
													fgColor={'#000000'}
													level={'L'}
												/>
											)}
										</div>

										<div className='flex flex-col gap-2 items-center'>
											<p className='text-sm text-muted-foreground font-medium'>
												Escaneie o QR Code com o app do seu banco
											</p>
											{pixData.txid && (
												<p className='text-xs text-muted-foreground'>
													ID: {pixData.txid}
												</p>
											)}

										</div>
									</div>
								) : (
									<div className='flex flex-col items-center justify-center py-4'>
										{/* Card destacado com o valor da cobrança mesmo sem QR code */}
										{pixData.amount && (
											<div className='mb-6 w-full max-w-sm'>
												<div className='bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-xl p-6 text-center shadow-sm'>
													<div className='flex items-center justify-center gap-2 mb-2'>
														<Wallet className='h-5 w-5 text-primary' />
														<span className='text-sm font-medium text-muted-foreground'>
															Valor da Cobrança
														</span>
													</div>
													<div className='text-3xl font-bold text-primary mb-1'>
														{formatCurrency(pixData.amount)}
													</div>
													<div className='text-xs text-muted-foreground'>
														Pagamento via PIX
													</div>
												</div>
											</div>
										)}

										<div className='bg-amber-50 p-4 rounded-lg mb-4 text-amber-800 text-sm border border-amber-200'>
											<div className='flex items-center gap-2'>
												<Info className='h-5 w-5 text-amber-500' />
												<p>
													QR Code não disponível. Utilize o código PIX abaixo
													para pagamento.
												</p>
											</div>
										</div>
									</div>
								)}
							</div>

							{pixData.pixPayload && (
								<div className='space-y-2'>
									<div className='flex justify-between items-center'>
										<p className='text-sm font-medium'>
											Código PIX para copiar e colar:
										</p>
										<Button
											variant='ghost'
											size='sm'
											className='h-8 px-2'
											onClick={() => {
												if (typeof pixData.pixPayload === 'string') {
													navigator.clipboard.writeText(pixData.pixPayload);
													toast({
														title: 'Código copiado',
														description:
															'O código PIX foi copiado para a área de transferência',
														variant: 'success',
													});
												} else {
													toast({
														title: 'Erro ao copiar',
														description: 'Não foi possível copiar o código PIX',
														variant: 'error',
													});
												}
											}}
										>
											<Copy className='h-3.5 w-3.5 mr-1' />
											Copiar
										</Button>
									</div>
									<div className='bg-muted p-3 rounded-md text-sm break-all border border-border'>
										{typeof pixData.pixPayload === 'string'
											? pixData.pixPayload
											: 'Código PIX não disponível'}
									</div>
								</div>
							)}

							<div className='bg-blue-500/10 dark:bg-blue-500/5 border border-blue-500/20 rounded-lg p-4 text-sm mt-6'>
								<div className='flex items-start gap-3'>
									<Info className='h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5' />
									<p className='text-foreground'>
										O pagamento será processado automaticamente. Você pode
										fechar esta janela e acompanhar o status na lista de
										transações.
									</p>
								</div>
							</div>

							<DialogFooter className='flex flex-row gap-3 sm:justify-between pt-4'>
								<Button
									type='button'
									variant='outline'
									onClick={() => {
										onClose();
										// Recarregar a página após fechar o modal para atualizar a lista de transações
										window.location.reload();
									}}
									className='flex-1 h-12 rounded-lg'
								>
									Fechar
								</Button>
								{pixData.pixPayload && (
									<Button
										type='button'
										className='flex-1 bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-12 rounded-lg shadow-sm'
										onClick={() => {
											if (typeof pixData.pixPayload === 'string') {
												navigator.clipboard.writeText(pixData.pixPayload);
												toast({
													title: 'Código copiado',
													description:
														'O código PIX foi copiado para a área de transferência',
													variant: 'success',
												});
											} else {
												toast({
													title: 'Erro ao copiar',
													description: 'Não foi possível copiar o código PIX',
													variant: 'error',
												});
											}
										}}
									>
										<Copy className='h-4 w-4' />
										Copiar e Pagar com PIX
									</Button>
								)}
							</DialogFooter>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
