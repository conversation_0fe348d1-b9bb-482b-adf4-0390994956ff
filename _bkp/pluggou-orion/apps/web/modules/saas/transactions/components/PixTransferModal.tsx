"use client";

import { useState, use<PERSON><PERSON>back, useEffect, useRef } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@ui/components/dialog";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Tabs, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Alert, AlertDescription } from "@ui/components/alert";
import {
  AlertCircle,
  AlertTriangle,
  ArrowLeftRight,
  Ban,
  Check,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  CircleAlert,
  CircleAlertIcon,
  Clock,
  Copy,
  Info,
  Loader2,
  Send,
  ShieldCheck,
  Wallet,
  XCircle,
  Zap,
} from "lucide-react";
import { cn } from "@ui/lib";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@ui/components/form";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { IMaskInput } from "react-imask";
import { useToast } from "@ui/hooks/use-toast";
import { TransferTwoFactorForm } from "./TransferTwoFactorForm";

type PixTransferModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
};

type BalanceInfo = {
  available: number;
  custody: number;
  blocked: number;
};

type ModalStep = 'form' | 'twoFactor' | 'processing' | 'success' | 'error';

const pixKeyTypes = ["cpf", "cnpj", "telefone", "email", "chave-aleatoria"] as const;
type PixKeyType = typeof pixKeyTypes[number];

// Mapeamento de tipos de chave PIX para os valores esperados pela API
const pixKeyTypeMapping: Record<PixKeyType, string> = {
  "cpf": "CPF",
  "cnpj": "CNPJ",
  "telefone": "PHONE",
  "email": "EMAIL",
  "chave-aleatoria": "RANDOM"
};

type Gateway = {
  id: string;
  name: string;
  type: string;
  isDefault: boolean;
  isActive: boolean;
  canReceive: boolean;
  canSend: boolean;
};

export function PixTransferModal({ isOpen, onClose, onSuccess }: PixTransferModalProps) {
  // Usar o hook de traduções
  const translations = useTranslations();
  // Função de tradução com fallback para evitar erros
  const t = (key: string, params?: any) => {
    try {
      // @ts-ignore - Ignorar erro de tipo para permitir qualquer chave
      return translations(key, params) || '';
    } catch (error) {
      console.warn(`Tradução não encontrada para a chave: ${key}`);
      return '';
    }
  };
  const [selectedKeyType, setSelectedKeyType] = useState<PixKeyType>("cpf");
  const [isLoading, setIsLoading] = useState(false);
  const [defaultGateway, setDefaultGateway] = useState<Gateway | null>(null);
  const [isLoadingGateways, setIsLoadingGateways] = useState(false);
  const { toast } = useToast();
  const { activeOrganization } = useActiveOrganization();

  // Estado para armazenar o saldo da organização
  const [balanceInfo, setBalanceInfo] = useState<BalanceInfo>({
    available: 0,
    custody: 0,
    blocked: 0
  });
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);
  const [currentStep, setCurrentStep] = useState<ModalStep>('form');
  const [transferData, setTransferData] = useState<any>(null);
  const [transferResult, setTransferResult] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [transferFee, setTransferFee] = useState<number>(0); // Taxa fixa de transferência

  // Ref to track if we've already shown an error
  const errorShownRef = useRef(false);

  // Ref to track whether initialization has run for this modal opening
  const hasInitializedRef = useRef(false);

  // Reset the error shown ref when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      errorShownRef.current = false;
      hasInitializedRef.current = false;
    }
  }, [isOpen]);

  // Função para carregar o gateway para transferências
  const loadDefaultGateway = useCallback(async () => {
    if (!activeOrganization?.id) {
      console.log("No active organization");
      return;
    }

    setIsLoadingGateways(true);
    try {
      console.log(`Loading gateways for organization: ${activeOrganization.id}`);

      // Primeiro, buscar as taxas da organização para obter a taxa fixa de transferência
      try {
        const taxesResponse = await fetch(`/api/organizations/${activeOrganization.id}/taxes`, {
          credentials: 'include',
        });

        if (taxesResponse.ok) {
          const taxesData = await taxesResponse.json();
          console.log("Organization taxes:", taxesData);

          if (taxesData.pixTransferFixedFee !== undefined) {
            console.log("Organization transfer fixed fee:", taxesData.pixTransferFixedFee);
            setTransferFee(taxesData.pixTransferFixedFee);
          }
        } else {
          console.warn("Failed to fetch organization taxes:", await taxesResponse.text());
        }
      } catch (taxesError) {
        console.error("Error fetching organization taxes:", taxesError);
      }

      // Verificar especificamente o gateway Transfeera (preferencial para transferências)
      const transfeeraResponse = await fetch(`/api/payments/gateways/check-transfeera?organizationId=${activeOrganization.id}`, {
        credentials: 'include',
      });

      if (transfeeraResponse.ok) {
        const transfeeraData = await transfeeraResponse.json();
        console.log("Transfeera gateway check:", transfeeraData);

        if (transfeeraData.success && transfeeraData.gateway.isConfiguredCorrectly) {
          // Se o Transfeera está configurado corretamente, usá-lo
          console.log("Using Transfeera gateway for transfers");
          setDefaultGateway({
            id: transfeeraData.gateway.id,
            name: transfeeraData.gateway.name,
            type: transfeeraData.gateway.type,
            isActive: transfeeraData.gateway.isActive,
            canSend: transfeeraData.gateway.canSend,
            canReceive: transfeeraData.gateway.canReceive || false,
            isDefault: transfeeraData.gateway.isDefaultForOrg
          });

          // Obter a taxa fixa de transferência do gateway apenas se não conseguimos obter da organização
          // ou se a taxa da organização é zero (não configurada)
          if (transfeeraData.gateway.pixTransferFixedFee !== undefined && transferFee === 0) {
            console.log("Gateway transfer fixed fee:", transfeeraData.gateway.pixTransferFixedFee);
            setTransferFee(transfeeraData.gateway.pixTransferFixedFee);
          }

          // Também atualizar o saldo se disponível
          if (transfeeraData.balance) {
            setBalanceInfo({
              available: transfeeraData.balance.available || 0,
              custody: transfeeraData.balance.pending || 0,
              blocked: transfeeraData.balance.reserved || 0
            });
            setIsLoadingBalance(false);
          }

          setIsLoadingGateways(false);
          return;
        }
      }

      // Se não encontrou o Transfeera ou ele não está configurado corretamente,
      // buscar todos os gateways que podem enviar dinheiro
      const response = await fetch(`/api/payments/gateways/list?organizationId=${activeOrganization.id}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log(`Response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error loading gateways: ${response.status} - ${errorText}`);
        throw new Error(`Error loading payment gateways: ${response.status}`);
      }

      const data = await response.json();
      console.log("Gateway data:", data);

      if (data.success && data.sendEnabledGateways && data.sendEnabledGateways.length > 0) {
        // Use the first gateway that can send money (already ordered by priority)
        console.log("Send-enabled gateway found:", data.sendEnabledGateways[0]);
        setDefaultGateway(data.sendEnabledGateways[0]);
      } else {
        // If no gateways can send money
        console.log("No gateways found that can send money");

        // Only show toast if we haven't shown an error yet for this modal session
        if (!errorShownRef.current) {
          errorShownRef.current = true; // Mark that we've shown an error
          toast({
            title: 'No gateway for transfers',
            description: 'Contact the administrator to configure a gateway that supports sending money for PIX transfers.',
            variant: 'error'
          });
        }
      }
    } catch (error) {
      console.error("Error loading gateways:", error);

      // Only show toast if we haven't shown an error yet for this modal session
      if (!errorShownRef.current) {
        errorShownRef.current = true; // Mark that we've shown an error
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Could not load payment gateways',
          variant: 'error'
        });
      }
    } finally {
      setIsLoadingGateways(false);
    }
  }, [activeOrganization?.id, toast, errorShownRef]);

  // Load balance function
  const loadBalance = useCallback(async () => {
    if (!activeOrganization?.id) return;

    setIsLoadingBalance(true);
    try {
      console.log(`Loading balance for organization: ${activeOrganization.id}`);

      // Fazer a chamada real à API
      const response = await fetch(`/api/payments/balance?organizationId=${activeOrganization.id}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error loading balance: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log("Balance data:", data);

      if (data.success) {
        setBalanceInfo({
          available: data.available || 0,
          custody: data.pending || 0,
          blocked: data.reserved || 0
        });
      }
    } catch (error) {
      console.error("Error loading balance:", error);

      // Only show toast if we haven't shown an error yet for this modal session
      if (!errorShownRef.current) {
        errorShownRef.current = true; // Mark that we've shown an error

        let title = t("transactions.errors.genericErrorTitle") || 'Erro';
        let description = t("transactions.errors.loadBalanceError") || 'Não foi possível carregar os dados de saldo';

        if (error instanceof Error && error.message.includes('Error loading balance: 403')) {
          try {
            // Extract the JSON part from the error message
            const jsonString = error.message.substring(error.message.indexOf('{'));
            const errorData = JSON.parse(jsonString);
            if (errorData.error === "Organization is not approved") {
              title = t("transactions.errors.orgNotApprovedTitle") || "Empresa não aprovada";
              description = t("transactions.errors.orgNotApprovedDesc") || "Sua empresa ainda está em análise. O saldo estará disponível após a aprovação.";
            }
          } catch (parseError) {
            console.error("Could not parse balance error message:", parseError);
            // Keep generic message if parsing fails
          }
        }

        toast({
          title: title,
          description: description,
          variant: 'error'
        });
      }
    } finally {
      setIsLoadingBalance(false);
    }
  }, [activeOrganization?.id, toast, t]);

  // Estado para armazenar se o usuário tem 2FA habilitado
  const [has2FAEnabled, setHas2FAEnabled] = useState<boolean | null>(null);
  // Estado para controlar o carregamento da verificação 2FA
  const [, setIs2FALoading] = useState(false);

  // Função para verificar se o usuário tem 2FA habilitado
  const check2FAStatus = useCallback(async () => {
    if (!activeOrganization?.id) return;

    setIs2FALoading(true);
    try {
      console.log("Verificando status 2FA do usuário...");

      const response = await fetch('/api/auth/two-factor/check', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Erro ao verificar status 2FA: ${response.status}`);
      }

      const data = await response.json();
      console.log("Status 2FA:", data);

      setHas2FAEnabled(data.twoFactorEnabled);

      // Se o usuário não tem 2FA habilitado, mostrar um aviso
      if (!data.twoFactorEnabled && !errorShownRef.current) {
        errorShownRef.current = true;
        toast({
          title: "Autenticação em duas etapas necessária",
          description: "Para realizar transferências, você precisa ativar a autenticação em duas etapas nas configurações da sua conta.",
          variant: "error",
          duration: 10000 // 10 segundos
        });
      }
    } catch (error) {
      console.error("Erro ao verificar status 2FA:", error);
    } finally {
      setIs2FALoading(false);
    }
  }, [activeOrganization?.id, toast]);

  // Função para verificar se os saques estão bloqueados
  const checkWithdrawalBlocking = useCallback(async () => {
    if (!activeOrganization?.id) return { blocked: false };

    try {
      console.log("Verificando bloqueio de saques...");

      const response = await fetch(`/api/organizations/${activeOrganization.id}/withdrawal-blocking`, {
        credentials: 'include',
      });

      if (!response.ok) {
        console.warn("Não foi possível verificar bloqueio de saques:", response.status);
        return { blocked: false };
      }

      const data = await response.json();
      console.log("Status de bloqueio de saques:", data);

      return data;
    } catch (error) {
      console.error("Erro ao verificar bloqueio de saques:", error);
      return { blocked: false };
    }
  }, [activeOrganization?.id]);

  // Carregar gateway padrão e saldo quando o modal é aberto
  useEffect(() => {
    const initializeData = async () => {
      // Make sure this only runs once per modal opening
      if (isOpen && activeOrganization?.id && !hasInitializedRef.current) {
        // Mark as initialized immediately to prevent re-execution
        hasInitializedRef.current = true;

        console.log("Initializing data for modal...");

        try {
          // Reset error state
          errorShownRef.current = false;

          // Load gateway, balance and 2FA status concurrently for faster loading
          await Promise.all([
            loadDefaultGateway(),
            loadBalance(),
            check2FAStatus()
          ]);

          console.log("Data initialization complete!");
        } catch (error) {
          // Just log the error - actual handling is done in the individual functions
          console.error("Error during modal data initialization:", error);
        }
      }
    };

    initializeData();

    // No cleanup needed here anymore, as we handle reset in the other useEffect
  }, [isOpen, activeOrganization?.id, loadDefaultGateway, loadBalance, check2FAStatus]);

  // Função para converter valor formatado em número
  const parseAmountValue = (formattedAmount: string): number => {
    if (!formattedAmount) return 0;

    // Para valores formatados pelo IMaskInput com formato brasileiro:
    // - Remover espaços e caracteres especiais exceto dígitos, vírgulas e pontos
    // - Se há vírgula, ela é o separador decimal
    // - Pontos antes da vírgula são separadores de milhares
    let cleanValue = formattedAmount.replace(/[^\d.,]/g, '');

    // Se há vírgula, tratar como separador decimal brasileiro
    if (cleanValue.includes(',')) {
      // Dividir em parte inteira e decimal
      const parts = cleanValue.split(',');
      if (parts.length === 2) {
        // Remover pontos da parte inteira (separadores de milhares)
        const integerPart = parts[0].replace(/\./g, '');
        const decimalPart = parts[1];
        cleanValue = `${integerPart}.${decimalPart}`;
      }
    } else {
      // Se não há vírgula, remover pontos (podem ser separadores de milhares)
      // Mas manter se for o último ponto com 2 dígitos após (separador decimal)
      const lastDotIndex = cleanValue.lastIndexOf('.');
      if (lastDotIndex !== -1 && cleanValue.length - lastDotIndex === 3) {
        // Último ponto com 2 dígitos após - provavelmente separador decimal
        const beforeDot = cleanValue.substring(0, lastDotIndex).replace(/\./g, '');
        const afterDot = cleanValue.substring(lastDotIndex + 1);
        cleanValue = `${beforeDot}.${afterDot}`;
      } else {
        // Remover todos os pontos (separadores de milhares)
        cleanValue = cleanValue.replace(/\./g, '');
      }
    }

    const result = parseFloat(cleanValue);
    return isNaN(result) ? 0 : result;
  };

  // Form schema with improved validation
  const formSchema = z.object({
    pixKey: z.string().min(1, { message: t("transactions.pixTransfer.validation.pixKeyRequired") || "Chave PIX é obrigatória" })
      .refine((val) => {
        // Use shared PIX key validation utility for consistency
        const { validatePixKey } = require("@repo/utils/src/pix-validation");

        // Map frontend key types to backend types
        const keyTypeMap: Record<string, string> = {
          "cpf": "CPF",
          "cnpj": "CNPJ",
          "telefone": "PHONE",
          "email": "EMAIL",
          "chave-aleatoria": "RANDOM"
        };

        const backendKeyType = keyTypeMap[selectedKeyType];
        if (!backendKeyType) return true;

        return validatePixKey(val, backendKeyType);
      }, { message: t("transactions.pixTransfer.validation.invalidPixKey") || "Formato de chave PIX inválido" }),
    amount: z.string()
      .min(1, { message: t("transactions.pixTransfer.validation.amountRequired") || "Valor é obrigatório" })
      .refine(
        (val) => {
          try {
            const numValue = parseAmountValue(val);
            return !isNaN(numValue) && numValue > 0;
          } catch (e) {
            return false;
          }
        },
        { message: t("transactions.pixTransfer.validation.invalidAmount") || "Valor inválido" }
      )
      // Removendo a validação de saldo aqui para tratar no onSubmit
      // Isso evita problemas de validação quando o saldo é carregado assincronamente
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      pixKey: "",
      amount: ""
    }
  });

  // Reset form when key type changes
  const handleKeyTypeChange = (value: string) => {
    setSelectedKeyType(value as PixKeyType);
    form.setValue("pixKey", "");
    form.clearErrors("pixKey");
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    console.log("Iniciando submissão do formulário", values);

    if (!activeOrganization?.id) {
      console.error("Organização não encontrada");
      toast({
        title: "Erro",
        description: "Organização não encontrada",
        variant: "error"
      });
      return;
    }

    // Verificar se há saldo disponível antes de qualquer outra verificação
    try {
      // Converter o valor da transferência para número usando a função corrigida
      const requestedAmount = parseAmountValue(values.amount);

      // Calcular o valor total considerando a taxa fixa de transferência
      const totalAmount = requestedAmount + transferFee;

      console.log("Verificando saldo:", {
        available: balanceInfo.available,
        requested: requestedAmount,
        fee: transferFee,
        totalRequired: totalAmount,
        originalAmount: values.amount,
        parsedAmount: requestedAmount,
        totalWithFee: totalAmount
      });

      if (isNaN(requestedAmount)) {
        console.error("Valor inválido");
        toast({
          title: "Valor inválido",
          description: "Por favor, informe um valor válido para a transferência.",
          variant: "error"
        });
        return;
      }

      // Verificar se o valor solicitado é maior que zero
      if (requestedAmount <= 0) {
        console.error("Valor da transferência deve ser maior que zero", {
          requested: requestedAmount
        });

        setErrorMessage(`O valor da transferência deve ser maior que zero.`);
        setCurrentStep('error');
        return;
      }

      if (balanceInfo.available <= 0 || balanceInfo.available < totalAmount) {
        console.error("Saldo insuficiente", {
          available: balanceInfo.available,
          requested: requestedAmount,
          fee: transferFee,
          totalRequired: totalAmount
        });

        // Mostrar erro no estado do modal em vez de apenas um toast
        setErrorMessage(`Você não possui saldo suficiente para esta transferência. É necessário ter R$ ${totalAmount.toFixed(2).replace('.', ',')} disponível (valor R$ ${requestedAmount.toFixed(2).replace('.', ',')} + taxa de R$ ${transferFee.toFixed(2).replace('.', ',')}).`);
        setCurrentStep('error');

        // Adicionar log para depuração
        console.log("Alterando para estado de erro: saldo insuficiente");
        console.log("Estado atual:", currentStep);
        return;
      }
    } catch (error) {
      console.error("Erro ao verificar saldo:", error);
    }

    // Verificar se o usuário tem 2FA habilitado
    if (has2FAEnabled === false) {
      console.error("Usuário não tem 2FA habilitado");

      // Mostrar erro no estado do modal
      setErrorMessage("Para realizar transferências, você precisa ativar a autenticação em duas etapas nas configurações da sua conta.");
      setCurrentStep('error');
      return;
    }

    // Verificar se os saques estão bloqueados
    try {
      const blockingResult = await checkWithdrawalBlocking();
      if (blockingResult.blocked) {
        console.log("Saques bloqueados para a organização:", blockingResult);

        // Mostrar erro no estado do modal com a mensagem específica
        setErrorMessage(blockingResult.message || "Saques temporariamente indisponíveis para sua organização. Entre em contato com o suporte.");
        setCurrentStep('error');
        return;
      }
    } catch (error) {
      console.warn("Erro ao verificar bloqueio de saques:", error);
      // Continuar com a transferência se não conseguir verificar o bloqueio
    }

    // Verificar status da organização
    try {
      const orgResponse = await fetch(`/api/organizations/${activeOrganization.id}/status`, {
        credentials: 'include',
      });

      if (orgResponse.ok) {
        const orgData = await orgResponse.json();
        console.log("Status da organização:", orgData);

        if (orgData.status !== "APPROVED") {
          console.error("Organização não aprovada para transferências:", orgData.status);

          // Mostrar erro no estado do modal em vez de apenas um toast
          setErrorMessage("Sua organização precisa ser aprovada para realizar transferências.");
          setCurrentStep('error');
          return;
        }
      } else {
        console.warn("Não foi possível verificar o status da organização");
      }
    } catch (error) {
      console.warn("Erro ao verificar status da organização:", error);
    }

    if (!defaultGateway) {
      console.error("Nenhum gateway de pagamento configurado");

      // Mostrar erro no estado do modal em vez de apenas um toast
      setErrorMessage("Nenhum gateway de pagamento está configurado para transferências.");
      setCurrentStep('error');
      return;
    }

    // Verificar se o gateway está ativo e pode enviar dinheiro
    if (!defaultGateway.isActive || !defaultGateway.canSend) {
      console.error("Gateway não está ativo ou não pode enviar dinheiro", defaultGateway);

      // Mostrar erro no estado do modal em vez de apenas um toast
      setErrorMessage(`O gateway ${defaultGateway.name} não está disponível para transferências.`);
      setCurrentStep('error');
      return;
    }

    setIsLoading(true);
    console.log("Estado de carregamento definido como true");

    try {
      // Formatar os valores para envio usando a função corrigida
      const numericAmount = parseAmountValue(values.amount);

      // Formatar a chave PIX de acordo com o tipo
      let formattedPixKey = values.pixKey;

      // Remover caracteres especiais para diversos tipos de chave
      if (selectedKeyType === "cpf") {
        formattedPixKey = values.pixKey.replace(/\D/g, ''); // Remove tudo que não for dígito
      } else if (selectedKeyType === "cnpj") {
        formattedPixKey = values.pixKey.replace(/\D/g, ''); // Remove tudo que não for dígito
      } else if (selectedKeyType === "telefone") {
        // Tratar número de telefone de forma especial para evitar duplicação do código de país
        const phoneInput = values.pixKey.replace(/\D/g, ''); // Remove tudo que não for dígito

        // Verificar se já começa com 55 (código do Brasil)
        if (phoneInput.startsWith('55') && phoneInput.length >= 12) {
          // Já tem código do país, só adicionar o "+" no início
          formattedPixKey = `+${phoneInput}`;
        } else if (phoneInput.length <= 11) {
          // Número brasileiro sem código do país
          formattedPixKey = `+55${phoneInput}`;
        } else {
          // Outro formato de número internacional
          formattedPixKey = `+${phoneInput}`;
        }

        // Log para debug
        console.log("Formatação de telefone:", {
          original: values.pixKey,
          digitsOnly: phoneInput,
          formatted: formattedPixKey
        });
      } else if (selectedKeyType === "email") {
        formattedPixKey = values.pixKey.trim().toLowerCase(); // Email em lowercase
      } else if (selectedKeyType === "chave-aleatoria") {
        formattedPixKey = values.pixKey.replace(/[^a-zA-Z0-9-]/g, ''); // Apenas letras, números e hífen
      }

      // Preparar dados para envio
      const requestData = {
        amount: numericAmount,
        pixKey: formattedPixKey,
        pixKeyType: pixKeyTypeMapping[selectedKeyType],
        organizationId: activeOrganization.id,
        gatewayType: defaultGateway.type
      };

      // Salvar os dados da transferência para uso posterior (após 2FA se necessário)
      setTransferData(requestData);
      console.log("Dados da transferência salvos:", requestData);

      // Sempre exigir verificação 2FA para cada transferência
      console.log("Exigindo verificação 2FA para transferência");
      setIsLoading(false);
      setCurrentStep('twoFactor');

      toast({
        title: "Verificação em duas etapas",
        description: "Por favor, insira o código de verificação para continuar com a transferência",
        variant: "default"
      });

      return;
    } catch (error) {
      console.error("Erro ao preparar transferência:", error);

      toast({
        title: "Erro",
        description: "Ocorreu um erro ao preparar a transferência",
        variant: "error"
      });

      setIsLoading(false);
    }
  };

  const getPixKeyPlaceholder = () => {
    switch (selectedKeyType) {
      case "cpf":
        return "000.000.000-00";
      case "cnpj":
        return "00.000.000/0000-00";
      case "telefone":
        return "+55 (00) 00000-0000";
      case "email":
        return "<EMAIL>";
      case "chave-aleatoria":
        return "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx";
      default:
        return "";
    }
  };

  const getPixKeyMask = useCallback(() => {
    switch (selectedKeyType) {
      case "cpf":
        return "000.000.000-00";
      case "cnpj":
        return "00.000.000/0000-00";
      case "telefone":
        return "+{55} (00) 00000-0000";
      default:
        return undefined;
    }
  }, [selectedKeyType]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Função para processar a transferência (após 2FA)
  const processTransfer = async (data: any) => {
    console.log("Processando transferência com dados:", data);
    setIsLoading(true);
    setCurrentStep('processing');

    try {
      // Verificar novamente o saldo antes de prosseguir
      try {
        if (!activeOrganization?.id) {
          throw new Error("Organização não encontrada");
        }

        const balanceResponse = await fetch(`/api/payments/balance?organizationId=${activeOrganization.id}`, {
          credentials: 'include',
        });

        if (balanceResponse.ok) {
          const balanceData = await balanceResponse.json();
          console.log("Saldo atual verificado:", balanceData);

          // Converter o valor da transferência para número
          const transferAmount = parseFloat(data.amount);
          // Calcular o valor total considerando a taxa fixa de transferência
          const totalAmount = transferAmount + transferFee;

          if (balanceData.available < totalAmount) {
            console.error("Saldo insuficiente para transferência", {
              available: balanceData.available,
              requested: transferAmount,
              fee: transferFee,
              totalRequired: totalAmount
            });

            setErrorMessage(`Saldo insuficiente para realizar esta transferência. É necessário ter R$ ${totalAmount.toFixed(2).replace('.', ',')} disponível (valor R$ ${transferAmount.toFixed(2).replace('.', ',')} + taxa de R$ ${transferFee.toFixed(2).replace('.', ',')}).`);
            setCurrentStep('error');
            return;
          }

          // Não atualizar o saldo aqui - apenas após confirmação de sucesso
        }
      } catch (balanceError) {
        console.warn("Erro ao verificar saldo antes da transferência:", balanceError);
      }

      // Definir os cabeçalhos para a API
      const headers = {
        'Content-Type': 'application/json',
        // Adicionar cabeçalhos específicos para indicar solicitação de produção
        'X-Environment': 'production',
        'X-2FA-Verified': 'true',
        'X-2FA-Timestamp': new Date().toISOString(),
        'X-Domain': window.location.hostname, // Incluir hostname para depuração
      };

      // Log para depuração dos cabeçalhos
      console.log("Cabeçalhos da solicitação de transferência:", headers);

      // Fazer a chamada real à API para realizar a transferência
      const response = await fetch('/api/payments/transactions/withdraw', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ...data,
          totalAmount: parseFloat(data.amount) + transferFee, // Incluir o valor total com a taxa
          fee: transferFee, // Incluir a taxa separadamente para registro
          percentFee: 0, // Taxa percentual (0 neste caso)
          fixedFee: transferFee, // Taxa fixa
          totalFee: transferFee, // Total de taxas
          requiresTwoFactor: true, // Indicar explicitamente que esta transação passou por 2FA
          // Include additional fee information for better tracking
          feeDetails: {
            transferFee: transferFee,
            totalWithFee: parseFloat(data.amount) + transferFee
          }
        }),
        credentials: 'include',
      });

      console.log("Status da resposta:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Erro na resposta: ${response.status} - ${errorText}`);

        // Não precisamos reverter o saldo aqui, pois não o atualizamos prematuramente

        // Verificar especificamente se é o erro de 2FA antes de qualquer outro processamento
        if (errorText.includes("TWO_FACTOR_REQUIRED")) {
          console.log("Detectado erro de verificação em duas etapas");
          setIsLoading(false);

          toast({
            title: "Verificação em duas etapas",
            description: "Por favor, insira o código de verificação para continuar com a transferência",
            variant: "default"
          });

          setCurrentStep('twoFactor');
          console.log("Estado currentStep alterado para: twoFactor");
          return;
        }

        // Verificar outros erros específicos
        if (errorText.includes("Organization is not approved")) {
          setErrorMessage("Sua organização precisa ser aprovada para realizar transferências.");
          setCurrentStep('error');
          return;
        }

        if (errorText.includes("Insufficient balance")) {
          setErrorMessage("Saldo insuficiente para realizar esta transferência.");
          setCurrentStep('error');
          return;
        }

        // Verificar se é um bloqueio de saques
        if (response.status === 403) {
          try {
            const errorData = JSON.parse(errorText);
            if (errorData.message && errorData.message.includes("temporariamente indisponível")) {
              setErrorMessage(errorData.message);
              setCurrentStep('error');
              return;
            }
          } catch (parseError) {
            // Se não conseguir fazer parse, continua para o tratamento genérico
          }
        }

        // Verificar erros específicos do PLUGGOU_PIX
        if (errorText.includes("PLUGGOU_PIX") || errorText.includes("Pluggou PIX")) {
          let pluggouErrorMessage = "Erro ao processar transferência com o gateway Pluggou PIX";

          // Tentar extrair mensagens de erro mais específicas
          if (errorText.includes("Failed to process Pluggou PIX withdrawal")) {
            try {
              // Tentar extrair a mensagem de erro detalhada
              const errorMatch = errorText.match(/Pluggou PIX transfer failed \((\d+)\): (.*?)"/);
              if (errorMatch && errorMatch.length >= 3) {
                const errorCode = errorMatch[1];
                const errorDetails = errorMatch[2];
                pluggouErrorMessage = `Erro no gateway Pluggou PIX (${errorCode}): ${errorDetails}`;
                console.error("Erro detalhado do Pluggou PIX:", {
                  code: errorCode,
                  message: errorDetails
                });
              }
            } catch (parseError) {
              console.error("Erro ao extrair detalhes do erro Pluggou PIX:", parseError);
            }
          }

          setErrorMessage(pluggouErrorMessage);
          setCurrentStep('error');
          return;
        }

        let errorMessage = 'Erro ao processar transferência';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
          console.error("Dados do erro:", errorData);

          // Verificar se há informações específicas sobre o erro do gateway
          if (errorData.gatewayError) {
            errorMessage = `Erro no gateway de pagamento: ${errorData.gatewayError}`;
          }

          // Verificar se há um código de erro específico
          if (errorData.errorCode) {
            errorMessage = `Erro ${errorData.errorCode}: ${errorMessage}`;
          }

          if (errorMessage.includes("Headers are required")) {
            errorMessage = "Erro de autenticação. Por favor, atualize a página e tente novamente.";
          } else if (errorMessage.includes("BAD_REQUEST")) {
            errorMessage = "Requisição inválida. Verifique os dados da transferência.";
          } else if (errorMessage.includes("Unauthorized") || errorMessage.includes("Não autorizado")) {
            errorMessage = "Sessão expirada. Por favor, faça login novamente.";
            setTimeout(() => {
              window.location.href = '/auth/login';
            }, 2000);
          }
        } catch (parseError) {
          console.error("Erro ao analisar resposta de erro:", parseError);
        }

        setErrorMessage(errorMessage);
        setCurrentStep('error');
        throw new Error(errorMessage);
      }

      // Se chegou aqui, a resposta foi bem-sucedida
      let responseData;
      try {
        const responseText = await response.text();
        console.log("Resposta bruta:", responseText);
        responseData = responseText ? JSON.parse(responseText) : {};
        console.log("Dados da resposta:", responseData);
      } catch (parseError) {
        console.error("Erro ao analisar resposta:", parseError);
        throw new Error("Erro ao processar resposta do servidor");
      }

      // Atualizar o estado com os dados da transferência bem-sucedida
      setTransferResult(responseData);

      // Verificar se temos um ID de transação e sincronizar manualmente
      if (responseData && responseData.id) {
        console.log("Tentando sincronizar status da transação:", responseData.id);

        try {
          // Primeiro, registrar explicitamente o mapeamento entre transferId e transactionId
          if (responseData.externalId) {
            console.log("Registrando mapeamento de IDs", {
              transactionId: responseData.id,
              externalId: responseData.externalId,
              organizationId: activeOrganization?.id
            });

            // Chamar API para registrar mapeamento explícito
            const mappingResponse = await fetch('/api/payments/transactions/map-external-id', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                transactionId: responseData.id,
                externalId: responseData.externalId,
                gatewayType: 'TRANSFEERA',
                organizationId: activeOrganization?.id
              }),
              credentials: 'include',
            });

            if (mappingResponse.ok) {
              console.log("Mapeamento de IDs registrado com sucesso");

              // Single status check after a delay instead of aggressive polling
              setTimeout(async () => {
                try {
                  console.log("Performing single status check after transfer creation");

                  const syncResponse = await fetch(`/api/payments/transactions/${responseData.id}/sync`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      organizationId: activeOrganization?.id,
                      force: true,
                      forceApproved: true
                    }),
                    credentials: 'include',
                  });

                  if (syncResponse.ok) {
                    console.log("Status sync completed successfully");
                    await loadBalance();
                  }
                } catch (error) {
                  console.error("Error in status sync:", error);
                }
              }, 5000); // Single check after 5 seconds
            } else {
              console.warn("Falha ao registrar mapeamento de IDs");
            }
          } else {
            console.warn("ID externo não encontrado na resposta:", responseData);
          }

          // Status sync is already handled above, no need for additional sync calls
          console.log("Transfer created successfully, status sync will be handled automatically");
        } catch (syncError) {
          console.error("Erro ao sincronizar status manualmente:", syncError);
        }
      } else {
        console.warn("ID de transação não encontrado na resposta:", responseData);
      }

      // Mostrar o estado de sucesso
      setCurrentStep('success');

      toast({
        title: "Sucesso",
        description: "Transferência iniciada com sucesso",
        variant: "success"
      });

      // Aguardar 3 segundos antes de fechar o modal ou resetar o estado
      setTimeout(() => {
        if (onSuccess) {
          console.log("Chamando callback de sucesso");
          onSuccess(responseData);
        }

        // Resetar o estado do modal para o formulário inicial
        setCurrentStep('form');
        form.reset();

        // Fechar o modal após mostrar o sucesso
        console.log("Fechando modal");
        onClose();
      }, 3000);

    } catch (error: any) {
      console.error("Erro ao processar transferência:", error);
      setErrorMessage(error.message || "Não foi possível processar a transferência. Tente novamente.");
      setCurrentStep('error');

      toast({
        title: "Erro na transferência",
        description: error.message || "Não foi possível processar a transferência. Tente novamente.",
        variant: "error"
      });

      // Voltar para o formulário após 3 segundos
      setTimeout(() => {
        setCurrentStep('form');
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para processar a transferência após verificação 2FA
  const processTransferAfter2FA = async () => {
    if (!transferData) {
      toast({
        title: "Erro",
        description: "Dados da transferência não encontrados",
        variant: "error"
      });
      return;
    }

    setCurrentStep('processing');
    setIsLoading(true);

    try {
      // Iniciar a transferência sem depender do cookie
      await processTransfer(transferData);
    } catch (error) {
      console.error('Erro ao processar transferência após 2FA:', error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao processar a transferência. Por favor, tente novamente.",
        variant: "error"
      });
      setCurrentStep('form');
    } finally {
      setIsLoading(false);
    }
  };

  // Log o estado atual sempre que ele mudar
  useEffect(() => {
    console.log("Estado atual do modal:", currentStep);

    // Adicionar logs específicos para o estado de erro
    if (currentStep === 'error') {
      console.log("Modal em estado de erro com mensagem:", errorMessage);
    }

    // Verificar cookie 2FA quando entra no estado twoFactor
    if (currentStep === 'twoFactor') {
      const getCookie = (name: string) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop()?.split(';').shift();
        return null;
      };

      const twoFactorVerifiedCookie = getCookie('2fa_verified');
      console.log("Verificando cookie 2FA no estado twoFactor:", twoFactorVerifiedCookie);
    }
  }, [currentStep, errorMessage]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[700px] p-0 overflow-hidden">
        <div className="bg-background px-6 pt-6">
          <DialogHeader className="mb-0">
            <DialogTitle className="text-center text-2xl font-bold flex items-center justify-center gap-2">
              {currentStep === 'twoFactor' ? (
                <>
                   Confirme sua identidade para continuar
                </>
              ) : (
                <>
                  <Zap className="h-6 w-6 text-primary" />
                  {t("transactions.pixTransfer.title") || "Transferência por Pix"}
                </>
              )}
            </DialogTitle>
            {currentStep === 'form' && (
              <p className="text-center text-sm text-muted-foreground">
                {t("transactions.pixTransfer.subtitle") || "Envie recursos da sua conta"}
              </p>
            )}
            {currentStep === 'twoFactor' && (
              <p className="text-center text-sm text-muted-foreground mt-2">
                Insira o código do seu aplicativo de autenticação para continuar com a transferência.
              </p>
            )}
          </DialogHeader>
        </div>
        <div className="p-6 bg-card">
        {/* Conteúdo do modal baseado no estado atual */}

        {currentStep === 'processing' && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="relative mb-6">
              <div className="w-20 h-20 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
              <Zap className="h-8 w-8 text-primary absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Processando transferência</h3>
            <p className="text-muted-foreground text-center max-w-md">
              Estamos processando sua transferência PIX. Isso pode levar alguns instantes.
            </p>
          </div>
        )}

        {currentStep === 'success' && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="bg-emerald-100 dark:bg-emerald-900/30 rounded-full p-4 mb-6">
              <CheckCircle className="h-16 w-16 text-emerald-500" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Transferência realizada!</h3>
            <p className="text-muted-foreground text-center max-w-md mb-6">
              Sua transferência PIX foi iniciada com sucesso e está sendo processada pelo gateway de pagamento.
            </p>
            <div className="bg-muted/50 rounded-lg p-4 w-full max-w-md">
              <div className="flex justify-between mb-2">
                <span className="text-muted-foreground">Valor:</span>
                <span className="font-medium">{transferData ? formatCurrency(transferData.amount) : ""}</span>
              </div>
              {transferFee > 0 && (
                <div className="flex justify-between mb-2">
                  <span className="text-muted-foreground">Taxa:</span>
                  <span className="font-medium">{formatCurrency(transferFee)}</span>
                </div>
              )}
              {transferFee > 0 && (
                <div className="flex justify-between mb-2">
                  <span className="text-muted-foreground">Total:</span>
                  <span className="font-medium">{transferData ? formatCurrency(transferData.amount + transferFee) : ""}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-muted-foreground">Chave PIX:</span>
                <span className="font-medium">{transferData?.pixKey}</span>
              </div>
            </div>
          </div>
        )}

        {currentStep === 'error' && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="bg-rose-100 dark:bg-rose-900/30 rounded-full p-4 mb-6">
              <XCircle className="h-16 w-16 text-rose-500" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Erro na transferência</h3>
            <p className="text-rose-500 text-center max-w-md mb-6">
              {errorMessage || "Ocorreu um erro ao processar sua transferência. Por favor, tente novamente."}
            </p>

            {/* Mostrar detalhes específicos para saldo insuficiente */}
            {errorMessage && errorMessage.includes("saldo insuficiente") && (
              <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6 w-full max-w-md">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-amber-800 dark:text-amber-300">Saldo insuficiente</p>
                    <p className="text-xs text-amber-700 dark:text-amber-400 mt-1">
                      Você precisa ter saldo disponível para realizar transferências.
                      Aguarde que seus pagamentos sejam processados ou entre em contato com o suporte.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Mostrar detalhes específicos para organização não aprovada */}
            {errorMessage && errorMessage.includes("organização precisa ser aprovada") && (
              <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6 w-full max-w-md">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-300">Empresa em análise</p>
                    <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
                      Sua empresa está em processo de análise. Após a aprovação,
                      você poderá realizar transferências normalmente.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Mostrar detalhes específicos para gateway não configurado */}
            {errorMessage && errorMessage.includes("gateway de pagamento") && (
              <div className="bg-orange-50 dark:bg-orange-950/30 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-6 w-full max-w-md">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-orange-800 dark:text-orange-300">Gateway não configurado</p>
                    <p className="text-xs text-orange-700 dark:text-orange-400 mt-1">
                      Entre em contato com o administrador para configurar um gateway
                      de pagamento que suporte transferências PIX.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Mostrar detalhes específicos para 2FA não habilitado */}
            {errorMessage && errorMessage.includes("autenticação em duas etapas") && (
              <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6 w-full max-w-md">
                <div className="flex items-start gap-3">
                  <ShieldCheck className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-300">Autenticação em duas etapas necessária</p>
                    <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
                      Para sua segurança, é necessário ativar a autenticação em duas etapas
                      nas configurações da sua conta antes de realizar transferências.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Mostrar detalhes específicos para erros do Pluggou PIX */}
            {errorMessage && (errorMessage.includes("Pluggou PIX") || errorMessage.includes("gateway Pluggou")) && (
              <div className="bg-purple-50 dark:bg-purple-950/30 border border-purple-200 dark:border-purple-800 rounded-lg p-4 mb-6 w-full max-w-md">
                <div className="flex items-start gap-3">
                  <CircleAlert className="h-5 w-5 text-purple-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-purple-800 dark:text-purple-300">Erro no gateway de pagamento</p>
                    <p className="text-xs text-purple-700 dark:text-purple-400 mt-1">
                      Ocorreu um erro ao processar sua transferência no gateway Pluggou PIX.
                      Verifique os dados da transferência e tente novamente. Se o problema persistir,
                      entre em contato com o suporte.
                    </p>
                    {errorMessage.includes("(") && errorMessage.includes("):") && (
                      <p className="text-xs font-medium text-purple-800 dark:text-purple-300 mt-2 border-t border-purple-200 dark:border-purple-700 pt-2">
                        Detalhes: {errorMessage}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={() => {
                setCurrentStep('form');
                setErrorMessage("");
              }}
              variant="outline"
              className="px-8"
            >
              Tentar novamente
            </Button>
          </div>
        )}

        {currentStep === 'twoFactor' ? (
          <TransferTwoFactorForm
            onSuccess={processTransferAfter2FA}
            onCancel={() => setCurrentStep('form')}
          />
        ) : currentStep === 'form' && (
          <>
          {/* Balance Information */}
          <div className="grid grid-cols-3 mb-6 gap-4 text-center text-sm">
            <div className="rounded-lg border border-border bg-card p-4 shadow-sm transition-all hover:shadow-md">
              <div className="bg-emerald-500/90 dark:bg-emerald-500/80 rounded-full p-2 w-8 h-8 mx-auto mb-2 flex items-center justify-center">
                <Wallet className="h-4 w-4 text-white" />
              </div>
              {isLoadingBalance ? (
                <div className="animate-pulse h-6 bg-muted rounded"></div>
              ) : (
                <p className="font-bold text-foreground">{formatCurrency(balanceInfo.available)}</p>
              )}
              <p className="text-xs text-muted-foreground">{t("transactions.pixTransfer.available") || "Disponível"}</p>
            </div>
            <div className="rounded-lg border border-border bg-card p-4 shadow-sm transition-all hover:shadow-md">
              <div className="bg-blue-500/90 dark:bg-blue-500/80 rounded-full p-2 w-8 h-8 mx-auto mb-2 flex items-center justify-center">
                <ArrowLeftRight className="h-4 w-4 text-white" />
              </div>
              {isLoadingBalance ? (
                <div className="animate-pulse h-6 bg-muted rounded"></div>
              ) : (
                <p className="font-bold text-foreground">{formatCurrency(balanceInfo.custody)}</p>
              )}
              <p className="text-xs text-muted-foreground">{t("transactions.pixTransfer.custody") || "Custódia"}</p>
            </div>
            <div className="rounded-lg border border-border bg-card p-4 shadow-sm transition-all hover:shadow-md">
              <div className="bg-rose-500/90 dark:bg-rose-500/80 rounded-full p-2 w-8 h-8 mx-auto mb-2 flex items-center justify-center">
                <Ban className="h-4 w-4 text-white" />
              </div>
              {isLoadingBalance ? (
                <div className="animate-pulse h-6 bg-muted rounded"></div>
              ) : (
                <p className="font-bold text-foreground">{formatCurrency(balanceInfo.blocked)}</p>
              )}
              <p className="text-xs text-muted-foreground">{t("transactions.pixTransfer.blocked") || "Bloqueios"}</p>
            </div>
          </div>

          {/* Warning Message */}
          {balanceInfo.blocked > 0 && (
            <div className="bg-amber-500/10 dark:bg-amber-500/5 border border-amber-500/20 rounded-lg my-6 p-4 text-sm mb-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                <p className="text-foreground">
                  {t("transactions.pixTransfer.warning") || "É necessário que seu saldo esteja maior que o valor dos bloqueios cautelares"}
                </p>
              </div>
            </div>
          )}

          {/* {defaultGateway && defaultGateway.isActive && defaultGateway.canSend && (
            <div className="flex items-center justify-between px-4 py-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center gap-2">
                <ArrowLeftRight className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-700">Gateway para transferências:</span>
              </div>
              <span className="text-sm font-medium text-blue-800">{defaultGateway.name}</span>
            </div>
          )} */}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* PIX Key Type Selector */}
              <Tabs
                defaultValue="cpf"
                value={selectedKeyType}
                onValueChange={handleKeyTypeChange}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-5 rounded-lg overflow-hidden border border-border p-0.5 bg-background">
                  <TabsTrigger
                    value="cpf"
                    className="text-xs py-2 rounded-md m-0.5 data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:font-medium"
                  >
                    CPF
                  </TabsTrigger>
                  <TabsTrigger
                    value="cnpj"
                    className="text-xs py-2 rounded-md m-0.5 data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:font-medium"
                  >
                    CNPJ
                  </TabsTrigger>
                  <TabsTrigger
                    value="telefone"
                    className="text-xs py-2 rounded-md m-0.5 data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:font-medium"
                  >
                    {t("transactions.pixTransfer.phone") || "Telefone"}
                  </TabsTrigger>
                  <TabsTrigger
                    value="email"
                    className="text-xs py-2 rounded-md m-0.5 data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:font-medium"
                  >
                    Email
                  </TabsTrigger>
                  <TabsTrigger
                    value="chave-aleatoria"
                    className="text-xs py-2 rounded-md m-0.5 data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:font-medium"
                  >
                    {t("transactions.pixTransfer.randomKey") || "Chave aleatória"}
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* PIX Key Input */}
              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">
                  {selectedKeyType === "cpf" ? "Chave CPF" :
                   selectedKeyType === "cnpj" ? "Chave CNPJ" :
                   selectedKeyType === "telefone" ? "Chave Telefone" :
                   selectedKeyType === "email" ? "Chave Email" :
                   "Chave Aleatória"}
                </p>
                <FormField
                  control={form.control}
                  name="pixKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          {selectedKeyType === "email" && (
                            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">@</span>
                          )}
                          {getPixKeyMask() ? (
                            <IMaskInput
                              {...field}
                              mask={getPixKeyMask() || ""}
                              unmask={false}
                              placeholder={getPixKeyPlaceholder()}
                              className={cn(
                                "flex h-12 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                                selectedKeyType === "email" && "pl-8"
                              )}
                              onAccept={(value) => field.onChange(value)}
                            />
                          ) : (
                            <Input
                              placeholder={getPixKeyPlaceholder()}
                              {...field}
                              className={cn(
                                "w-full h-12 rounded-lg border-input focus:border-primary focus:ring-primary/20 bg-background shadow-sm",
                                selectedKeyType === "email" && "pl-8"
                              )}
                            />
                          )}
                        </div>
                      </FormControl>
                      <FormMessage className="text-destructive" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Amount Input */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-foreground">{t("transactions.pixTransfer.amount") || "Valor"}</p>
                  {transferFee > 0 && (
                    <p className="text-xs text-muted-foreground">
                      Taxa: {formatCurrency(transferFee)}
                    </p>
                  )}
                </div>
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">R$</span>
                          <IMaskInput
                            {...field}
                            mask={Number}
                            radix=","
                            thousandsSeparator="."
                            mapToRadix={["."]}
                            scale={2}
                            normalizeZeros={true}
                            padFractionalZeros={true}
                            placeholder="0,00"
                            className="flex h-12 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-10"
                            onAccept={(value) => {
                              field.onChange(value);
                              // Force re-render to update the total amount display
                              setTransferFee(prev => prev);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-destructive" />
                      {field.value && transferFee > 0 && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Total com taxa: {formatCurrency(parseAmountValue(field.value) + transferFee)}
                        </p>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* Gateway Display - Gateway that can send money */}
              {/* Gateway Display - Gateway que pode enviar dinheiro */}
              {!defaultGateway && (
                <div className="mx-auto text-center">
                  {/* <AlertCircle className="mx-auto mb-4 size-12 text-orange-500" />
                  <h3 className="mb-2 text-xl font-semibold">{t("transactions.pixTransfer.noGatewayTitle") || "Gateway not configured"}</h3>
                  <p className="mb-4 text-muted-foreground">
                    {t("transactions.pixTransfer.noGatewayMessage") ||
                     "No payment gateway configured for PIX transfers. Contact the administrator to configure a gateway that supports sending money."}
                  </p> */}
                </div>
              )}

              {defaultGateway && !defaultGateway.isActive && (
                <div className="mx-auto p-6 text-center">
                  <AlertCircle className="mx-auto mb-4 size-12 text-orange-500" />
                  <h3 className="mb-2 text-xl font-semibold">Gateway inativo</h3>
                  <p className="mb-4 text-muted-foreground">
                    O gateway {defaultGateway.name} está configurado mas não está ativo.
                    Entre em contato com o administrador para ativar este gateway.
                  </p>
                </div>
              )}

              {defaultGateway && defaultGateway.isActive && !defaultGateway.canSend && (
                <div className="mx-auto p-6 text-center">
                  <AlertCircle className="mx-auto mb-4 size-12 text-orange-500" />
                  <h3 className="mb-2 text-xl font-semibold">Gateway não suporta envio</h3>
                  <p className="mb-4 text-muted-foreground">
                    O gateway {defaultGateway.name} está ativo mas não está configurado para enviar dinheiro.
                    Entre em contato com o administrador para configurar um gateway que suporte envio de dinheiro.
                  </p>
                </div>
              )}

              {/* Fee Information */}
              {transferFee > 0 && (
                <div className="bg-blue-500/10 dark:bg-blue-500/5 border border-blue-500/20 rounded-lg p-4 text-sm">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="text-foreground font-medium">
                        {t("transactions.pixTransfer.feeInfo", { fee: formatCurrency(transferFee) }) ||
                          `Será cobrado uma taxa de ${formatCurrency(transferFee)} por transferência PIX.`}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Esta taxa é aplicada a todas as transferências PIX e é definida pela administração do sistema.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <DialogFooter className="flex flex-row gap-3 sm:justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="flex-1 h-12 rounded-lg"
                  disabled={isLoading}
                >
                  {t("common.cancel") || "Cancelar"}
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-12 rounded-lg shadow-sm"
                  disabled={isLoading || !defaultGateway || !defaultGateway.isActive || !defaultGateway.canSend}
                  title={!defaultGateway ? "Configure um gateway para transferências primeiro" :
                         !defaultGateway.isActive ? `O gateway ${defaultGateway.name} está inativo` :
                         !defaultGateway.canSend ? `O gateway ${defaultGateway.name} não suporta envio de dinheiro` : ""}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Zap className="h-4 w-4" />
                  )}
                  {isLoading
                    ? (t("common.processing") || "Processando...")
                    : (t("transactions.pixTransfer.transfer") || "Transferir")}
                </Button>
              </DialogFooter>
            </form>
          </Form>
          </>
        )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
