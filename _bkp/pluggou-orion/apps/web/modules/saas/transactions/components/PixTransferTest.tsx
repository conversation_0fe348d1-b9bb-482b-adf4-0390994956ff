"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@ui/components/card";
import { PixTransferModal } from "./PixTransferModal";
import { Zap } from "lucide-react";

export function PixTransferTest() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastTransaction, setLastTransaction] = useState<any>(null);

  const handleSuccess = (data: any) => {
    setLastTransaction(data);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Teste de Transferência Pix</CardTitle>
          <CardDescription>
            Teste a funcionalidade de transferência Pix usando o ReflowPay
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => setIsModalOpen(true)}
            className="bg-primary hover:bg-primary/90 text-primary-foreground border-none gap-2 h-12 rounded-lg shadow-sm"
          >
            <Zap className="h-4 w-4" />
            Fazer Transferência Pix
          </Button>
        </CardContent>
        {lastTransaction && (
          <CardFooter className="flex flex-col items-start border-t pt-4">
            <h3 className="text-sm font-medium">Última Transação:</h3>
            <pre className="mt-2 w-full overflow-auto rounded-md bg-slate-950 p-4 text-xs text-white">
              {JSON.stringify(lastTransaction, null, 2)}
            </pre>
          </CardFooter>
        )}
      </Card>

      <PixTransferModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
        onSuccess={handleSuccess}
      />
    </div>
  );
}
