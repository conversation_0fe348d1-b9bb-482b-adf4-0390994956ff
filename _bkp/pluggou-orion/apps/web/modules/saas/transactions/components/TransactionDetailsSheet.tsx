"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@ui/components/sheet";
import { ArrowDownLeft, ArrowUpRight, Copy, Loader2, X, ChevronDown, ChevronUp, RefreshCwIcon, CheckCircleIcon, XCircleIcon, ClockIcon, Zap } from "lucide-react";
import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import { useTheme } from "next-themes";
import { toast } from "sonner";
import { QRCodeSVG } from "qrcode.react";
import { isPendingStatus, normalizeTransactionStatus } from "@repo/utils/src/transaction-status";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@ui/components/tabs";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useParams } from "next/navigation";
import { EventDetails } from "../../../../app/(app)/app/(organizations)/[organizationSlug]/webhooks-portal/components/EventDetails";

import { Transaction, TransactionStatus } from "../hooks/use-transactions";

export type TransactionDetails = Transaction & {
  // Campos adicionais para o sheet de detalhes
  organizationId: string;
  description?: string;
  fee?: string | number | null;
  platformFee?: string | number | null;
  securityReserve?: string | number | null;
  commission?: string | number | null;
  netAmount?: string | number | null;
  // Dedicated fee fields
  percentFee?: number;
  fixedFee?: number;
  totalFee?: number;
  pixCode?: string;
  pixKey?: string;
  pixKeyType?: "CPF" | "EMAIL" | "PHONE" | "RANDOM" | "CNPJ";
  receiverName?: string;
  receiverDocument?: string;
  receiverBank?: string;
  // PIX specific identifiers
  endToEndId?: string | null;
  pixEndToEndId?: string | null;
  refunds?: Array<{
    id: string;
    amount: number;
    status: TransactionStatus;
    reason?: string;
    createdAt: string;
    processedAt?: string;
  }>;
  blocks?: Array<{
    id: string;
    reason: string;
    status: string;
    createdAt: string;
    releasedAt?: string;
  }>;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  // Dados adicionais do PIX conforme documentação
  paymentDetails?: {
    pixEndToEndId?: string | null;
    endToEndId?: string | null;
    pixQrCode?: string | null;
    pixPayload?: string | null;
    pixEncodedImage?: string | null;
    pixExpirationDate?: string | null;
    pixReceiptUrl?: string | null;
    authorizationCode?: string | null;
    receiverName?: string | null;
    receiverDocument?: string | null;
    receiverBank?: string | null;
  };
  metadata?: Record<string, any> | null;
  pix?: {
    txid?: string;
    qrCode?: {
      emv?: string;
      imagem?: string;
    };
    expirationDate?: string;
  };
  // Campos adicionais do response
  externalId?: string;
  referenceCode?: string;
  message?: string;
  paymentInfo?: {
    amount: number;
    customerEmail: string;
    customerName: string;
    paymentAt: string;
  };
};

type TransactionDetailsSheetProps = {
  isOpen: boolean;
  onClose: () => void;
  transaction: TransactionDetails | null;
};

export function TransactionDetailsSheet({
  isOpen,
  onClose,
  transaction
}: TransactionDetailsSheetProps) {
  const t = useTranslations();
  const { theme } = useTheme();
  const [isCheckingPayment, setIsCheckingPayment] = useState(false);
  const [expandedValues, setExpandedValues] = useState<Record<string, boolean>>({});



  // Se não houver transação ou não estiver aberto, não renderize nada
  if (!transaction || !isOpen) return null;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copiado para a área de transferência");
  };

  const toggleExpand = (key: string) => {
    setExpandedValues(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Função para verificar o status do pagamento
  const checkPaymentStatus = async () => {
    if (!transaction.id) return;

    setIsCheckingPayment(true);

    try {
      // For Pluggou PIX transactions, we should use externalId if available
      const idToSync = transaction.externalId || transaction.metadata?.paymentDetails?.txid ||
                      transaction.metadata?.pixRawPayload?.txid || transaction.id;

      const response = await fetch(`/api/payments/transactions/sync-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: idToSync,
          originalTransactionId: transaction.id, // The internal ID to update
        }),
      });

      console.log("Response:", response);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Erro desconhecido" }));
        console.error("Error syncing payment:", errorData);
        throw new Error(errorData.message || 'Erro ao verificar status do pagamento');
      }

      const data = await response.json();

      if (data.success) {
        toast.success("Status do pagamento atualizado");
        // Recarregar a página para atualizar os dados
        window.location.reload();
      } else {
        toast.error(data.message || "Erro ao verificar status do pagamento");
      }
    } catch (error) {
      console.error("Erro ao verificar status:", error);

      // Show a more descriptive error message
      if (error instanceof Error && error.message.includes("not found")) {
        toast.error("Transação não encontrada no gateway de pagamento. Use o ID da transação do recebedor.");
      } else {
        toast.error(`Erro ao verificar status do pagamento: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    } finally {
      setIsCheckingPayment(false);
    }
  };

  const getStatusBadge = () => {
    const normalizedStatus = normalizeTransactionStatus(transaction.status);

    switch (normalizedStatus) {
      case "APPROVED":
        return (
          <span className="bg-success/90 dark:bg-success/80 text-success-foreground dark:text-success-foreground text-xs font-medium px-4 py-1 rounded-full">
            Aprovado
          </span>
        );
      case "REJECTED":
      case "CANCELED":
      case "BLOCKED":
        return (
          <span className="bg-destructive/90 dark:bg-destructive/80 text-destructive-foreground dark:text-destructive-foreground text-xs font-medium px-4 py-1 rounded-full">
            Rejeitado
          </span>
        );
      case "REFUNDED":
        return (
          <span className="bg-violet-500/10 text-violet-500 border-violet-500/20 text-xs font-medium px-4 py-1 rounded-full">
            Estornado
          </span>
        );
      default:
        return (
          <span className="bg-highlight/90 dark:bg-highlight/80 text-highlight-foreground dark:text-highlight-foreground text-xs font-medium px-4 py-1 rounded-full">
            Pendente
          </span>
        );
    }
  };

  const getTransactionTypeIcon = () => {
    const isIncoming = transaction.type === "RECEIVE";
    return isIncoming ? (
      <div className="p-2 bg-success/10 dark:bg-success/20 rounded-full text-success dark:text-primary">
        <ArrowDownLeft size={18} />
      </div>
    ) : (
      <div className="p-2 bg-destructive/10 dark:bg-destructive/20 rounded-full text-destructive dark:text-destructive">
        <ArrowUpRight size={18} />
      </div>
    );
  };

  const formatPixKeyType = (type?: string) => {
    switch (type) {
      case "CPF": return "CPF";
      case "CNPJ": return "CNPJ";
      case "EMAIL": return "E-mail";
      case "PHONE": return "Telefone";
      case "RANDOM": return "Chave aleatória";
      default: return "Chave PIX";
    }
  };

  // Formatar data
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date);
    } catch (e) {
      return dateString;
    }
  };

  // Formatar valor usando a função centralizada
  const formatCurrencyValue = (value?: number) => {
    if (value === undefined || value === null) return "-";
    if (isNaN(value)) return "-";

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  return (
    <Sheet open={isOpen} onOpenChange={() => onClose()}>
      <SheetContent side="right" className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl p-0 border-l border-border dark:border-border bg-background/80 dark:bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col h-full">
          {/* Cabeçalho */}
          <div className="p-4 sm:p-5 border-b border-border dark:border-border flex items-center justify-between">
            <SheetTitle className="text-xl font-semibold text-foreground dark:text-foreground">
              Detalhes da Transação
            </SheetTitle>
          </div>

          {/* Conteúdo com abas */}
          <div className="flex-1 overflow-auto">
            <div className="p-4 sm:p-5">
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="details">Detalhes da Transação</TabsTrigger>
                  <TabsTrigger value="webhooks" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Eventos de Webhook
                  </TabsTrigger>
                </TabsList>

                {/* Aba de Detalhes */}
                <TabsContent value="details" className="space-y-5">
              {/* ID da Transação e Status */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  {getTransactionTypeIcon()}
                  <div>
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Transação</div>
                    <div className="font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[180px] truncate"
                         title={transaction.referenceCode || transaction.id}>
                      {transaction.referenceCode || transaction.id}
                      <button
                        onClick={() => copyToClipboard(transaction.referenceCode || transaction.id)}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                </div>
                <div>
                  {getStatusBadge()}
                </div>
              </div>

              {/* Datas */}
              <div className="space-y-3 bg-accent/20 dark:bg-accent/10 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-muted-foreground dark:text-muted-foreground">Realizada</div>
                  <div className="text-sm text-foreground dark:text-foreground">{formatDate(transaction.createdAt)}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-muted-foreground dark:text-muted-foreground">Pagamento</div>
                  <div className="text-sm text-foreground dark:text-foreground">{formatDate(transaction.paymentAt)}</div>
                </div>
              </div>

              {/* Dados da Transação */}
              <div className="bg-accent/20 dark:bg-accent/10 p-4 rounded-lg space-y-3">
                <h3 className="font-medium text-foreground dark:text-foreground">Dados da transação</h3>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">Subtotal</div>
                  <div className="text-sm font-medium text-foreground dark:text-foreground">{formatCurrencyValue(transaction.amount)}</div>
                </div>
                {/* Display transaction fees */}
                {(transaction.totalFee && transaction.totalFee > 0) || transaction.fee ? (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa por Transação</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">
                      -{transaction.totalFee && transaction.totalFee > 0 ? formatCurrencyValue(transaction.totalFee) :
                        transaction.fee ? (typeof transaction.fee === 'number' ? formatCurrencyValue(transaction.fee) : transaction.fee) : '-'}
                    </div>
                  </div>
                ) : null}
                {transaction.fixedFee && transaction.fixedFee > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa Fixa</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">-{formatCurrencyValue(transaction.fixedFee)}</div>
                  </div>
                )}
                {transaction.percentFee && transaction.percentFee > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa Percentual</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">-{formatCurrencyValue(transaction.percentFee)}</div>
                  </div>
                )}
                {/* PIX End-to-End ID */}
                {transaction.endToEndId && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">PIX End-to-End ID</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1">
                      <span className="max-w-[180px] truncate">
                        {transaction.endToEndId}
                      </span>
                      <button
                        onClick={() => copyToClipboard(transaction.endToEndId || "")}
                        className="text-muted-foreground hover:text-foreground"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}
                {transaction.platformFee && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa da Plataforma</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">{transaction.platformFee}</div>
                  </div>
                )}
                {transaction.securityReserve && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Reserva de Segurança</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">{transaction.securityReserve}</div>
                  </div>
                )}
                {transaction.commission && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Comissão</div>
                    <div className="text-sm font-medium text-success dark:text-primary">{transaction.commission}</div>
                  </div>
                )}
                {transaction.netAmount && (
                  <>
                    <hr className="border-border dark:border-border/50" />
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-semibold text-foreground dark:text-foreground">Valor Líquido</div>
                      <div className="text-sm font-semibold text-success dark:text-primary">{typeof transaction.netAmount === 'number' ? formatCurrencyValue(transaction.netAmount) : transaction.netAmount}</div>
                    </div>
                  </>
                )}
              </div>

              {/* Dados do PIX */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 p-4 rounded-lg space-y-3 border border-green-200/50 dark:border-green-800/50">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <h3 className="font-semibold text-foreground dark:text-foreground">Dados do PIX</h3>
                </div>

                {/* QR Code para transações pendentes ou não aprovadas */}
                {(isPendingStatus(transaction.status) || transaction.status === 'PENDING') && (
                  <div className="flex flex-col items-center justify-center py-2">
                    <div className="bg-white p-4 rounded-lg mb-2 shadow-sm">
                      {/* Prioridade 1: Imagem do QR Code do response */}
                      {transaction.pix?.qrCode?.imagem ? (
                        <img
                          src={transaction.pix.qrCode.imagem.startsWith('data:')
                            ? transaction.pix.qrCode.imagem
                            : `data:image/png;base64,${transaction.pix.qrCode.imagem}`}
                          alt="QR Code PIX"
                          width={150}
                          height={150}
                          className="rounded-lg"
                        />
                      ) : transaction.paymentDetails?.pixEncodedImage ? (
                        <img
                          src={typeof transaction.paymentDetails.pixEncodedImage === 'string' && transaction.paymentDetails.pixEncodedImage.startsWith('data:')
                            ? transaction.paymentDetails.pixEncodedImage
                            : typeof transaction.paymentDetails.pixEncodedImage === 'string'
                              ? `data:image/png;base64,${transaction.paymentDetails.pixEncodedImage}`
                              : ''}
                          alt="QR Code PIX"
                          width={150}
                          height={150}
                          className="rounded-lg"
                        />
                      ) : transaction.metadata?.pixQrCode || transaction.metadata?.pixEncodedImage ? (
                        <img
                          src={typeof transaction.metadata.pixQrCode === 'string' && transaction.metadata.pixQrCode.startsWith('data:')
                            ? transaction.metadata.pixQrCode
                            : typeof transaction.metadata.pixEncodedImage === 'string' && transaction.metadata.pixEncodedImage.startsWith('data:')
                              ? transaction.metadata.pixEncodedImage
                              : typeof transaction.metadata.pixQrCode === 'string'
                                ? `data:image/png;base64,${transaction.metadata.pixQrCode}`
                                : typeof transaction.metadata.pixEncodedImage === 'string'
                                  ? `data:image/png;base64,${transaction.metadata.pixEncodedImage}`
                                  : ''}
                          alt="QR Code PIX"
                          width={150}
                          height={150}
                          className="rounded-lg"
                        />
                      ) : transaction.paymentDetails?.pixPayload ? (
                        <QRCodeSVG
                          value={typeof transaction.paymentDetails.pixPayload === 'string' ? transaction.paymentDetails.pixPayload : ''}
                          size={150}
                          bgColor={"#ffffff"}
                          fgColor={"#000000"}
                          level={"L"}
                          className="rounded-sm"
                        />
                      ) : transaction.metadata?.pixPayload || transaction.metadata?.pixCode || transaction.pixCode ? (
                        <QRCodeSVG
                          value={typeof transaction.metadata?.pixPayload === 'string'
                            ? transaction.metadata.pixPayload
                            : typeof transaction.metadata?.pixCode === 'string'
                              ? transaction.metadata.pixCode
                              : typeof transaction.pixCode === 'string'
                                ? transaction.pixCode
                                : ""}
                          size={150}
                          bgColor={"#ffffff"}
                          fgColor={"#000000"}
                          level={"L"}
                          className="rounded-sm"
                        />
                      ) : null}
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">Escaneie o QR Code com o app do seu banco</p>
                  </div>
                )}

                {/* Código PIX para copiar e colar */}
                {(isPendingStatus(transaction.status) || transaction.status === 'PENDING') && (
                  transaction.pix?.qrCode?.emv ||
                  transaction.paymentDetails?.pixPayload ||
                  transaction.metadata?.pixPayload ||
                  transaction.metadata?.pixCode ||
                  transaction.pixCode
                ) && (
                  <div className="space-y-2 mb-3">
                    <div className="flex justify-between items-center">
                      <p className="text-xs font-medium">Código PIX para copiar e colar:</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2"
                        onClick={() => copyToClipboard(
                          transaction.pix?.qrCode?.emv ||
                          (transaction.paymentDetails?.pixPayload && typeof transaction.paymentDetails.pixPayload === 'string'
                            ? transaction.paymentDetails.pixPayload
                            : transaction.metadata?.pixPayload && typeof transaction.metadata.pixPayload === 'string'
                              ? transaction.metadata.pixPayload
                              : transaction.metadata?.pixCode && typeof transaction.metadata.pixCode === 'string'
                                ? transaction.metadata.pixCode
                                : transaction.pixCode && typeof transaction.pixCode === 'string'
                                  ? transaction.pixCode
                                  : "")
                        )}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copiar
                      </Button>
                    </div>
                    <div
                      className={cn(
                        "bg-muted p-2 rounded-md text-xs border border-border cursor-pointer transition-all duration-200",
                        expandedValues["pixCode"] ? "max-h-48 overflow-y-auto break-all" : "max-h-24 overflow-hidden text-ellipsis break-all"
                      )}
                      onClick={() => toggleExpand("pixCode")}
                      title={expandedValues["pixCode"] ? "Clique para recolher" : "Clique para expandir"}
                    >
                      {transaction.pix?.qrCode?.emv ||
                       (transaction.paymentDetails?.pixPayload && typeof transaction.paymentDetails.pixPayload === 'string'
                        ? transaction.paymentDetails.pixPayload
                        : transaction.metadata?.pixPayload && typeof transaction.metadata.pixPayload === 'string'
                          ? transaction.metadata.pixPayload
                          : transaction.metadata?.pixCode && typeof transaction.metadata.pixCode === 'string'
                            ? transaction.metadata.pixCode
                            : transaction.pixCode && typeof transaction.pixCode === 'string'
                              ? transaction.pixCode
                              : "")}
                    </div>
                    <div className="text-xs text-center text-muted-foreground">
                      {expandedValues["pixCode"] ? "Clique para recolher" : "Clique para expandir"}
                    </div>
                  </div>
                )}

                {/* Tipo de Transação */}
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">Tipo</div>
                  <Badge className={transaction.type === "SEND" ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" : "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"}>
                    {transaction.type === "SEND" ? "Transferência" : "Pagamento"}
                  </Badge>
                </div>

                {/* TxID */}
                {(transaction.pix?.txid || transaction.metadata?.txid || transaction.metadata?.paymentDetails?.txid) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">TxID</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.pix?.txid || transaction.metadata?.txid || transaction.metadata?.paymentDetails?.txid}
                      <button
                        onClick={() => copyToClipboard(
                          transaction.pix?.txid || transaction.metadata?.txid || transaction.metadata?.paymentDetails?.txid || ""
                        )}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Data de expiração do PIX */}
                {(transaction.pix?.expirationDate || transaction.paymentDetails?.pixExpirationDate) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Expira em</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">
                      {formatDate(transaction.pix?.expirationDate || transaction.paymentDetails?.pixExpirationDate || null)}
                    </div>
                  </div>
                )}

                {/* EMV do PIX */}
                {transaction.pix?.qrCode?.emv && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">EMV</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.pix?.qrCode?.emv}
                      <button
                        onClick={() => copyToClipboard(transaction.pix?.qrCode?.emv || "")}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* External ID */}
                {transaction.externalId && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">External ID</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.externalId}
                      <button
                        onClick={() => copyToClipboard(transaction.externalId || "")}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Reference Code */}
                {transaction.referenceCode && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Código de Referência</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.referenceCode}
                      <button
                        onClick={() => copyToClipboard(transaction.referenceCode || "")}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Message */}
                {transaction.message && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Mensagem</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate">
                      {transaction.message}
                    </div>
                  </div>
                )}

                {/* Descrição */}
                {transaction.description && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Descrição</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate">
                      {transaction.description}
                    </div>
                  </div>
                )}

                {/* Dados do pagador do PIX (se disponível) */}
                {(transaction.metadata?.paymentDetails?.payer?.name ||
                  (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'nome' in transaction.metadata.pixPayer && transaction.metadata.pixPayer.nome)) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Pagador</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate">
                      {transaction.metadata?.paymentDetails?.payer?.name ||
                       (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'nome' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.nome) : '')}
                    </div>
                  </div>
                )}

                {(transaction.metadata?.paymentDetails?.payer?.document ||
                  (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' &&
                   ('cpf_cnpj' in transaction.metadata.pixPayer || 'cpf' in transaction.metadata.pixPayer))) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Documento do Pagador</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">
                      {transaction.metadata?.paymentDetails?.payer?.document ||
                       (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'cpf_cnpj' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.cpf_cnpj) :
                        transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'cpf' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.cpf) : '')}
                    </div>
                  </div>
                )}

                {/* Identificador E2E (End-to-End ID) */}
                {(transaction.paymentDetails?.pixEndToEndId ||
                  (transaction.metadata?.paymentDetails && typeof transaction.metadata.paymentDetails === 'object' && 'endToEndId' in transaction.metadata.paymentDetails) ||
                  transaction.metadata?.pixEndToEndId) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">ID End-to-End</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.paymentDetails?.pixEndToEndId ||
                       (transaction.metadata?.paymentDetails && typeof transaction.metadata.paymentDetails === 'object' && 'endToEndId' in transaction.metadata.paymentDetails ?
                        String(transaction.metadata.paymentDetails.endToEndId) :
                        transaction.metadata?.pixEndToEndId ? String(transaction.metadata.pixEndToEndId) : '')}
                      <button
                        onClick={() => copyToClipboard(
                          transaction.paymentDetails?.pixEndToEndId ||
                          (transaction.metadata?.paymentDetails && typeof transaction.metadata.paymentDetails === 'object' && 'endToEndId' in transaction.metadata.paymentDetails ?
                           String(transaction.metadata.paymentDetails.endToEndId) :
                           transaction.metadata?.pixEndToEndId ? String(transaction.metadata.pixEndToEndId) : '')
                        )}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Código de autorização */}
                {transaction.paymentDetails?.authorizationCode && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Código de Autorização</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.paymentDetails.authorizationCode}</div>
                  </div>
                )}

                {/* Link do comprovante, se disponível */}
                {transaction.paymentDetails?.pixReceiptUrl && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Comprovante</div>
                    <a
                      href={transaction.paymentDetails.pixReceiptUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-primary hover:underline"
                    >
                      Visualizar comprovante
                    </a>
                  </div>
                )}
              </div>


              {/* Dados do Cliente */}
              <div className="bg-accent/20 dark:bg-accent/10 p-4 rounded-lg space-y-3">
                <h3 className="font-medium text-foreground dark:text-foreground">Dados do cliente</h3>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">Nome</div>
                  <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate">{transaction.customerName}</div>
                </div>
                {transaction.customerPhone && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Telefone</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.customerPhone}</div>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">E-mail</div>
                  <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate" title={transaction.customerEmail}>{transaction.customerEmail}</div>
                </div>
                {transaction.customerDocument && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Documento</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.customerDocument}</div>
                  </div>
                )}
              </div>



                </TabsContent>

                {/* Aba de Webhooks */}
                <TabsContent value="webhooks" className="space-y-5">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 p-4 rounded-lg space-y-3 border border-green-200/50 dark:border-green-800/50">
                    <div className="flex items-center gap-2 mb-3">
                      <Zap className="h-5 w-5 text-green-600 dark:text-green-400" />
                      <h3 className="font-semibold text-foreground dark:text-foreground">Eventos de Webhook</h3>
                      <Badge className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                        Sistema
                      </Badge>
                    </div>
                    <WebhookEventsList transactionId={transaction.id} />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          {/* Rodapé com botões de ação */}
          <div className="p-4 sm:p-5 border-t border-border dark:border-border">
            <div className="flex flex-wrap justify-end gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                className="text-foreground dark:text-foreground border-border dark:border-border hover:bg-accent/50 dark:hover:bg-accent/30"
              >
                Fechar
              </Button>

              {(transaction.pixCode || transaction.paymentDetails?.pixPayload) && isPendingStatus(transaction.status) && (
                <Button
                  className="bg-primary hover:bg-primary/90 text-primary-foreground gap-2"
                  onClick={() => copyToClipboard(transaction.pixCode || transaction.paymentDetails?.pixPayload || "")}
                >
                  <Copy className="h-4 w-4" />
                  Copiar e Pagar com PIX
                </Button>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

// Componente para listar os eventos de webhook relacionados a uma transação
function WebhookEventsList({ transactionId }: { transactionId: string }) {
  const { activeOrganization } = useActiveOrganization();
  const params = useParams<{ organizationSlug: string }>();
  const organizationSlug = params.organizationSlug;
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);



      const { data: webhookEvents, isLoading } = useQuery({
    queryKey: ["transaction-webhook-events", transactionId, organizationSlug],
    queryFn: async () => {
      if (!organizationSlug) {
        throw new Error("Organization slug não encontrado");
      }

            const response = await fetch(`/api/webhooks/portal/events/transaction/${transactionId}?organizationId=${organizationSlug}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Webhook events API error:", {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`Falha ao carregar eventos de webhook: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      return data.data || [];
    },
    enabled: !!transactionId && !!organizationSlug
  });



  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm">Carregando eventos...</span>
      </div>
    );
  }

  if (!webhookEvents || webhookEvents.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground">Nenhum evento de webhook encontrado para esta transação.</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {webhookEvents.map((event: any) => (
        <div key={event.id} className="border border-border rounded-md">
          <div
            className="p-3 flex justify-between items-center cursor-pointer hover:bg-accent/10"
            onClick={() => setSelectedEventId(selectedEventId === event.id ? null : event.id)}
          >
            <div>
              <Badge className="mb-1">{event.eventType}</Badge>
              <div className="text-xs text-muted-foreground">
                {new Date(event.timestamp).toLocaleString('pt-BR')}
              </div>
            </div>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              {selectedEventId === event.id ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </div>

          {selectedEventId === event.id && (
            <div className="p-3 pt-0 border-t border-border mt-2">
              <EventDetails
                messageId={event.id}
                organizationId={organizationSlug || ''}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
