"use client";

import { Card } from "@ui/components/card";
import { useTransactionsSummary } from "@saas/transactions/hooks/use-transactions";
import { formatCurrency } from "@shared/lib/format";
import {
  CircleDollarSign,
  FileText,
  CheckCircle2,
  Clock,
  TrendingUp,
  TrendingDown
} from "lucide-react";

/**
 * SummaryCard component for dashboard-style cards
 */
function SummaryCard({
  icon,
  value,
  label,
  bgColor,
  textColor,
  total,
  trend
}: {
  icon: React.ReactNode;
  value: string;
  label: string;
  bgColor: string;
  textColor: string;
  total?: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
}) {
  // Convert bgColor to an appropriate style backgroundColor with higher transparency
  const getStyleColor = () => {
    if (bgColor.includes("emerald")) return "rgba(16, 185, 129, 0.05)"; // emerald-500 with 0.05 opacity
    if (bgColor.includes("rose")) return "rgba(244, 63, 94, 0.05)"; // rose-500 with 0.05 opacity
    if (bgColor.includes("blue")) return "rgba(59, 130, 246, 0.05)"; // blue-500 with 0.05 opacity
    if (bgColor.includes("amber")) return "rgba(245, 158, 11, 0.05)"; // amber-500 with 0.05 opacity
    if (bgColor.includes("purple")) return "rgba(139, 92, 246, 0.05)"; // purple-500 with 0.05 opacity
    return "rgba(75, 85, 99, 0.05)"; // gray-500 with 0.05 opacity
  };

  return (
    <Card className="overflow-hidden border border-gray-800" style={{ backgroundColor: getStyleColor() }}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-3">
          <div className={`rounded-full ${bgColor} p-2`}>
            {icon}
          </div>
          {trend && (
            <div className={`flex items-center ${trend.isPositive ? 'text-emerald-500' : 'text-rose-500'} text-sm font-medium`}>
              {trend.isPositive ? (
                <>
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +{trend.value}
                </>
              ) : (
                <>
                  <TrendingDown className="mr-1 h-3 w-3" />
                  {trend.value}
                </>
              )}
            </div>
          )}
        </div>
        <div className={`font-bold text-xl ${textColor}`}>{value}</div>
        <div className="text-sm text-muted-foreground">{label}</div>
        {total && (
          <div className="text-xs text-muted-foreground mt-2">
            {total}
          </div>
        )}
      </div>
    </Card>
  );
}


/**
 * TransactionSummaryCards component to display transaction metrics
 */
export function TransactionSummaryCards() {
  const { data: summaryData, isLoading } = useTransactionsSummary();

  // Calculate values with null checks
  const volumeGrowth = summaryData?.financialVolume?.growth ?? 0;
  const avgTicket = summaryData?.financialVolume?.averageTicket ?? 0;
  const financialVolume = summaryData?.financialVolume?.amount ?? 0;
  const totalCount = summaryData?.totalTransactions?.count ?? 0;
  const totalGrowth = summaryData?.totalTransactions?.growth ?? 0;
  const approvedCount = summaryData?.approvedTransactions?.count ?? 0;
  const approvedGrowth = summaryData?.approvedTransactions?.growth ?? 0;
  const pendingCount = summaryData?.pendingTransactions?.count ?? 0;
  const pendingGrowth = summaryData?.pendingTransactions?.growth ?? 0;
  const approvalRate = summaryData?.approvedTransactions?.approvalRate ?? 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <SummaryCard
        icon={<CircleDollarSign className="size-5 text-emerald-500" />}
        value={isLoading ? "..." : formatCurrency(financialVolume)}
        label="Volume Financeiro"
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
        trend={{
          value: isLoading ? "..." : `${volumeGrowth.toFixed(1)}%`,
          isPositive: volumeGrowth >= 0
        }}
        total={isLoading ? "..." : `Ticket médio: ${formatCurrency(avgTicket)}`}
      />

      <SummaryCard
        icon={<FileText className="size-5 text-blue-500" />}
        value={isLoading ? "..." : String(totalCount)}
        label="Total de Transações"
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
        trend={{
          value: isLoading ? "..." : `${totalGrowth.toFixed(1)}%`,
          isPositive: totalGrowth >= 0
        }}
      />

      <SummaryCard
        icon={<CheckCircle2 className="size-5 text-green-500" />}
        value={isLoading ? "..." : String(approvedCount)}
        label="Transações Aprovadas"
        bgColor="bg-green-500/10"
        textColor="text-green-500"
        trend={{
          value: isLoading ? "..." : `${approvedGrowth.toFixed(1)}%`,
          isPositive: approvedGrowth >= 0
        }}
        total={isLoading ? "..." : `Taxa de aprovação: ${approvalRate.toFixed(1)}%`}
      />

      <SummaryCard
        icon={<Clock className="size-5 text-amber-500" />}
        value={isLoading ? "..." : String(pendingCount)}
        label="Transações Pendentes"
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
        trend={{
          value: isLoading ? "..." : `${pendingGrowth.toFixed(1)}%`,
          isPositive: pendingGrowth < 0 // Negative growth in pending is positive
        }}
      />
    </div>
  );
}
