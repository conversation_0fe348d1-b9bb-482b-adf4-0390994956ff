"use client";

import { Card } from "@ui/components/card";

import { useState, useEffect } from "react";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Eye, Loader2, Search } from "lucide-react";
import { Button } from "@ui/components/button";
import { badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { NewPixTransferButton } from "./NewPixTransferButton";

import { CreatePixChargeButton } from "./CreatePixChargeButton";
import { TransactionDetails, TransactionDetailsSheet } from "./TransactionDetailsSheet";
import { useTransactions, useTransactionDetails, TransactionStatus } from "../hooks/use-transactions";
import { useToast } from "@ui/hooks/use-toast";
import { formatCurrency } from "@shared/lib/format";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Pagination } from "@shared/components/Pagination";

export function TransactionsContent() {
  const { toast } = useToast();
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchId, setSearchId] = useState("");
  const [debouncedSearchId, setDebouncedSearchId] = useState("");

  // Estado para o modal de detalhes da transação
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);

  // Debounce para busca rápida
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchId(searchId);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchId]);

  // Mapear o filtro de status para o formato da API
  const mapStatusFilter = (): TransactionStatus | undefined => {
    switch (statusFilter) {
      case "approved": return "APPROVED";
      case "pending": return "PENDING";
      case "rejected": return "REJECTED";
      case "all": return undefined;
      default: return undefined;
    }
  };


  // Buscar transações usando o hook
  const { data, isLoading, error } = useTransactions({
    page: currentPage,
    limit: 10,
    status: mapStatusFilter(),
    searchId: debouncedSearchId,
  });

  // Buscar detalhes da transação selecionada
  const { data: transactionDetails } = useTransactionDetails(selectedTransactionId);

  // Função para abrir o modal de detalhes
  const openTransactionDetails = (transactionId: string) => {
    setSelectedTransactionId(transactionId);
    setIsDetailsModalOpen(true);
  };

  // Exibir erro se houver
  useEffect(() => {
    if (error) {
      toast({
        title: "Erro ao carregar transações",
        description: error.message,
        variant: "error",
      });
    }
  }, [error, toast]);

  // Formatar data
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "dd/MM/yyyy HH:mm:ss", { locale: ptBR });
    } catch (e) {
      return dateString;
    }
  };

  // Mapear status para exibição
  const statusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <span className={cn(badge({ status: "success" }))}>
            Aprovado
          </span>
        );
      case "REJECTED":
      case "CANCELED":
      case "BLOCKED":
        return (
          <span className={cn(badge({ status: "error" }))}>
            Rejeitado
          </span>
        );
      case "REFUNDED":
        return (
          <span className={cn(badge({ status: "refunded" }))}>
            Estornado
          </span>
        );
      case "PROCESSING":
      case "PENDING":
      default:
        return (
          <span className={cn(badge({ status: "warning" }))}>
            Pendente
          </span>
        );
    }
  };

  return (
    <Card className="overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <div className="p-6">
        {/* Header simplificado */}
        <div className="mb-6">
          {/* Busca Rápida, Filtros e Botões */}
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar por ID, External ID, Referência, CPF/CNPJ, Telefone..."
                value={searchId}
                onChange={(e) => setSearchId(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40 bg-gray-800/50 border-gray-700 text-white">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="approved">Aprovado</SelectItem>
                <SelectItem value="pending">Pendente</SelectItem>
                <SelectItem value="rejected">Rejeitado</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <CreatePixChargeButton />
              <NewPixTransferButton />
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-gray-500">
              {data?.pagination?.total ? `${data.pagination.total} transações encontradas` : ""}
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-800 text-left">
              <th className="pb-2 font-medium text-muted-foreground text-sm" />
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Transação
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Cliente
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Descrição
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Data
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Pagamento
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Valor
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Status
                </th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                <tr>
                  <td colSpan={9} className="py-10 text-center">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Carregando transações...</span>
                    </div>
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={9} className="py-10 text-center text-red-500">
                    Erro ao carregar transações. Tente novamente.
                  </td>
                </tr>
              ) : !data?.data || data.data.length === 0 ? (
                <tr>
                  <td colSpan={9} className="py-10 text-center">
                    Nenhuma transação encontrada.
                  </td>
                </tr>
              ) : (
                data.data.map((transaction) => (
                  <tr
                    key={transaction.id}
                    className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
                    onClick={() => openTransactionDetails(transaction.id)}
                  >
                    {/* Indicador visual de hover */}
                    <td className="absolute inset-y-0 left-0 w-1 bg-[#4caf50] opacity-0 group-hover:opacity-100 transition-opacity" aria-hidden="true"></td>
                    <td className="py-3 text-sm pl-4">{transaction.referenceCode || transaction.id}</td>
                    <td className="py-3 text-sm">
                      <div>
                        <div>{transaction.customerName}</div>
                        <div className="text-xs text-muted-foreground">{transaction.customerEmail}</div>
                      </div>
                    </td>
                    <td className="py-3 text-sm">
                      <div className="max-w-[200px]">
                        {transaction.description ? (
                          <span className="text-sm text-gray-300 truncate block" title={transaction.description}>
                            {transaction.description}
                          </span>
                        ) : (
                          <span className="text-xs text-muted-foreground">-</span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 text-sm">{formatDate(transaction.createdAt)}</td>
                    <td className="py-3 text-sm">{transaction.paymentAt ? formatDate(transaction.paymentAt) : "-"}</td>
                    <td className="py-3 text-sm">{formatCurrency(transaction.amount)}</td>
                    <td className="py-3 text-sm">
                      {statusBadge(transaction.status)}
                    </td>
                    <td className="py-3 text-sm">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-3 text-xs hover:bg-[#4caf50]/10 hover:text-[#4caf50] transition-colors"
                        onClick={(e) => {
                          e.stopPropagation(); // Evita que o clique propague para a linha
                          openTransactionDetails(transaction.id);
                        }}
                      >
                        <Eye className="h-3.5 w-3.5 mr-1" />
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {data?.pagination && data.pagination.total > 0 && (
          <div className="flex justify-center mt-8 mb-4">
            <Pagination
              currentPage={currentPage}
              totalPages={data.pagination.pages || Math.ceil(data.pagination.total / data.pagination.limit) || 1}
              onPageChange={setCurrentPage}
            />
          </div>
        )}

        {/* Debug info */}
        <div className="text-xs text-muted-foreground text-center mt-2">
          {data?.pagination && (
            <span>Total: {data.pagination.total} | Página: {data.pagination.page} de {data.pagination.pages} | Itens por página: {data.pagination.limit}</span>
          )}
        </div>
      </div>

      {/* Modal de detalhes da transação */}
      {transactionDetails && (
        <TransactionDetailsSheet
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false);
            setSelectedTransactionId(null);
          }}
          transaction={transactionDetails}
        />
      )}
    </Card>
  );
}


