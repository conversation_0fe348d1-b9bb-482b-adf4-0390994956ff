"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { <PERSON>ertCircle, ArrowLeft, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@ui/components/form";

// Props para o componente TransferTwoFactorForm
type TransferTwoFactorFormProps = {
  onSuccess: () => void;
  onCancel: () => void;
};

// Schema para validação do código 2FA
const twoFactorSchema = z.object({
  code: z
    .string()
    .min(6, { message: "Código deve ter 6 dígitos" })
    .max(6, { message: "Código deve ter 6 dígitos" })
    .regex(/^\d+$/, { message: "Código deve conter apenas números" }),
});

export function TransferTwoFactorForm({ onSuccess, onCancel }: TransferTwoFactorFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Configuração do formulário com React Hook Form
  const form = useForm<z.infer<typeof twoFactorSchema>>({
    resolver: zodResolver(twoFactorSchema),
    defaultValues: {
      code: "",
    },
  });

  // Função para verificar o código 2FA
  const onSubmit = async (values: z.infer<typeof twoFactorSchema>) => {
    console.log("Verificando código 2FA:", values.code);
    setIsLoading(true);
    setError(null);

    try {
      // Chamada à API para validar o código 2FA
      const response = await fetch("/api/auth/two-factor/verify-transaction", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Environment": "production", // Importante para garantir consistência entre ambientes
        },
        body: JSON.stringify({
          code: values.code,
          purpose: "PIX_TRANSFER", // Indicar o propósito específico da verificação
          timestamp: new Date().toISOString() // Adicionar timestamp para evitar reutilização
        }),
        credentials: "include",
      });

      console.log("Status da resposta 2FA:", response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Erro na verificação 2FA:", errorData);

        let errorMessage = "Código inválido ou expirado. Tente novamente.";
        if (errorData && errorData.message) {
          errorMessage = errorData.message;
        } else if (response.status === 401) {
          errorMessage = "Código inválido. Verifique e tente novamente.";
        } else if (response.status === 429) {
          errorMessage = "Muitas tentativas. Aguarde um momento e tente novamente.";
        }

        setError(errorMessage);
        throw new Error(errorMessage);
      }

      // Se chegou aqui, a resposta foi bem-sucedida
      console.log("Verificação 2FA bem-sucedida!");

      // Extrair o token de verificação do cabeçalho de resposta
      const verificationToken = response.headers.get("X-2FA-Verified");
      const verificationTimestamp = response.headers.get("X-2FA-Timestamp");

      console.log("Cabeçalhos da resposta 2FA:", {
        verified: verificationToken === "true",
        timestamp: verificationTimestamp
      });

      // Configurar o interceptor global para adicionar o cabeçalho X-2FA-Verified em todas as solicitações subsequentes
      if (verificationToken === "true") {
        console.log("Configurando cabeçalho X-2FA-Verified para solicitações subsequentes");

        // Para compatibilidade com o código existente, criar uma função global para adicionar o cabeçalho
        // Esta abordagem garante que mesmo solicitações em outros componentes terão o cabeçalho
        try {
          const addVerificationHeader = () => {
            const originalFetch = window.fetch;
            window.fetch = function(input, init) {
              init = init || {};
              init.headers = init.headers || {};

              // Adicionar o header X-2FA-Verified em todas as solicitações
              Object.assign(init.headers, {
                "X-2FA-Verified": "true",
                "X-2FA-Timestamp": verificationTimestamp || new Date().toISOString()
              });

              return originalFetch.call(this, input, init);
            };
          };

          // Executar a função para modificar o fetch global
          addVerificationHeader();
          console.log("Interceptor de fetch configurado com sucesso");
        } catch (interceptorError) {
          console.error("Erro ao configurar o interceptor de fetch:", interceptorError);
        }
      }

      // Mostrar toast de sucesso
      toast({
        title: "Verificação concluída",
        description: "Sua identidade foi verificada com sucesso",
        variant: "success",
      });

      // Chamar o callback de sucesso
      onSuccess();
    } catch (error) {
      console.error("Erro durante verificação 2FA:", error);

      // Exibir erro para o usuário se ainda não estiver definido
      if (!error) {
        setError("Erro ao verificar código. Tente novamente.");
      }

      toast({
        title: "Erro na verificação",
        description: error instanceof Error ? error.message : "Erro ao verificar código. Tente novamente.",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2 text-center">
              <p className="text-muted-foreground">
                Insira o código de 6 dígitos do seu aplicativo de autenticação para continuar com a transferência.
              </p>
            </div>

            {/* Campo de entrada do código */}
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder="Digite o código de 6 dígitos"
                      className="h-12 text-center text-xl tracking-wider"
                      maxLength={6}
                      inputMode="numeric"
                      pattern="[0-9]*"
                      autoComplete="one-time-code"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />

            {/* Exibir mensagem de erro, se houver */}
            {error && (
              <div className="rounded-lg bg-destructive/10 p-3 text-sm text-destructive flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            )}
          </div>

          {/* Botões de ação */}
          <div className="flex flex-col gap-3">
            <Button
              type="submit"
              disabled={isLoading}
              className="h-12 bg-primary border-none hover:bg-primary/90 text-primary-foreground"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Verificando...
                </>
              ) : (
                "Verificar código"
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="h-12"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Voltar
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
