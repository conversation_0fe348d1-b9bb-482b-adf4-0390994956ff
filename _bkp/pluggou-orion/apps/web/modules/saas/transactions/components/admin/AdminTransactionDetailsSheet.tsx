"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@ui/components/sheet";
import { ArrowDownLeft, ArrowUpRight, Copy, RotateCcw, X, ChevronDown, ChevronUp, RefreshCwIcon, CheckCircleIcon, XCircleIcon, ClockIcon, Loader2, Zap, FileText, Activity } from "lucide-react";
import { cn } from "@ui/lib";
import { Badge, badge } from "@ui/components/badge";
import { toast } from "sonner";
import { Button } from "@ui/components/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { format } from "date-fns";
import { RefundConfirmationDialog } from "./RefundConfirmationDialog";
import { CancelTransactionDialog } from "./CancelTransactionDialog";
import { formatCurrency } from "@shared/lib/currency";
import { isApprovedStatus, normalizeTransactionStatus } from "@repo/utils/src/transaction-status";
import { useQuery } from "@tanstack/react-query";

import { EventDetails } from "../../../../../app/(app)/app/(organizations)/[organizationSlug]/webhooks-portal/components/EventDetails";

// Interface para transações admin
interface AdminTransaction {
  id: string;
  externalId: string | null;
  referenceCode: string | null;
  customerName: string;
  customerEmail: string;
  amount: number;
  status: string;
  type: string;
  createdAt: string;
  paymentAt: string | null;
  organizationId: string;
  organizationName: string;
  organizationSlug: string;
  gateway?: {
    name: string;
    type: string;
  } | null;
  description?: string;
  fee?: number | string;
  platformFee?: number | string;
  netAmount?: number | string;
  // Dedicated fee fields
  percentFee?: number;
  fixedFee?: number;
  totalFee?: number;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  // PIX specific identifiers
  endToEndId?: string | null;
  pixEndToEndId?: string | null;
  metadata?: Record<string, any> | null;
}

type AdminTransactionDetailsSheetProps = {
  isOpen: boolean;
  onClose: () => void;
  transaction: AdminTransaction | null;
};

export function AdminTransactionDetailsSheet({
  isOpen,
  onClose,
  transaction
}: AdminTransactionDetailsSheetProps) {
  const [isRefundDialogOpen, setIsRefundDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  // Debug: Log transaction data
  useEffect(() => {
    if (transaction) {
      console.log("AdminTransactionDetailsSheet - Transaction data:", {
        id: transaction.id,
        organizationId: transaction.organizationId,
        organizationSlug: transaction.organizationSlug,
        organizationName: transaction.organizationName,
        gateway: transaction.gateway // Adicionar log do gateway
      });
    }
  }, [transaction]);

  // Se não houver transação ou não estiver aberto, não renderize nada
  if (!transaction || !isOpen) return null;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copiado para a área de transferência");
  };

  // Função para obter o ícone do tipo de transação
  const getTransactionTypeIcon = () => {
    const isIncoming = transaction.type === "RECEIVE";
    return isIncoming ? (
      <div className="p-2 bg-success/10 dark:bg-success/20 rounded-full text-success dark:text-primary">
        <ArrowDownLeft size={18} />
      </div>
    ) : (
      <div className="p-2 bg-destructive/10 dark:bg-destructive/20 rounded-full text-destructive dark:text-destructive">
        <ArrowUpRight size={18} />
      </div>
    );
  };

  // Formatar data
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return format(date, "dd/MM/yyyy HH:mm:ss");
    } catch (e) {
      return dateString;
    }
  };

  // Formatar valor usando a função centralizada
  const formatCurrencyValue = (value?: number | string) => {
    if (value === undefined || value === null) return "-";

    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    if (isNaN(numValue)) return "-";

    return formatCurrency(numValue);
  };

  // Mapear status para exibição
  const getStatusBadge = () => {
    const normalizedStatus = normalizeTransactionStatus(transaction.status);

    switch (normalizedStatus) {
      case "APPROVED":
        return (
          <span className={cn(badge({ status: "success" }))}>
            Aprovado
          </span>
        );
      case "REJECTED":
      case "CANCELED":
      case "BLOCKED":
        return (
          <span className={cn(badge({ status: "error" }))}>
            Rejeitado
          </span>
        );
      case "REFUNDED":
        return (
          <span className={cn(badge({ status: "refunded" }))}>
            Estornado
          </span>
        );
      case "PROCESSING":
      case "PENDING":
      default:
        return (
          <span className={cn(badge({ status: "warning" }))}>
            Pendente
          </span>
        );
    }
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={() => onClose()}>
        <SheetContent side="right" className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl p-0 border-l border-border dark:border-border bg-background/80 dark:bg-background/80 backdrop-blur-sm">
          <div className="flex flex-col h-full">
            {/* Cabeçalho */}
            <div className="p-4 sm:p-5 border-b border-border dark:border-border flex items-center justify-between">
              <SheetTitle className="text-xl font-semibold text-foreground dark:text-foreground">
                Detalhes da Transação
              </SheetTitle>
            </div>

            {/* Conteúdo com Abas */}
            <div className="flex-1 overflow-hidden">
              <Tabs defaultValue="details" className="h-full flex flex-col">
                <div className="px-4 sm:px-5 pt-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="details" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Detalhes
                    </TabsTrigger>
                    <TabsTrigger value="webhooks" className="flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Webhooks
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="details" className="flex-1 overflow-auto p-4 sm:p-5 space-y-5">
                  {/* ID da Transação e Status */}
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      {getTransactionTypeIcon()}
                      <div>
                        <div className="text-sm text-gray-400">Transação</div>
                        <div className="font-medium flex items-center gap-1 max-w-[180px] truncate"
                             title={transaction.referenceCode || transaction.id}>
                          {transaction.referenceCode || transaction.id}
                          <button
                            onClick={() => copyToClipboard(transaction.referenceCode || transaction.id)}
                            className="text-gray-500 hover:text-white flex-shrink-0"
                          >
                            <Copy size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div>
                      {getStatusBadge()}
                    </div>
                  </div>

                  {/* Organização */}
                  <div className="p-4 bg-gray-800/30 rounded-lg">
                    <div className="text-sm text-gray-400 mb-1">Organização</div>
                    <div className="font-medium">{transaction.organizationName}</div>
                    <div className="text-xs text-gray-400">{transaction.organizationId}</div>
                  </div>

                  {/* Datas */}
                  <div className="space-y-3 bg-gray-800/30 rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium text-gray-400">Criação</div>
                      <div className="text-sm">{formatDate(transaction.createdAt)}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium text-gray-400">Pagamento</div>
                      <div className="text-sm">{formatDate(transaction.paymentAt)}</div>
                    </div>
                  </div>

                  {/* Cliente */}
                  <div className="space-y-3 bg-gray-800/30 rounded-lg p-4">
                    <div className="text-sm font-medium">Dados do Cliente</div>
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-400">Nome</div>
                      <div className="text-sm">{transaction.customerName}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-400">E-mail</div>
                      <div className="text-sm">{transaction.customerEmail}</div>
                    </div>
                    {transaction.customerPhone && (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Telefone</div>
                        <div className="text-sm">{transaction.customerPhone}</div>
                      </div>
                    )}
                    {transaction.customerDocument && (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">
                          {transaction.customerDocumentType || "Documento"}
                        </div>
                        <div className="text-sm">{transaction.customerDocument}</div>
                      </div>
                    )}
                  </div>

                  {/* Dados do PIX - Melhorado */}
                  <div className="space-y-3 bg-gradient-to-r from-green-900/30 to-emerald-900/30 rounded-lg p-4 border border-green-700/30">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div className="text-sm font-medium">
                        {transaction.type === "SEND" ? "Dados da Transferência PIX" : "Dados do Pagamento PIX"}
                      </div>
                    </div>

                    {/* Tipo de Transação */}
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-400">Tipo</div>
                      <div className={`text-xs px-2 py-1 rounded-full ${transaction.type === "SEND" ? "bg-blue-900/50 text-blue-300" : "bg-green-900/50 text-green-300"}`}>
                        {transaction.type === "SEND" ? "Transferência" : "Pagamento"}
                      </div>
                    </div>

                    {/* External ID */}
                    {transaction.externalId && (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">External ID</div>
                        <div className="text-sm flex items-center gap-1 max-w-[60%] truncate">
                          {transaction.externalId}
                          <button
                            onClick={() => copyToClipboard(transaction.externalId || "")}
                            className="text-gray-500 hover:text-white flex-shrink-0"
                          >
                            <Copy size={14} />
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Reference Code */}
                    {transaction.referenceCode && (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Código de Referência</div>
                        <div className="text-sm flex items-center gap-1 max-w-[60%] truncate">
                          {transaction.referenceCode}
                          <button
                            onClick={() => copyToClipboard(transaction.referenceCode || "")}
                            className="text-gray-500 hover:text-white flex-shrink-0"
                          >
                            <Copy size={14} />
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Descrição */}
                    {transaction.description && (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Descrição</div>
                        <div className="text-sm max-w-[60%] truncate">
                          {transaction.description}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Dados da Transação */}
                  <div className="bg-gray-800/30 p-4 rounded-lg space-y-3">
                    <h3 className="font-medium">Dados Financeiros</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-400">Valor</div>
                      <div className="text-sm font-medium">{formatCurrencyValue(transaction.amount)}</div>
                    </div>
                    {/* Display transaction fees */}
                    {(transaction.totalFee && transaction.totalFee > 0) || transaction.fee ? (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Taxa por Transação</div>
                        <div className="text-sm font-medium text-red-500">
                          -{transaction.totalFee && transaction.totalFee > 0 ? formatCurrencyValue(transaction.totalFee) : formatCurrencyValue(transaction.fee)}
                        </div>
                      </div>
                    ) : null}
                    {transaction.fixedFee && transaction.fixedFee > 0 ? (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Taxa Fixa</div>
                        <div className="text-sm font-medium text-red-500">-{formatCurrencyValue(transaction.fixedFee)}</div>
                      </div>
                    ): null}
                    {transaction.percentFee && transaction.percentFee > 0 ? (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Taxa Percentual</div>
                        <div className="text-sm font-medium text-red-500">-{formatCurrencyValue(transaction.percentFee)}</div>
                      </div>
                    ): null}
                    {/* PIX End-to-End ID */}
                    {transaction.endToEndId ? (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">PIX End-to-End ID</div>
                        <div className="text-sm flex items-center gap-1">
                          <span className="max-w-[180px] truncate">
                            {transaction.endToEndId}
                          </span>
                          <button
                            onClick={() => copyToClipboard(transaction.endToEndId || "")}
                            className="text-gray-500 hover:text-white"
                          >
                            <Copy size={14} />
                          </button>
                        </div>
                      </div>
                    ): null}
                    {transaction.platformFee ? (
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Taxa da Plataforma</div>
                        <div className="text-sm font-medium text-red-500">-{formatCurrencyValue(transaction.platformFee)}</div>
                      </div>
                    ): null}
                    {transaction.netAmount ? (
                      <>
                        <hr className="border-gray-700" />
                        <div className="flex justify-between items-center">
                          <div className="text-sm font-semibold">Valor Líquido</div>
                          <div className="text-sm font-semibold text-green-500">{formatCurrencyValue(transaction.netAmount)}</div>
                        </div>
                      </>
                    ): null}
                  </div>

                  {/* Gateway */}
                  <div className="bg-gray-800/30 p-4 rounded-lg space-y-3">
                    <h3 className="font-medium">Gateway</h3>
                    {transaction.gateway ? (
                      <>
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-400">Nome</div>
                          <div className="text-sm">{transaction.gateway.name}</div>
                        </div>
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-400">Tipo</div>
                          <div className="text-sm">{transaction.gateway.type}</div>
                        </div>
                      </>
                    ) : transaction.metadata?.gatewayName ? (
                      <>
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-400">Nome</div>
                          <div className="text-sm">{transaction.metadata.gatewayName}</div>
                        </div>
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-400">Tipo</div>
                          <div className="text-sm">{transaction.metadata.gatewayType}</div>
                        </div>
                      </>
                    ) : (
                      <div className="text-sm text-gray-400">
                        Gateway não disponível para esta transação
                      </div>
                    )}
                  </div>

                  {/* Aprovação Manual pelo Admin */}
                  {transaction.metadata?.adminApproval && (
                    <div className="bg-blue-900/30 p-4 rounded-lg space-y-3 border border-blue-700/30">
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-blue-400" />
                        <h3 className="font-medium text-blue-300">Aprovação Manual</h3>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Aprovado em</div>
                        <div className="text-sm">{formatDate(transaction.metadata.manuallyApprovedAt)}</div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Aprovado por</div>
                        <div className="text-sm">{transaction.metadata.manuallyApprovedBy}</div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Motivo</div>
                        <div className="text-sm">{transaction.metadata.approvalReason}</div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-400">Status Original</div>
                        <div className="text-sm">{transaction.metadata.originalStatus}</div>
                      </div>
                    </div>
                  )}

                  {/* Botões de ação */}
                  <div className="flex justify-between items-center mt-6">
                    <div className="flex gap-2">
                      {/* Botão de estorno apenas para pagamentos (RECEIVE) aprovados */}
                      {transaction.type === "RECEIVE" && isApprovedStatus(transaction.status) && (
                        <Button
                          variant="error"
                          onClick={() => setIsRefundDialogOpen(true)}
                          className="flex items-center gap-2"
                        >
                          <RotateCcw className="h-4 w-4" />
                          Estornar Transação
                        </Button>
                      )}

                      {/* Botão de cancelamento para transferências (SEND) pendentes/processando */}
                      {transaction.type === "SEND" && (transaction.status === "PENDING" || transaction.status === "PROCESSING") && (
                        <Button
                          onClick={() => setIsCancelDialogOpen(true)}
                          className="flex items-center gap-2 bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          <X className="h-4 w-4" />
                          Cancelar Transação
                        </Button>
                      )}
                    </div>
                    <Button variant="outline" onClick={onClose}>
                      Fechar
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="webhooks" className="flex-1 overflow-auto p-4 sm:p-5">
                  <AdminWebhookEventsList
                    transactionId={transaction.id}
                    organizationId={transaction.organizationSlug || transaction.organizationId}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Modal de confirmação de estorno */}
      {transaction && (
        <RefundConfirmationDialog
          isOpen={isRefundDialogOpen}
          onClose={() => setIsRefundDialogOpen(false)}
          transaction={transaction}
          onRefundSuccess={() => {
            setIsRefundDialogOpen(false);
            onClose(); // Fechar o sheet de detalhes também
            // Recarregar a página para atualizar a lista
            window.location.reload();
          }}
        />
      )}

      {/* Modal de confirmação de cancelamento */}
      {transaction && (
        <CancelTransactionDialog
          isOpen={isCancelDialogOpen}
          onClose={() => setIsCancelDialogOpen(false)}
          transaction={transaction}
          onCancelSuccess={() => {
            setIsCancelDialogOpen(false);
            onClose(); // Fechar o sheet de detalhes também
            // Recarregar a página para atualizar a lista
            window.location.reload();
          }}
        />
      )}
    </>
  );
}

// Componente para listar os eventos de webhook relacionados a uma transação
function AdminWebhookEventsList({ transactionId, organizationId }: { transactionId: string; organizationId: string }) {
  // Debug: Log parameters
  useEffect(() => {
    console.log("AdminWebhookEventsList - Parameters:", {
      transactionId,
      organizationId
    });
  }, [transactionId, organizationId]);

  const { data: webhookData, isLoading, error } = useQuery({
    queryKey: ["admin-transaction-webhook-events", transactionId, organizationId],
    queryFn: async () => {
            try {
        console.log("Fetching webhook events for:", { transactionId, organizationId });

        const response = await fetch(`/api/admin/webhooks/events/transaction/${transactionId}?organizationId=${organizationId}`);

        console.log("Response status:", response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Admin webhook events API error:", {
            status: response.status,
            statusText: response.statusText,
            errorText,
            url: response.url
          });

          // Se for erro 500, provavelmente é problema de configuração do SVIX
          if (response.status === 500) {
            throw new Error("Serviço de webhooks temporariamente indisponível. Verifique a configuração do SVIX.");
          }

          throw new Error(`Falha ao carregar eventos de webhook: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log("Webhook events response:", data);

        return {
          events: data.data || [],
          isMock: data.mock || false
        };
      } catch (error) {
        console.error("Error in webhook events query:", error);
        throw error;
      }
    },
    enabled: !!transactionId && !!organizationId,
    retry: 1,
    retryDelay: 1000
  });

  const webhookEvents = webhookData?.events || [];
  const isMockData = webhookData?.isMock || false;

  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm">Carregando eventos de webhook...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="bg-red-900/30 border border-red-700/30 rounded-lg p-4 mb-4">
          <XCircleIcon className="h-8 w-8 text-red-400 mx-auto mb-2" />
          <h3 className="text-sm font-medium text-red-400 mb-1">Erro ao carregar webhooks</h3>
          <p className="text-xs text-red-300">
            {error instanceof Error ? error.message : "Erro desconhecido"}
          </p>
        </div>
        <p className="text-xs text-muted-foreground">
          Verifique se o SVIX está configurado corretamente no ambiente.
        </p>
      </div>
    );
  }

  if (!webhookEvents || webhookEvents.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="bg-blue-900/30 border border-blue-700/30 rounded-lg p-4 mb-4">
          <Zap className="h-8 w-8 text-blue-400 mx-auto mb-2" />
          <h3 className="text-sm font-medium text-blue-400 mb-1">Nenhum evento encontrado</h3>
          <p className="text-xs text-blue-300">
            Não foram encontrados eventos de webhook para esta transação.
          </p>
        </div>
        <p className="text-xs text-muted-foreground">
          Os eventos podem aparecer após alguns minutos ou podem não ter sido gerados ainda.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Zap className="h-5 w-5 text-green-400" />
        <h3 className="font-semibold text-foreground dark:text-foreground">Eventos de Webhook</h3>
        <Badge className="text-xs bg-green-900/50 text-green-300">
          {webhookEvents.length} evento{webhookEvents.length !== 1 ? 's' : ''}
        </Badge>
        {isMockData && (
          <Badge className="text-xs bg-yellow-900/50 text-yellow-300">
            Dados de Demonstração
          </Badge>
        )}
      </div>

      {isMockData && (
        <div className="bg-yellow-900/30 border border-yellow-700/30 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2 mb-2">
            <ClockIcon className="h-4 w-4 text-yellow-400" />
            <span className="text-sm font-medium text-yellow-400">Dados de Demonstração</span>
          </div>
          <p className="text-xs text-yellow-300">
            O SVIX não está configurado. Estes são dados de exemplo para demonstração da interface.
          </p>
        </div>
      )}

      <div className="space-y-3">
        {webhookEvents.map((event: any) => (
          <div key={event.id} className="border border-border rounded-md">
            <div
              className="p-3 flex justify-between items-center cursor-pointer hover:bg-accent/10"
              onClick={() => setSelectedEventId(selectedEventId === event.id ? null : event.id)}
            >
              <div>
                <Badge className="mb-1">{event.eventType}</Badge>
                <div className="text-xs text-muted-foreground">
                  {new Date(event.timestamp).toLocaleString('pt-BR')}
                </div>
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                {selectedEventId === event.id ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </Button>
            </div>

            {selectedEventId === event.id && (
              <div className="p-3 pt-0 border-t border-border mt-2">
                <EventDetails
                  messageId={event.id}
                  organizationId={organizationId}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
