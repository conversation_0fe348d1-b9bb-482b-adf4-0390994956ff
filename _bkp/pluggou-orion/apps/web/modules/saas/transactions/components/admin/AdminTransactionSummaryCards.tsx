"use client";

import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { TrendingDown, TrendingUp } from "lucide-react";
import { useAdminTransactionsSummary } from "../../hooks/use-admin-transactions";
import { formatCurrency } from "@shared/lib/format";

interface SummaryCardProps {
  title: string;
  value: string;
  change: {
    value: string;
    isPositive: boolean;
  };
  description: string;
  subtitle: string;
  bgColor: string;
  textColor: string;
}

function SummaryCard({
  title,
  value,
  change,
  description,
  subtitle,
  bgColor,
  textColor,
}: SummaryCardProps) {
  // Convert bgColor to an appropriate style backgroundColor with higher transparency
  const getStyleColor = () => {
    if (bgColor.includes("emerald")) return "rgba(16, 185, 129, 0.05)"; // emerald-500 with 0.05 opacity
    if (bgColor.includes("rose")) return "rgba(244, 63, 94, 0.05)"; // rose-500 with 0.05 opacity
    if (bgColor.includes("blue")) return "rgba(59, 130, 246, 0.05)"; // blue-500 with 0.05 opacity
    if (bgColor.includes("amber")) return "rgba(245, 158, 11, 0.05)"; // amber-500 with 0.05 opacity
    if (bgColor.includes("purple")) return "rgba(139, 92, 246, 0.05)"; // purple-500 with 0.05 opacity
    return "rgba(75, 85, 99, 0.05)"; // gray-500 with 0.05 opacity
  };

  return (
    <Card className="overflow-hidden border border-gray-800" style={{ backgroundColor: getStyleColor() }}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm text-muted-foreground">{title}</div>
          <div className={`flex items-center ${change.isPositive ? 'text-emerald-500' : 'text-rose-500'} text-sm font-medium`}>
            {change.isPositive ? (
              <>
                <TrendingUp className="mr-1 h-3 w-3" />
                +{change.value}
              </>
            ) : (
              <>
                <TrendingDown className="mr-1 h-3 w-3" />
                {change.value}
              </>
            )}
          </div>
        </div>
        <div className={`font-bold text-2xl ${textColor} mb-1`}>{value}</div>
        <div className="text-sm text-muted-foreground mb-3">{description}</div>
        <div className="text-xs text-muted-foreground">{subtitle}</div>
      </div>
    </Card>
  );
}

interface AdminTransactionSummaryCardsProps {
  typeFilter?: "CHARGE" | "SEND";
}

export function AdminTransactionSummaryCards({ typeFilter }: AdminTransactionSummaryCardsProps = {}) {
  const { data: summaryData, isLoading, error } = useAdminTransactionsSummary({ type: typeFilter });

  // Calculate growth values with null checks
  const totalGrowth = summaryData?.totalTransactions?.growth ?? 0;
  const approvedGrowth = summaryData?.approvedTransactions?.growth ?? 0;
  const pendingGrowth = summaryData?.pendingTransactions?.growth ?? 0;
  const volumeGrowth = summaryData?.financialVolume?.growth ?? 0;
  const organizationsGrowth = summaryData?.activeOrganizations?.growth ?? 0;
  const approvalRate = summaryData?.approvedTransactions?.approvalRate ?? 0;
  const avgTicket = summaryData?.financialVolume?.averageTicket ?? 0;

  // Use fallback data if loading or error
  const fallbackData = {
    totalTransactions: {
      title: "Total de Transações",
      value: isLoading ? "..." : String(summaryData?.totalTransactions?.count ?? 0),
      change: {
        value: isLoading ? "..." : `${totalGrowth.toFixed(1)}%`,
        isPositive: totalGrowth >= 0
      },
      description: "Crescimento em transações",
      subtitle: "Todas as organizações"
    },
    approvedTransactions: {
      title: "Transações Aprovadas",
      value: isLoading ? "..." : String(summaryData?.approvedTransactions?.count ?? 0),
      change: {
        value: isLoading ? "..." : `${approvedGrowth.toFixed(1)}%`,
        isPositive: approvedGrowth >= 0
      },
      description: "Aumento em aprovações",
      subtitle: isLoading ? "..." : `Taxa de aprovação de ${approvalRate.toFixed(1)}%`
    },
    pendingTransactions: {
      title: "Transações Pendentes",
      value: isLoading ? "..." : String(summaryData?.pendingTransactions?.count ?? 0),
      change: {
        value: isLoading ? "..." : `${pendingGrowth.toFixed(1)}%`,
        isPositive: pendingGrowth < 0 // Negative growth in pending is positive
      },
      description: "Aumento em pendências",
      subtitle: "Verificar motivos recorrentes"
    },
    transactionVolume: {
      title: "Volume Financeiro",
      value: isLoading ? "..." : formatCurrency(summaryData?.financialVolume?.amount ?? 0),
      change: {
        value: isLoading ? "..." : `${volumeGrowth.toFixed(1)}%`,
        isPositive: volumeGrowth >= 0
      },
      description: "Crescimento no volume",
      subtitle: isLoading ? "..." : `Ticket médio de ${formatCurrency(avgTicket)}`
    },
    activeOrganizations: {
      title: "Organizações Ativas",
      value: isLoading ? "..." : String(summaryData?.activeOrganizations?.count ?? 0),
      change: {
        value: isLoading ? "..." : `${organizationsGrowth.toFixed(1)}%`,
        isPositive: organizationsGrowth >= 0
      },
      description: "Crescimento em organizações",
      subtitle: "Com transações recentes"
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-5">
      <SummaryCard
        title={fallbackData.totalTransactions.title}
        value={fallbackData.totalTransactions.value}
        change={fallbackData.totalTransactions.change}
        description={fallbackData.totalTransactions.description}
        subtitle={fallbackData.totalTransactions.subtitle}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />

      <SummaryCard
        title={fallbackData.approvedTransactions.title}
        value={fallbackData.approvedTransactions.value}
        change={fallbackData.approvedTransactions.change}
        description={fallbackData.approvedTransactions.description}
        subtitle={fallbackData.approvedTransactions.subtitle}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
      />

      <SummaryCard
        title={fallbackData.pendingTransactions.title}
        value={fallbackData.pendingTransactions.value}
        change={fallbackData.pendingTransactions.change}
        description={fallbackData.pendingTransactions.description}
        subtitle={fallbackData.pendingTransactions.subtitle}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />

      <SummaryCard
        title={fallbackData.transactionVolume.title}
        value={fallbackData.transactionVolume.value}
        change={fallbackData.transactionVolume.change}
        description={fallbackData.transactionVolume.description}
        subtitle={fallbackData.transactionVolume.subtitle}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />

      <SummaryCard
        title={fallbackData.activeOrganizations.title}
        value={fallbackData.activeOrganizations.value}
        change={fallbackData.activeOrganizations.change}
        description={fallbackData.activeOrganizations.description}
        subtitle={fallbackData.activeOrganizations.subtitle}
        bgColor="bg-purple-500/10"
        textColor="text-purple-500"
      />
    </div>
  );
}
