"use client";

import { Card } from "@ui/components/card";
import { useState, useEffect } from "react";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import { cn } from "@ui/lib";
import { useToast } from "@ui/hooks/use-toast";
import { formatCurrency } from "@shared/lib/format";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Pagination } from "@shared/components/Pagination";
import { useAdminTransactions } from "../../hooks/use-admin-transactions";
import { TransactionStatus } from "@prisma/client";
import { Search, Filter, Calendar, Building2, <PERSON><PERSON>, <PERSON>, <PERSON>, CheckCircle } from "lucide-react";
import { AdminTransactionDetailsSheet } from "./AdminTransactionDetailsSheet";
import { ApproveTransactionDialog } from "./ApproveTransactionDialog";
// Skeleton loading para a tabela
const TableSkeleton = ({ isTransferPage = false }: { isTransferPage?: boolean }) => {
  return (
    <>
      {Array.from({ length: 15 }).map((_, index) => (
        <tr key={index} className="border-b border-gray-800/50">
          <td className="py-3 text-sm">
            <div className="h-4 bg-gray-700 rounded animate-pulse w-24"></div>
          </td>
          {isTransferPage ? (
            <td className="py-3 text-sm">
              <div className="flex items-center gap-2 mb-1">
                <div className="h-5 bg-gray-700 rounded animate-pulse w-12"></div>
                <div className="h-4 bg-gray-700 rounded animate-pulse w-32"></div>
              </div>
              <div className="h-3 bg-gray-700 rounded animate-pulse w-40"></div>
            </td>
          ) : (
            <td className="py-3 text-sm">
              <div className="h-4 bg-gray-700 rounded animate-pulse w-32 mb-1"></div>
              <div className="h-3 bg-gray-700 rounded animate-pulse w-40"></div>
            </td>
          )}
          <td className="py-3 text-sm">
            <div className="h-4 bg-gray-700 rounded animate-pulse w-28 mb-1"></div>
            <div className="h-3 bg-gray-700 rounded animate-pulse w-24"></div>
          </td>
          <td className="py-3 text-sm">
            <div className="h-4 bg-gray-700 rounded animate-pulse w-20"></div>
          </td>
          <td className="py-3 text-sm">
            <div className="h-6 bg-gray-700 rounded animate-pulse w-16"></div>
          </td>
          <td className="py-3 text-sm">
            <div className="h-4 bg-gray-700 rounded animate-pulse w-24 mb-1"></div>
            <div className="h-3 bg-gray-700 rounded animate-pulse w-16"></div>
          </td>
          <td className="py-3 text-sm">
            <div className="h-8 bg-gray-700 rounded animate-pulse w-8"></div>
          </td>
        </tr>
      ))}
    </>
  );
};

// Componente simples de detalhes que abre rapidamente
const SimpleTransactionDetailsSheet = ({ isOpen, onClose, transaction }: { isOpen: boolean; onClose: () => void; transaction: any }) => {
  if (!isOpen || !transaction) return null;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full sm:max-w-md">
        <SheetHeader>
          <SheetTitle>Detalhes da Transação</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-300">ID</label>
            <p className="text-white font-mono text-sm">{transaction.id}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-300">Cliente</label>
            <p className="text-white">{transaction.customerName || "N/A"}</p>
            <p className="text-gray-400 text-sm">{transaction.customerEmail || "N/A"}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-300">Organização</label>
            <p className="text-white">{transaction.organization?.name || "N/A"}</p>
            <p className="text-gray-400 text-sm">{transaction.organization?.slug || "N/A"}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-300">Valor</label>
            <p className="text-white font-semibold">{formatCurrency(transaction.amount)}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-300">Status</label>
            <p className="text-white">{transaction.status}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-300">Data</label>
            <p className="text-white">{format(new Date(transaction.createdAt), "dd/MM/yyyy HH:mm", { locale: ptBR })}</p>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

interface AdminTransactionsContentProps {
  defaultTypeFilter?: "CHARGE" | "SEND" | "RECEIVE";
}

export function AdminTransactionsContent({ defaultTypeFilter }: AdminTransactionsContentProps = {}) {
  const { toast } = useToast();
  
  // Estados principais
  const [currentPage, setCurrentPage] = useState(1);
  const [searchId, setSearchId] = useState("");
  const [debouncedSearchId, setDebouncedSearchId] = useState("");
  const [searchEndToEndId, setSearchEndToEndId] = useState("");
  const [debouncedSearchEndToEndId, setDebouncedSearchEndToEndId] = useState("");
  
  // Estados para filtros visíveis
  const [statusFilter, setStatusFilter] = useState<string>("approved");
  const [typeFilter, setTypeFilter] = useState<string>("CHARGE");
  
  // Estados para filtros no sheet
  const [gatewayFilter, setGatewayFilter] = useState<string>("all");
  const [searchClient, setSearchClient] = useState("");
  const [searchOrganization, setSearchOrganization] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  
  // Estados com debounce
  const [debouncedSearchClient, setDebouncedSearchClient] = useState("");
  const [debouncedSearchOrganization, setDebouncedSearchOrganization] = useState("");
  
  // Estado para o sheet de detalhes
  const [isDetailsSheetOpen, setIsDetailsSheetOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  
  // Estado para o diálogo de aprovação
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [transactionToApprove, setTransactionToApprove] = useState<any>(null);

  // Debounce para busca rápida
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchId(searchId);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchId]);

  // Debounce para busca por endToEndId
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchEndToEndId(searchEndToEndId);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchEndToEndId]);

  // Debounce para filtros do sheet
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchClient(searchClient);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchClient]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchOrganization(searchOrganization);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchOrganization]);

  // Mapear o filtro de status para o formato da API
  const mapStatusFilter = (): TransactionStatus | undefined => {
    switch (statusFilter) {
      case "approved": return "APPROVED";
      case "pending": return "PENDING";
      case "rejected": return "REJECTED";
      case "canceled": return "CANCELED";
      case "processing": return "PROCESSING";
      case "refunded": return "REFUNDED";
      case "blocked": return "BLOCKED";
      case "all": return undefined;
      default: return "APPROVED"; // Padrão para transações aprovadas
    }
  };

  // Mapear o filtro de tipo baseado no defaultTypeFilter
  const mapTypeFilter = (): "CHARGE" | "SEND" | "RECEIVE" | undefined => {
    return defaultTypeFilter || "CHARGE"; // Usa o filtro padrão passado como prop
  };

  // Buscar transações usando o hook
  const { data, isLoading, error } = useAdminTransactions({
    page: currentPage,
    limit: 20,
    searchId: debouncedSearchId || undefined,
    searchEndToEndId: debouncedSearchEndToEndId || undefined,
    searchClient: debouncedSearchClient || undefined,
    searchOrganization: debouncedSearchOrganization || undefined,
    status: mapStatusFilter(),
    type: mapTypeFilter(),
    gatewayId: gatewayFilter !== "all" ? gatewayFilter : undefined,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
  });

  // Debug logs
  console.log("AdminTransactionsContent - data:", data);
  console.log("AdminTransactionsContent - isLoading:", isLoading);
  console.log("AdminTransactionsContent - error:", error);
  console.log("AdminTransactionsContent - transactions:", (data as any)?.transactions);
  console.log("AdminTransactionsContent - transactions length:", (data as any)?.transactions?.length);

  // Função para abrir detalhes da transação
  const openTransactionDetails = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsDetailsSheetOpen(true);
  };

  // Função para abrir diálogo de aprovação
  const openApproveDialog = (transaction: any) => {
    setTransactionToApprove(transaction);
    setIsApproveDialogOpen(true);
  };

  // Função para lidar com sucesso da aprovação
  const handleApproveSuccess = () => {
    setIsApproveDialogOpen(false);
    setTransactionToApprove(null);
    // Recarregar a página para atualizar a lista
    window.location.reload();
  };

  // Exibir erro se houver
  useEffect(() => {
    if (error) {
      toast({
        title: "Erro ao carregar transações",
        description: "Ocorreu um erro ao carregar as transações. Tente novamente.",
      });
    }
  }, [error, toast]);

  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case "APPROVED":
        return <Badge className="bg-green-500/10 text-green-500 border-green-500/30">Aprovado</Badge>;
      case "PENDING":
        return <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/30">Pendente</Badge>;
      case "REJECTED":
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/30">Rejeitado</Badge>;
      case "CANCELED":
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/30">Cancelado</Badge>;
      case "PROCESSING":
        return <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/30">Processando</Badge>;
      case "REFUNDED":
        return <Badge className="bg-purple-500/10 text-purple-500 border-purple-500/30">Reembolsado</Badge>;
      case "BLOCKED":
        return <Badge className="bg-orange-500/10 text-orange-500 border-orange-500/30">Bloqueado</Badge>;
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/30">Desconhecido</Badge>;
    }
  };

  const getTypeBadge = (type: "CHARGE" | "SEND" | "RECEIVE") => {
    switch (type) {
      case "CHARGE":
        return <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/30">Cobrança</Badge>;
      case "SEND":
        return <Badge className="bg-green-500/10 text-green-500 border-green-500/30">Envio</Badge>;
      case "RECEIVE":
        return <Badge className="bg-purple-500/10 text-purple-500 border-purple-500/30">Recebimento</Badge>;
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/30">Desconhecido</Badge>;
    }
  };

  // Função para extrair dados PIX do metadata
  const getPixData = (transaction: any) => {
    const metadata = transaction.metadata || {};
    
    // Tentar extrair de diferentes locais no metadata
    let pixKey = transaction.pixKey || 
                 metadata.receiverPixKey || 
                 metadata.providerResponse?.data?.receiverPixKey ||
                 metadata.hubgames?.hubgamesResponse?.pixKey;
    
    let pixKeyType = transaction.pixKeyType || 
                     metadata.pixKeyType || 
                     metadata.hubgames?.pixKeyType ||
                     metadata.providerResponse?.data?.pixKeyType ||
                     metadata.hubgames?.hubgamesResponse?.pixKeyType;
    
    return {
      pixKey: pixKey || "N/A",
      pixKeyType: pixKeyType || "N/A"
    };
  };

  return (
    <Card className="overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <div className="p-6">
        {/* Header simplificado */}
        <div className="mb-6">
          {/* Busca Rápida e Filtros */}
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar por ID, External ID, Referência, CPF/CNPJ, Telefone..."
                value={searchId}
                onChange={(e) => setSearchId(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
              />
            </div>
            
            <div className="relative w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar por End-to-End ID..."
                value={searchEndToEndId}
                onChange={(e) => setSearchEndToEndId(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40 bg-gray-800/50 border-gray-700 text-white">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="approved">Aprovado</SelectItem>
                <SelectItem value="pending">Pendente</SelectItem>
                <SelectItem value="rejected">Rejeitado</SelectItem>
                <SelectItem value="canceled">Cancelado</SelectItem>
                <SelectItem value="processing">Processando</SelectItem>
                <SelectItem value="refunded">Reembolsado</SelectItem>
                <SelectItem value="blocked">Bloqueado</SelectItem>
              </SelectContent>
            </Select>
            
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" className="border-gray-700 text-gray-300 hover:bg-gray-800">
                  <Filter className="h-4 w-4 mr-2" />
                  Filtros
                </Button>
              </SheetTrigger>
              <SheetContent className="w-[400px] sm:w-[540px] bg-gray-900 border-gray-800">
                <SheetHeader>
                  <SheetTitle className="text-white">Filtros de Transações</SheetTitle>
                  <SheetDescription className="text-gray-400">
                    Configure os filtros para encontrar transações específicas
                  </SheetDescription>
                </SheetHeader>
                
                <div className="mt-6 space-y-6">
                  {/* Filtros no Sheet */}
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-2 block">Cliente</label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Buscar por cliente"
                          value={searchClient}
                          onChange={(e) => setSearchClient(e.target.value)}
                          className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-2 block">Organização</label>
                      <div className="relative">
                        <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Buscar por organização"
                          value={searchOrganization}
                          onChange={(e) => setSearchOrganization(e.target.value)}
                          className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
                        />
                      </div>
                    </div>



                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-2 block">Data Inicial</label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="date"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                          className="pl-10 bg-gray-800/50 border-gray-700 text-white"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-2 block">Data Final</label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="date"
                          value={endDate}
                          onChange={(e) => setEndDate(e.target.value)}
                          className="pl-10 bg-gray-800/50 border-gray-700 text-white"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Tabela de transações */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-800 text-left">
                <th className="pb-2 font-medium text-muted-foreground text-sm">Transação</th>
                {defaultTypeFilter === "SEND" ? (
                  <th className="pb-2 font-medium text-muted-foreground text-sm">Chave PIX</th>
                ) : (
                  <th className="pb-2 font-medium text-muted-foreground text-sm">Cliente</th>
                )}
                <th className="pb-2 font-medium text-muted-foreground text-sm">Organização</th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">Valor</th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">Status</th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">Data</th>
                <th className="pb-2 font-medium text-muted-foreground text-sm">Ações</th>
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                <TableSkeleton isTransferPage={defaultTypeFilter === "SEND"} />
              ) : (data as any)?.transactions?.length === 0 ? (
                <tr>
                  <td colSpan={6} className="py-10 text-center">
                    Nenhuma transação encontrada.
                  </td>
                </tr>
              ) : (
                (data as any)?.transactions?.map((transaction: any) => {
                  
                  // Extrair dados PIX uma única vez
                  const pixData = defaultTypeFilter === "SEND" ? getPixData(transaction) : null;
                  
                  return (
                  <tr 
                    key={transaction.id} 
                    className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
                    onClick={() => openTransactionDetails(transaction)}
                  >
                    <td className="py-3 text-sm">
                      {transaction.referenceCode || transaction.id}
                    </td>
                    {defaultTypeFilter === "SEND" ? (
                      <td className="py-3 text-sm">
                        <div>
                          <div className="flex items-center gap-2">
                            {pixData?.pixKeyType !== "N/A" && (
                              <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/30 text-xs">
                                {pixData?.pixKeyType.toUpperCase()}
                              </Badge>
                            )}
                            <span className="font-mono text-sm">{pixData?.pixKey}</span>
                          </div>
                          {transaction.endToEndId && (
                            <div className="text-xs text-muted-foreground mt-1">
                              E2E: {transaction.endToEndId}
                            </div>
                          )}
                        </div>
                      </td>
                    ) : (
                      <td className="py-3 text-sm">
                        <div>
                          <div>{transaction.customerName || "N/A"}</div>
                          <div className="text-xs text-muted-foreground">{transaction.customerEmail || "N/A"}</div>
                        </div>
                      </td>
                    )}
                    <td className="py-3 text-sm">
                      <div>
                        <div>{transaction.organizationName || transaction.organization?.name || "N/A"}</div>
                        
                      </div>
                    </td>
                    <td className="py-3 text-sm">
                      <div className="font-semibold">
                        {formatCurrency(transaction.amount)}
                      </div>
                    </td>
                    <td className="py-3 text-sm">
                      {getStatusBadge(transaction.status)}
                    </td>
                    <td className="py-3 text-sm">
                      <div>
                        {format(new Date(transaction.createdAt), "dd/MM/yyyy", { locale: ptBR })}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(transaction.createdAt), "HH:mm", { locale: ptBR })}
                      </div>
                    </td>
                    <td className="py-3 text-sm">
                      <div className="flex gap-2">
                        {/* Botão de aprovação para transações PENDING */}
                        {transaction.status === "PENDING" && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-300 border-green-600 hover:bg-green-700 hover:text-white"
                            onClick={(e) => {
                              e.stopPropagation();
                              openApproveDialog(transaction);
                            }}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        
                        {/* Botão de visualizar detalhes */}
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-gray-300 border-gray-600 hover:bg-gray-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            openTransactionDetails(transaction);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Paginação e contador */}
        {data?.pagination && (
          <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="text-sm text-gray-400">
              {data?.pagination?.total > 0 ? (
                <>
                  Mostrando {((currentPage - 1) * 20) + 1} a {Math.min(currentPage * 20, data.pagination.total)} de {data.pagination.total} transações
                </>
              ) : (
                'Nenhuma transação encontrada'
              )}
            </div>
            
            {data.pagination.pages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={data.pagination.pages}
                onPageChange={setCurrentPage}
              />
            )}
          </div>
        )}
      </div>
      
      {/* Sheet de detalhes da transação */}
      <AdminTransactionDetailsSheet
        isOpen={isDetailsSheetOpen}
        onClose={() => setIsDetailsSheetOpen(false)}
        transaction={selectedTransaction}
      />
      
      {/* Diálogo de aprovação manual */}
      {transactionToApprove && (
        <ApproveTransactionDialog
          isOpen={isApproveDialogOpen}
          onClose={() => {
            setIsApproveDialogOpen(false);
            setTransactionToApprove(null);
          }}
          transaction={transactionToApprove}
          onApproveSuccess={handleApproveSuccess}
        />
      )}
    </Card>
  );
}