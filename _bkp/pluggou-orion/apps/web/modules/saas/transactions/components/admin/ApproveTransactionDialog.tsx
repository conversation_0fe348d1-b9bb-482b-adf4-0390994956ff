"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { useToast } from "@ui/hooks/use-toast";
import { Loader2, CheckCircle } from "lucide-react";
import { formatCurrency } from "@shared/lib/format";

interface AdminTransaction {
  id: string;
  referenceCode: string | null;
  customerName: string;
  customerEmail: string;
  amount: number;
  status: string;
  type: string;
  organizationName?: string;
  organization?: {
    name: string;
  };
  description?: string;
}

interface ApproveTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: AdminTransaction;
  onApproveSuccess: () => void;
}

export function ApproveTransactionDialog({
  isOpen,
  onClose,
  transaction,
  onApproveSuccess,
}: ApproveTransactionDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleApprove = async () => {
    setIsLoading(true);

    try {
      const response = await fetch(`/api/admin/transactions/${transaction.id}/manual-approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: "Aprovação manual pelo administrador"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Erro ao aprovar transação");
      }

      toast({
        title: "Transação aprovada",
        description: `A transação ${transaction.referenceCode || transaction.id} foi aprovada com sucesso.`,
        variant: "default",
      });

      onApproveSuccess();
      onClose();
    } catch (error) {
      console.error("Erro ao aprovar transação:", error);
      toast({
        title: "Erro ao aprovar",
        description: error instanceof Error ? error.message : "Erro inesperado ao aprovar transação",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Aprovar Transação
          </DialogTitle>
          <DialogDescription>
            Confirme a aprovação manual desta transação. Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted/50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span className="text-sm font-medium">Transação:</span>
              <span className="text-sm">{transaction.referenceCode || transaction.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Cliente:</span>
              <span className="text-sm">{transaction.customerName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Organização:</span>
              <span className="text-sm">{transaction.organizationName || transaction.organization?.name || "N/A"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Valor:</span>
              <span className="text-sm font-semibold">{formatCurrency(transaction.amount)}</span>
            </div>
            {transaction.description && (
              <div className="flex justify-between">
                <span className="text-sm font-medium">Descrição:</span>
                <span className="text-sm">{transaction.description}</span>
              </div>
            )}
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-3 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
               Ao aprovar esta transação, o saldo da organização será atualizado e os webhooks serão disparados automaticamente.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleApprove}
            disabled={isLoading}
 variant={"secondary"}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Aprovando...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Aprovar Transação
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
