"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Textarea } from "@ui/components/textarea";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import { useToast } from "@ui/hooks/use-toast";
import { Loader2, AlertTriangle } from "lucide-react";
import { formatCurrency } from "@shared/lib/format";

interface AdminTransaction {
  id: string;
  externalId: string | null;
  referenceCode: string | null;
  customerName: string;
  customerEmail: string;
  amount: number;
  status: string;
  type: string;
  createdAt: string;
  paymentAt: string | null;
  organizationId: string;
  organizationName: string;
  organizationSlug: string;
  totalFee?: number;
  description?: string;
}

interface CancelTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: AdminTransaction | null;
  onCancelSuccess: () => void;
}

export function CancelTransactionDialog({
  isOpen,
  onClose,
  transaction,
  onCancelSuccess,
}: CancelTransactionDialogProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [reason, setReason] = useState("");
  const [releaseReservedBalance, setReleaseReservedBalance] = useState(true);

  const handleCancel = async () => {
    if (!transaction) return;

    setIsLoading(true);

    try {
      const response = await fetch(`/api/admin/transactions/${transaction.id}/cancel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: reason.trim() || "Cancelamento manual pelo administrador",
          releaseReservedBalance
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || data.message || "Erro ao cancelar transação");
      }

      toast({
        title: "Transação cancelada com sucesso",
        description: releaseReservedBalance 
          ? `Saldo de ${formatCurrency(data.data.releasedAmount)} foi liberado para ${data.data.organizationName}`
          : `Transação cancelada para ${data.data.organizationName}. Saldo permanece reservado.`,
        variant: "success",
      });

      onCancelSuccess();
      onClose();
      setReason("");
      setReleaseReservedBalance(true);
    } catch (error) {
      console.error("Erro ao cancelar transação:", error);
      toast({
        title: "Erro ao cancelar transação",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setReason("");
      setReleaseReservedBalance(true);
    }
  };

  if (!transaction) return null;

  // Calcular valor total que será liberado
  const totalReservedAmount = transaction.amount + (transaction.totalFee || 0);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Cancelar Transação
          </DialogTitle>
          <DialogDescription>
            Esta ação irá cancelar a transação e liberar o saldo reservado.
            Esta operação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informações da transação */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-2">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-medium">ID:</span>
                <div className="text-muted-foreground font-mono">
                  {transaction.referenceCode || transaction.id}
                </div>
              </div>
              <div>
                <span className="font-medium">Status:</span>
                <div className="text-muted-foreground">{transaction.status}</div>
              </div>
              <div>
                <span className="font-medium">Cliente:</span>
                <div className="text-muted-foreground">{transaction.customerName}</div>
              </div>
              <div>
                <span className="font-medium">Organização:</span>
                <div className="text-muted-foreground">{transaction.organizationName}</div>
              </div>
              <div>
                <span className="font-medium">Valor:</span>
                <div className="text-muted-foreground">{formatCurrency(transaction.amount)}</div>
              </div>
              <div>
                <span className="font-medium">Taxa:</span>
                <div className="text-muted-foreground">
                  {transaction.totalFee ? formatCurrency(transaction.totalFee) : "R$ 0,00"}
                </div>
              </div>
            </div>
            
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between items-center font-medium">
                <span>{releaseReservedBalance ? "Total a ser liberado:" : "Total reservado:"}:</span>
                <span className={releaseReservedBalance ? "text-green-600 dark:text-green-400" : "text-yellow-600 dark:text-yellow-400"}>
                  {formatCurrency(totalReservedAmount)}
                </span>
              </div>
            </div>
          </div>

          {/* Opção de liberar saldo reservado */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="releaseBalance"
                checked={releaseReservedBalance}
                onCheckedChange={setReleaseReservedBalance}
                disabled={isLoading}
              />
              <Label htmlFor="releaseBalance" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Liberar saldo reservado
              </Label>
            </div>
            <p className="text-xs text-muted-foreground ml-6">
              {releaseReservedBalance 
                ? "O saldo reservado será liberado e ficará disponível para uso."
                : "O saldo permanecerá reservado e não ficará disponível para uso."}
            </p>
          </div>

          {/* Campo de motivo */}
          <div className="space-y-2">
            <Label htmlFor="reason">Motivo do cancelamento (opcional)</Label>
            <Textarea
              id="reason"
              placeholder="Ex: Chave PIX inválida, solicitação do cliente, etc."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              disabled={isLoading}
            />
          </div>

          {/* Aviso importante */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Atenção:</strong> Esta ação irá:
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Alterar o status da transação para CANCELADO</li>
                  {releaseReservedBalance ? (
                    <>
                      <li>Liberar {formatCurrency(totalReservedAmount)} do saldo reservado</li>
                      <li>Registrar a operação no histórico de saldo</li>
                    </>
                  ) : (
                    <li>Manter {formatCurrency(totalReservedAmount)} no saldo reservado</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleCancel}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Cancelando...
              </>
            ) : (
              "Confirmar Cancelamento"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}