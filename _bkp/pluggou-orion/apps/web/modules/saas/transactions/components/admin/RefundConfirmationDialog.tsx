"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON>ota<PERSON>Ccw } from "lucide-react";
import { useToast } from "@ui/hooks/use-toast";
import { formatCurrency } from "@shared/lib/format";

interface AdminTransaction {
  id: string;
  externalId: string | null;
  referenceCode: string | null;
  customerName: string;
  customerEmail: string;
  amount: number;
  status: string;
  type: string;
  createdAt: string;
  paymentAt: string | null;
  organizationId: string;
  organizationName: string;
  organizationSlug: string;
  gateway?: {
    name: string;
    type: string;
  } | null;
  description?: string;
  fee?: number | string;
  platformFee?: number | string;
  netAmount?: number | string;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  endToEndId?: string | null;
  pixEndToEndId?: string | null;
  metadata?: Record<string, any> | null;
}

interface RefundConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: AdminTransaction;
  onRefundSuccess: () => void;
}

export function RefundConfirmationDialog({
  isOpen,
  onClose,
  transaction,
  onRefundSuccess
}: RefundConfirmationDialogProps) {
  const { toast } = useToast();
  const [reason, setReason] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const handleRefund = async () => {
    if (!reason.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, informe o motivo do estorno.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const response = await fetch(`/api/admin/transactions/${transaction.id}/refund`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: reason.trim(),
          amount: transaction.amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Erro ao processar estorno");
      }

      const result = await response.json();

      toast({
        title: "Estorno processado",
        description: "O estorno foi processado com sucesso.",
        variant: "default",
      });

      onRefundSuccess();
      onClose();
    } catch (error) {
      console.error("Erro ao processar estorno:", error);
      toast({
        title: "Erro ao processar estorno",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (!isProcessing) {
      setReason("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5 text-red-500" />
            Confirmar Estorno
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Aviso */}
          <div className="flex items-start gap-3 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-yellow-500 mb-1">Atenção!</p>
              <p className="text-muted-foreground">
                Esta ação irá estornar a transação apenas em nossa plataforma. 
                Não será feita chamada para APIs externas dos gateways de pagamento.
              </p>
            </div>
          </div>

          {/* Detalhes da transação */}
          <div className="space-y-3 p-4 bg-gray-800/30 rounded-lg">
            <h4 className="font-medium">Detalhes da Transação</h4>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-muted-foreground">ID:</span>
                <p className="font-mono">{transaction.referenceCode || transaction.id}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Valor:</span>
                <p className="font-semibold">{formatCurrency(transaction.amount)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Cliente:</span>
                <p>{transaction.customerName}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Organização:</span>
                <p>{transaction.organizationName}</p>
              </div>
            </div>
          </div>

          {/* Campo de motivo */}
          <div className="space-y-2">
            <label htmlFor="reason" className="text-sm font-medium">
              Motivo do estorno *
            </label>
            <Input
              id="reason"
              placeholder="Ex: Solicitação do cliente, erro no pagamento, etc."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={isProcessing}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isProcessing}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleRefund}
            disabled={isProcessing || !reason.trim()}
          >
            {isProcessing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Confirmar Estorno
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
