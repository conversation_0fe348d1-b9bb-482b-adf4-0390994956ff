import { useQuery } from "@tanstack/react-query";
import { TransactionStatus } from "@prisma/client";

interface AdminTransaction {
  id: string;
  externalId: string | null;
  endToEndId: string | null;
  referenceCode: string | null;
  customerName: string | null;
  customerEmail: string | null;
  customerDocument: string | null;
  customerPhone: string | null;
  amount: number;
  status: TransactionStatus;
  type: "CHARGE" | "SEND" | "RECEIVE";
  createdAt: Date;
  paymentAt: Date | null;
  organizationName?: string;
  organizationSlug?: string;
  organization?: {
    id: string;
    name: string;
    slug: string;
  };
  gateway: {
    id: string;
    name: string;
  } | null;
}

interface UseAdminTransactionsParams {
  // Busca unificada por qualquer identificador
  searchId?: string;
  // Busca específica por endToEndId
  searchEndToEndId?: string;
  // Filtros específicos existentes
  searchClient?: string;
  searchOrganization?: string;
  // Filtros adicionais
  status?: TransactionStatus;
  type?: "CHARGE" | "SEND" | "RECEIVE";
  gatewayId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  enabled?: boolean;
}

interface AdminTransactionsResponse {
  transactions: AdminTransaction[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export function useAdminTransactions(params: UseAdminTransactionsParams = {}) {
  const {
    searchId,
    searchEndToEndId,
    searchClient,
    searchOrganization,
    status,
    type,
    gatewayId,
    startDate,
    endDate,
    page = 1,
    limit = 10,
    enabled = true
  } = params;

  return useQuery<AdminTransactionsResponse>({
    queryKey: [
      "admin-transactions",
      searchId,
      searchEndToEndId,
      searchClient,
      searchOrganization,
      status,
      type,
      gatewayId,
      startDate,
      endDate,
      page,
      limit
    ],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      
      if (searchId) searchParams.append("searchId", searchId);
      if (searchEndToEndId) searchParams.append("searchEndToEndId", searchEndToEndId);
      if (searchClient) searchParams.append("searchClient", searchClient);
      if (searchOrganization) searchParams.append("searchOrganization", searchOrganization);
      if (status) searchParams.append("status", status);
      if (type) searchParams.append("type", type);
      if (gatewayId) searchParams.append("gatewayId", gatewayId);
      if (startDate) searchParams.append("startDate", startDate);
      if (endDate) searchParams.append("endDate", endDate);
      searchParams.append("page", page.toString());
      searchParams.append("limit", limit.toString());

        const response = await fetch(`/api/admin/transactions/list?${searchParams.toString()}`);

        if (!response.ok) {
          throw new Error("Failed to fetch admin transactions");
        }

        const data = await response.json();
        console.log("Admin transactions response:", data);
        return data;
    },
    enabled,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  });
}

export function useAdminTransactionDetails(transactionId: string | null) {
  return useQuery({
    queryKey: ["admin-transaction-details", transactionId],
    queryFn: async () => {
      if (!transactionId) return null;
      
      const response = await fetch(`/api/admin/transactions/${transactionId}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch transaction details");
      }

      return response.json();
    },
    enabled: !!transactionId,
    staleTime: 60000, // 1 minute
  });
}

interface AdminTransactionsSummary {
  totalTransactions: {
    count: number;
    growth: number;
  };
  approvedTransactions: {
    count: number;
    growth: number;
    approvalRate: number;
  };
  pendingTransactions: {
    count: number;
    growth: number;
  };
  financialVolume: {
    amount: number;
    growth: number;
    averageTicket: number;
  };
  activeOrganizations: {
    count: number;
    growth: number;
  };
}

interface UseAdminTransactionsSummaryParams {
  type?: "CHARGE" | "SEND" | "RECEIVE";
  status?: TransactionStatus;
  enabled?: boolean;
}

export function useAdminTransactionsSummary({
  type,
  status,
  enabled = true
}: UseAdminTransactionsSummaryParams = {}) {
  return useQuery<AdminTransactionsSummary>({
    queryKey: [
      "admin-transactions-summary",
      type,
      status
    ],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      
      if (type) searchParams.append("type", type);
      if (status) searchParams.append("status", status);

      const response = await fetch(`/api/admin/transactions/summary?${searchParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch admin transactions summary");
      }

      return response.json();
    },
    enabled,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  });
}