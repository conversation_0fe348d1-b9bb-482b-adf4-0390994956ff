import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";

interface Gateway {
  id: string;
  name: string;
  type: string;
  isDefault: boolean;
  priority: number;
}

interface UseGatewaysParams {
  enabled?: boolean;
}

export function useGateways({ enabled = true }: UseGatewaysParams = {}) {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<Gateway[]>({
    queryKey: ["gateways", organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        const response = await fetch(
          `/api/payments/gateways?organizationId=${organizationId}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch gateways");
        }

        const data = await response.json();
        return data.gateways || [];
      } catch (error) {
        console.error("Error fetching gateways:", error);
        throw error;
      }
    },
    enabled: !!organizationId && enabled,
    staleTime: 300000, // 5 minutes
    refetchOnWindowFocus: false
  });
}
