"use client";

import { createQueryKeyWithParams } from "@shared/lib/query-client";
import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
// import { apiClient } from "@shared/lib/api-client";

export type TransactionStatus = "PENDING" | "APPROVED" | "REJECTED" | "CANCELED" | "PROCESSING" | "REFUNDED" | "BLOCKED";
export type TransactionType = "CHARGE" | "SEND" | "RECEIVE";

export interface Transaction {
  id: string;
  externalId: string | null;
  referenceCode: string | null;
  endToEndId?: string | null;
  customerName: string;
  customerEmail: string;
  amount: number;
  status: TransactionStatus;
  type: TransactionType;
  createdAt: string;
  paymentAt: string | null;
  gateway: {
    name: string;
    type: string;
  } | null;
  pixKey?: string;
  pixKeyType?: "CPF" | "CNPJ" | "EMAIL" | "PHONE" | "RANDOM";
  metadata?: Record<string, any> | null;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  description?: string;
  // Fee information
  percentFee?: number;
  fixedFee?: number;
  totalFee?: number;
  netAmount?: number;
  fee?: string | number | null; // Legacy fee field
  paymentDetails?: {
    endToEndId?: string;
    txid?: string;
    pixKey?: string;
    amount?: number;
    paymentTimestamp?: string;
    eventType?: string;
    status?: string;
    payer?: {
      name?: string;
      document?: string;
      bankCode?: string;
    };
  };
}

export interface TransactionPagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface TransactionsResponse {
  data: Transaction[];
  pagination: TransactionPagination;
}

export interface TransactionsSummary {
  totalTransactions: {
    count: number;
    growth: number;
  };
  approvedTransactions: {
    count: number;
    growth: number;
    approvalRate: number;
  };
  pendingTransactions: {
    count: number;
    growth: number;
  };
  financialVolume: {
    amount: number;
    growth: number;
    averageTicket: number;
  };
}

interface UseTransactionsParams {
  page?: number;
  limit?: number;
  status?: TransactionStatus;
  type?: TransactionType;
  startDate?: string;
  endDate?: string;
  searchId?: string;
  searchClient?: string;
  searchOrganization?: string;
  enabled?: boolean;
}

export const useTransactions = ({
  page = 1,
  limit = 10,
  status,
  type,
  startDate,
  endDate,
  searchId,
  searchClient,
  searchOrganization,
  enabled = true,
}: UseTransactionsParams = {}) => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<TransactionsResponse>({
    queryKey: createQueryKeyWithParams(
      ["transactions", "list"],
      {
        page,
        limit,
        ...(status && { status }),
        ...(type && { type }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
        ...(searchId && { searchId }),
        ...(searchClient && { searchClient }),
        ...(searchOrganization && { searchOrganization }),
        organizationId: organizationId || "",
      }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        // Convert params to the expected format
        const queryParams = {
          organizationId,
          page: page.toString(),
          limit: limit.toString(),
          ...(status && { status }),
          ...(type && { type }),
          ...(startDate && { startDate }),
          ...(endDate && { endDate }),
          ...(searchId && { search: searchId }),
          ...(searchClient && { search: searchClient }),
          ...(searchOrganization && { search: searchOrganization }),
        };

        // Using fetch directly as it matches the Next.js API route structure
        const response = await fetch(
          `/api/payments/transactions/list?${new URLSearchParams(queryParams as Record<string, string>)}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch transactions");
        }

        const data = await response.json();

        // Log para debug
        console.log('Pagination data:', {
          total: data.pagination?.total,
          page: data.pagination?.page,
          limit: data.pagination?.limit,
          pages: data.pagination?.totalPages || data.pagination?.pages,
          calculatedPages: Math.ceil((data.pagination?.total || 0) / limit)
        });

        // Format response to match the expected interface
        return {
          data: data.transactions || [],
          pagination: data.pagination || {
            total: data.total || 0,
            page: parseInt(data.pagination?.page) || page,
            limit: parseInt(data.pagination?.limit) || limit,
            pages: data.pagination?.totalPages || data.pagination?.pages || Math.ceil((data.pagination?.total || 0) / limit) || 1
          }
        };
      } catch (error) {
        console.error("Error fetching transactions:", error);
        throw error;
      }
    },
    enabled: !!organizationId && enabled,
    retry: 1,
  });
};

export const useTransactionDetails = (transactionId: string | null) => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery({
    queryKey: ["transaction", transactionId],
    queryFn: async () => {
      if (!transactionId) {
        throw new Error("No transaction ID provided");
      }

      try {
        // Using fetch directly as it matches the Next.js API route structure
        const response = await fetch(`/api/payments/transactions/${transactionId}`, {
          credentials: 'include'
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch transaction details");
        }

        const data = await response.json();
        return data.data || data; // Handle both formats
      } catch (error) {
        console.error("Error fetching transaction details:", error);
        throw error;
      }
    },
    enabled: !!transactionId && !!organizationId,
  });
};

export const useTransactionsSummary = ({
  type,
  status,
}: {
  type?: TransactionType;
  status?: TransactionStatus;
} = {}) => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<TransactionsSummary>({
    queryKey: createQueryKeyWithParams(
      ["transactions", "summary"],
      {
        organizationId: organizationId || "",
        ...(type && { type }),
        ...(status && { status })
      }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        // Construir queryParams com os filtros
        const queryParams = new URLSearchParams({
          organizationId: organizationId || ""
        });

        if (type) queryParams.append("type", type);
        if (status) queryParams.append("status", status);

        // Usando fetch diretamente para o endpoint summary
        const response = await fetch(
          `/api/payments/transactions/summary?${queryParams}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch transactions summary");
        }

        return await response.json();
      } catch (error) {
        console.error("Error fetching transactions summary:", error);
        throw error;
      }
    },
    enabled: !!organizationId,
    retry: 1,
  });
};
