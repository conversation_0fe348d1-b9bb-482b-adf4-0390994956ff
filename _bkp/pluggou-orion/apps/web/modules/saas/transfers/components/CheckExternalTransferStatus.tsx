import { useState } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { toast } from "sonner";
import { verifyTransferStatus } from "../hooks/use-transfers";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { RefreshCwIcon, CheckCircleIcon, XCircleIcon, ClockIcon } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { formatCurrency } from "@shared/lib/format";
import { getStatusDisplayText } from "@repo/utils/src/transaction-status";

interface ExternalTransfer {
  externalId: string;
  status: string;
  rawStatus: string;
  amount?: number;
  pixKey?: string;
  pixKeyType?: string;
  date?: string;
  paymentDate?: string;
  description?: string;
  gateway?: string;
}

export function CheckExternalTransferStatus({ onSuccess }: { onSuccess?: () => void }) {
  const [externalId, setExternalId] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [externalTransfer, setExternalTransfer] = useState<ExternalTransfer | null>(null);
  const { activeOrganization } = useActiveOrganization();

  const handleVerify = async () => {
    if (!externalId || !activeOrganization?.id) {
      toast.error("Por favor, informe o ID externo da transferência");
      return;
    }

    setIsVerifying(true);
    try {
      // Passamos o externalId como ambos transactionId e externalId para garantir que funcione
      const result = await verifyTransferStatus(
        externalId,
        activeOrganization.id,
        externalId
      );

      // Mostrar mensagem de sucesso
      toast.success(
        result.message || "Status da transferência verificado com sucesso!"
      );

      // Armazenar os dados da transferência externa
      if (result.externalTransfer) {
        setExternalTransfer(result.externalTransfer);
      }

      // Chamar callback de sucesso se fornecido
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error verifying transfer status:", error);
      toast.error("Falha ao verificar o status da transferência. Tente novamente.");
    } finally {
      setIsVerifying(false);
    }
  };

  // Função para renderizar o ícone de status
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case "APPROVED":
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case "REJECTED":
      case "CANCELED":
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  return (
    <div className="flex flex-col gap-4">
      {/* <Card>
        <CardHeader className="pb-2">
          <CardTitle>Verificar Status por ID Externo</CardTitle>
          <CardDescription>
            Informe o ID externo da transferência na Transfeera para verificar seu status.
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="ID externo da Transfeera"
              value={externalId}
              onChange={(e) => setExternalId(e.target.value)}
              disabled={isVerifying}
            />

            <Button
              onClick={handleVerify}
              disabled={isVerifying || !externalId}
              variant="outline"
            >
              {isVerifying ? (
                <>
                  <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                  Verificando...
                </>
              ) : (
                <>
                  <RefreshCwIcon className="mr-2 h-4 w-4" />
                  Verificar
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card> */}

      {externalTransfer && (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle>Detalhes da Transferência Externa</CardTitle>
              <div className="flex items-center gap-2">
                {renderStatusIcon(externalTransfer.status)}
                <span className="font-medium">
                  {getStatusDisplayText(externalTransfer.status)}
                </span>
              </div>
            </div>
            <CardDescription>
              ID Externo: {externalTransfer.externalId}
            </CardDescription>
          </CardHeader>

          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Valor</p>
                  <p className="text-lg font-semibold">
                    {externalTransfer.amount ? formatCurrency(externalTransfer.amount) : "N/A"}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status Original</p>
                  <p className="text-lg font-semibold">{externalTransfer.rawStatus || "N/A"}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Chave PIX</p>
                  <p className="text-lg font-semibold">{externalTransfer.pixKey || "N/A"}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Tipo de Chave</p>
                  <p className="text-lg font-semibold">{externalTransfer.pixKeyType || "N/A"}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Data de Criação</p>
                  <p className="text-lg font-semibold">
                    {externalTransfer.date ? new Date(externalTransfer.date).toLocaleString() : "N/A"}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Data de Pagamento</p>
                  <p className="text-lg font-semibold">
                    {externalTransfer.paymentDate ? new Date(externalTransfer.paymentDate).toLocaleString() : "N/A"}
                  </p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">Descrição</p>
                <p className="text-lg font-semibold">{externalTransfer.description || "N/A"}</p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">Gateway</p>
                <p className="text-lg font-semibold">{externalTransfer.gateway || "N/A"}</p>
              </div>

              <div className="text-sm text-muted-foreground">
                Status: {getStatusDisplayText(externalTransfer.status)}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
