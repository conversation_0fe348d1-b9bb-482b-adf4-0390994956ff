"use client";

import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>it<PERSON> } from "@ui/components/sheet";
import { badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { ArrowDownIcon, ArrowUpIcon, Eye, Loader2, RefreshCwIcon, ReceiptIcon } from "lucide-react";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Button } from "@ui/components/button";
import { Pagination } from "@shared/components/Pagination";
import { useTransfers, Transfer, verifyTransferStatus, getTransferBalanceHistory } from "@saas/transfers/hooks/use-transfers";
import { formatCurrency } from "@shared/lib/format";
import { toast } from "sonner";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@ui/components/card";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { SyncTransfeeraTransfer } from "./SyncTransfeeraTransfer";

interface DataTableTransfersProps {
  onRefresh?: () => void;
  actionButton?: React.ReactNode;
}

interface BalanceHistoryItem {
  id: string;
  operation: string;
  amount: number;
  description: string | null;
  balanceAfterOperation: {
    available: number;
    pending: number;
    reserved: number;
  };
  createdAt: string;
}

interface BalanceHistoryResponse {
  success: boolean;
  balanceHistory: BalanceHistoryItem[];
  transaction: {
    amount: number;
    status: string;
    type: string;
    metadata: any;
    createdAt: string;
    paymentAt: string | null;
    gatewayName: string | null;
  };
}

export function DataTableTransfers({ onRefresh, actionButton }: DataTableTransfersProps) {
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const { activeOrganization } = useActiveOrganization();
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [idFilter, setIdFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [balanceHistory, setBalanceHistory] = useState<BalanceHistoryItem[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [activeTab, setActiveTab] = useState("details");
  const itemsPerPage = 5; // Number of items to display per page

  const { data, isLoading, error } = useTransfers({
    page: currentPage,
    limit: itemsPerPage,
    status: statusFilter !== "all" ? statusFilter : undefined,
    searchId: idFilter || undefined,
  });

  const loadBalanceHistory = useCallback(async () => {
    if (!selectedTransfer || !activeOrganization?.id) return;

    setIsLoadingHistory(true);
    try {
      const result = await getTransferBalanceHistory(
        selectedTransfer.id,
        activeOrganization.id
      );

      if (result.success && result.balanceHistory) {
        setBalanceHistory(result.balanceHistory);
      }
    } catch (error) {
      console.error("Error loading balance history:", error);
      toast.error("Falha ao carregar o histórico de saldo. Tente novamente.");
    } finally {
      setIsLoadingHistory(false);
    }
  }, [selectedTransfer, activeOrganization?.id]);

  // Effect to load balance history when a transfer is selected
  useEffect(() => {
    if (selectedTransfer && isDetailsOpen && activeTab === "receipt" && activeOrganization?.id) {
      loadBalanceHistory();
    }
  }, [selectedTransfer, isDetailsOpen, activeTab, activeOrganization?.id, loadBalanceHistory]);

  const handleRowClick = (transfer: Transfer) => {
    setSelectedTransfer(transfer);
    setIsDetailsOpen(true);
    setActiveTab("details"); // Reset to details tab when opening
  };

  const statusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <span className={cn(badge({ status: "success" }))}>
            Concluída
          </span>
        );
      case "PENDING":
        return (
          <span className={cn(badge({ status: "warning" }))}>
            Pendente
          </span>
        );
      case "PROCESSING":
        return (
          <span className={cn(badge({ status: "warning" }))}>
            Processando
          </span>
        );
      case "REJECTED":
        return (
          <span className={cn(badge({ status: "error" }))}>
            Rejeitada
          </span>
        );
      case "CANCELED":
        return (
          <span className={cn(badge({ status: "error" }))}>
            Cancelada
          </span>
        );
      default:
        return (
          <span className={cn(badge({ status: "info" }))}>
            {status}
          </span>
        );
    }
  };

  // Função para formatar a data
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <>
      <div>
        <div className="flex items-center mb-6 gap-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
            <Input
              placeholder="Buscar por ID"
              value={idFilter}
              onChange={(e) => setIdFilter(e.target.value)}
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="APPROVED">Concluída</SelectItem>
                <SelectItem value="PENDING">Pendente</SelectItem>
                <SelectItem value="PROCESSING">Processando</SelectItem>
                <SelectItem value="REJECTED">Rejeitada</SelectItem>
                <SelectItem value="CANCELED">Cancelada</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {actionButton && (
            <div className="flex-shrink-0">{actionButton}</div>
          )}
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-center py-10 text-red-500">
            Erro ao carregar transferências. Por favor, tente novamente.
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-800 text-left">
                    <th className="pb-4 font-medium text-muted-foreground text-sm">
                      ID
                    </th>
                    <th className="pb-4 font-medium text-muted-foreground text-sm">
                      Data
                    </th>
                    <th className="pb-4 font-medium text-muted-foreground text-sm">
                      Tipo
                    </th>
                    <th className="pb-4 font-medium text-muted-foreground text-sm">
                      Detalhes
                    </th>
                    <th className="pb-4 font-medium text-muted-foreground text-sm text-left">
                      Valor
                    </th>
                    <th className="pb-4 font-medium text-muted-foreground text-sm text-left">
                      Status
                    </th>
                    <th className="pb-4 font-medium text-muted-foreground text-sm text-left">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {data?.data.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="py-6 text-center text-muted-foreground">
                        Nenhuma transferência encontrada
                      </td>
                    </tr>
                  ) : (
                    data?.data.map((transfer) => (
                      <tr
                        key={transfer.id}
                        className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
                        onClick={() => handleRowClick(transfer)}
                      >
                        <td className="py-4 text-sm font-medium">{transfer.id}</td>
                        <td className="py-4 text-sm">{formatDate(transfer.date)}</td>
                        <td className="py-4 text-sm">
                          <div className="flex items-center">
                            <ArrowUpIcon className="mr-1 h-4 w-4 text-red-500" />
                            <span>Enviada</span>
                          </div>
                        </td>
                        <td className="py-4 text-sm">
                          <div>
                            <div>Para: {transfer.recipient?.name || "Não informado"}</div>
                            <div className="text-muted-foreground text-xs">
                              {transfer.pixKeyType}: {transfer.pixKey}
                            </div>
                          </div>
                        </td>
                        <td className="py-4 text-sm text-left">
                          <span className="text-red-500">
                            -{formatCurrency(transfer.amount)}
                          </span>
                        </td>
                        <td className="py-4 text-sm text-left pr-6">{statusBadge(transfer.status)}</td>
                        <td className="py-4 text-sm text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-3 text-xs hover:bg-emerald-500/10 hover:text-emerald-500 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRowClick(transfer);
                            }}
                          >
                            <Eye className="h-3.5 w-3.5" />
                          </Button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {data && data.pagination.pages > 1 && (
              <div className="flex justify-center mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={data.pagination.pages}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </>
        )}
      </div>

      {/* Details Sheet */}
      <Sheet open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <SheetContent className="sm:max-w-md md:max-w-lg">
          <SheetHeader>
            <SheetTitle>Detalhes da Transferência</SheetTitle>
          </SheetHeader>

          {selectedTransfer && (
            <div className="mt-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="details">Detalhes</TabsTrigger>
                  <TabsTrigger value="receipt">Comprovante</TabsTrigger>
                </TabsList>

                {/* Details Tab */}
                <TabsContent value="details" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">ID da Transferência</p>
                      <p className="font-medium">{selectedTransfer.id}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Data</p>
                      <p className="font-medium">{formatDate(selectedTransfer.date)}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">Tipo</p>
                    <div className="flex items-center mt-1">
                      <ArrowUpIcon className="mr-1 h-4 w-4 text-red-500" />
                      <span>Enviada</span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">Destinatário</p>
                    <p className="font-medium">{selectedTransfer.recipient?.name || "Não informado"}</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {selectedTransfer.pixKeyType}: {selectedTransfer.pixKey}
                    </p>
                  </div>

                  {selectedTransfer.description && (
                    <div>
                      <p className="text-sm text-muted-foreground">Descrição</p>
                      <p className="font-medium">{selectedTransfer.description}</p>
                    </div>
                  )}

                  <div>
                    <p className="text-sm text-muted-foreground">Valor</p>
                    <p className="font-medium text-red-500">
                      -{formatCurrency(selectedTransfer.amount)}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <div className="mt-1">{statusBadge(selectedTransfer.status)}</div>
                  </div>

                  {selectedTransfer.gateway && (
                    <div>
                      <p className="text-sm text-muted-foreground">Gateway</p>
                      <p className="font-medium">{selectedTransfer.gateway.name}</p>
                    </div>
                  )}

                  {selectedTransfer.paymentDate && (
                    <div>
                      <p className="text-sm text-muted-foreground">Data de Pagamento</p>
                      <p className="font-medium">{formatDate(selectedTransfer.paymentDate)}</p>
                    </div>
                  )}

                  {selectedTransfer.updatedAt && (
                    <div>
                      <p className="text-sm text-muted-foreground">Última Atualização</p>
                      <p className="font-medium">{formatDate(selectedTransfer.updatedAt)}</p>
                    </div>
                  )}

                  {selectedTransfer.status !== "APPROVED" && selectedTransfer.status !== "REJECTED" && (
                    <div className="mt-4">
                      <div className="flex flex-col space-y-2">
                        <Button
                          onClick={async () => {
                            if (!activeOrganization?.id || !selectedTransfer) return;

                            setIsVerifying(true);
                            try {
                              const result = await verifyTransferStatus(
                                selectedTransfer.id,
                                activeOrganization.id,
                                selectedTransfer.externalId || undefined
                              );

                              // Update the selected transfer with the new status
                              setSelectedTransfer(prev => {
                                if (!prev) return null;
                                return { ...prev, ...result.transfer };
                              });

                              // Show success message
                              toast.success("Status da transferência atualizado com sucesso!");

                              // Refresh the transfers list
                              if (onRefresh) {
                                onRefresh();
                              }
                            } catch (error) {
                              console.error("Error verifying transfer status:", error);
                              toast.error("Falha ao verificar o status da transferência. Tente novamente.");
                            } finally {
                              setIsVerifying(false);
                            }
                          }}
                          disabled={isVerifying}
                          variant="outline"
                          className="w-full"
                        >
                          {isVerifying ? (
                            <>
                              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                              Verificando...
                            </>
                          ) : (
                            <>
                              <RefreshCwIcon className="mr-2 h-4 w-4" />
                              Verificar Status
                            </>
                          )}
                        </Button>

                        {/* Botão para sincronizar transferências da Transfeera */}
                        {selectedTransfer.gateway?.slug === "transfeera" && selectedTransfer.externalId && (
                          <SyncTransfeeraTransfer
                            transferId={selectedTransfer.externalId}
                            onSuccess={() => {
                              // Refresh the transfers list
                              if (onRefresh) {
                                onRefresh();
                              }

                              // Reload the transfer details
                              if (activeOrganization?.id && selectedTransfer) {
                                verifyTransferStatus(
                                  selectedTransfer.id,
                                  activeOrganization.id,
                                  selectedTransfer.externalId || undefined
                                ).then(result => {
                                  setSelectedTransfer(prev => {
                                    if (!prev) return null;
                                    return { ...prev, ...result.transfer };
                                  });
                                }).catch(error => {
                                  console.error("Error reloading transfer after sync:", error);
                                });
                              }
                            }}
                            variant="secondary"
                            className="w-full"
                          />
                        )}
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Receipt Tab */}
                <TabsContent value="receipt" className="space-y-4 mt-4">
                  <Card className="border-gray-800 bg-gray-900/30">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">Comprovante de Transferência</CardTitle>
                        <ReceiptIcon className="h-5 w-5 text-primary" />
                      </div>
                      <CardDescription>
                        Detalhes completos da operação
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="border-b border-gray-800 pb-4">
                        <h3 className="font-semibold text-md mb-2">Informações da Transferência</h3>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">ID da Transação</p>
                            <p className="font-medium">{selectedTransfer.id}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Data</p>
                            <p className="font-medium">{formatDate(selectedTransfer.date)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Valor</p>
                            <p className="font-medium text-red-500">-{formatCurrency(selectedTransfer.amount)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Status</p>
                            <div>{statusBadge(selectedTransfer.status)}</div>
                          </div>
                          {selectedTransfer.externalId && (
                            <div>
                              <p className="text-muted-foreground">ID Externo</p>
                              <p className="font-medium">{selectedTransfer.externalId}</p>
                            </div>
                          )}
                          {selectedTransfer.gateway && (
                            <div>
                              <p className="text-muted-foreground">Gateway</p>
                              <p className="font-medium">{selectedTransfer.gateway.name}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="border-b border-gray-800 pb-4">
                        <h3 className="font-semibold text-md mb-2">Destinatário</h3>
                        <div className="grid grid-cols-1 gap-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">Nome</p>
                            <p className="font-medium">{selectedTransfer.recipient?.name || "Não informado"}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Chave PIX</p>
                            <p className="font-medium">{selectedTransfer.pixKey}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Tipo de Chave</p>
                            <p className="font-medium">{selectedTransfer.pixKeyType}</p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-semibold text-md mb-2">Histórico de Saldo</h3>
                        {isLoadingHistory ? (
                          <div className="flex justify-center py-4">
                            <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          </div>
                        ) : balanceHistory.length === 0 ? (
                          <div className="text-center py-4 text-muted-foreground">
                            <p>Nenhum histórico de saldo encontrado</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-2"
                              onClick={loadBalanceHistory}
                            >
                              Carregar Histórico
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {balanceHistory.map((item) => (
                              <div key={item.id} className="border border-gray-800 rounded-md p-3">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <p className="font-medium">
                                      {item.operation === "RESERVE" ? "Reserva de Saldo" :
                                       item.operation === "UNRESERVE" ? "Liberação de Reserva" :
                                       item.operation === "DEBIT" ? "Débito" :
                                       item.operation === "CREDIT" ? "Crédito" : item.operation}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      {new Date(item.createdAt).toLocaleString('pt-BR')}
                                    </p>
                                  </div>
                                  <p className={cn(
                                    "font-medium",
                                    item.operation === "DEBIT" ? "text-red-500" :
                                    item.operation === "CREDIT" ? "text-green-500" :
                                    "text-yellow-500"
                                  )}>
                                    {item.operation === "DEBIT" ? "-" :
                                     item.operation === "CREDIT" ? "+" : ""}
                                    {formatCurrency(item.amount)}
                                  </p>
                                </div>
                                {item.description && (
                                  <p className="text-xs text-muted-foreground mt-1">{item.description}</p>
                                )}
                                <div className="grid grid-cols-3 gap-2 mt-2 text-xs">
                                  <div>
                                    <p className="text-muted-foreground">Disponível</p>
                                    <p className="font-medium">{formatCurrency(item.balanceAfterOperation.available)}</p>
                                  </div>
                                  <div>
                                    <p className="text-muted-foreground">Pendente</p>
                                    <p className="font-medium">{formatCurrency(item.balanceAfterOperation.pending)}</p>
                                  </div>
                                  <div>
                                    <p className="text-muted-foreground">Reservado</p>
                                    <p className="font-medium">{formatCurrency(item.balanceAfterOperation.reserved)}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  );
}
