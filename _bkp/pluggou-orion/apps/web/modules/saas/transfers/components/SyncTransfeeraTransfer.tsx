"use client";

import { useState } from "react";
import { Button } from "@ui/components/button";
import { Loader2, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";

type SyncTransfeeraTransferProps = {
  transferId: string;
  onSuccess?: () => void;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
};

export function SyncTransfeeraTransfer({
  transferId,
  onSuccess,
  variant = "outline",
  size = "sm",
  className
}: SyncTransfeeraTransferProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { activeOrganization } = useActiveOrganization();

  const handleSync = async () => {
    if (!activeOrganization?.id) {
      toast.error("Organização não encontrada");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/payments/transfers/sync-transfeera", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          transferId,
          organizationId: activeOrganization.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao sincronizar transferência");
      }

      const data = await response.json();
      
      if (data.result.balanceUpdated) {
        toast.success("Transferência sincronizada com sucesso");
        if (onSuccess) onSuccess();
      } else if (data.result.noChanges) {
        toast.info("Transferência já está atualizada");
      } else {
        toast.info("Status verificado, sem alterações necessárias");
      }
    } catch (error) {
      console.error("Erro ao sincronizar transferência:", error);
      toast.error(error instanceof Error ? error.message : "Erro ao sincronizar transferência");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleSync}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
      ) : (
        <RefreshCw className="h-4 w-4 mr-2" />
      )}
      Sincronizar Status
    </Button>
  );
}
