"use client";

import { Card } from "@ui/components/card";
import { DataTableTransfers } from "./DataTableTransfers";
import { useTransfers } from "@saas/transfers/hooks/use-transfers";
import { useQueryClient } from "@tanstack/react-query";
import { CheckExternalTransferStatus } from "./CheckExternalTransferStatus";
import { NewPixTransferButton } from "@saas/transactions/components/NewPixTransferButton";

export function TransfersContent() {
  const queryClient = useQueryClient();

  const handleRefresh = () => {
    // Invalidate the transfers query to trigger a refetch
    queryClient.invalidateQueries({ queryKey: ["transfers"] });
  };

  return (
    <div className="space-y-6">
      <CheckExternalTransferStatus onSuccess={handleRefresh} />

      <Card className="overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <div className="p-6">

          <DataTableTransfers onRefresh={handleRefresh} actionButton={<NewPixTransferButton />} />
        </div>
      </Card>
    </div>
  );
}
