"use client";

import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { TrendingDown, TrendingUp } from "lucide-react";
import { useTransfersSummary } from "@saas/transfers/hooks/use-transfers";
import { formatCurrency } from "@shared/lib/format";

interface SummaryCardProps {
  title: string;
  value: string;
  change: {
    value: string;
    isPositive: boolean;
  };
  description: string;
  subtitle: string;
  bgColor: string;
  textColor: string;
}

function SummaryCard({
  title,
  value,
  change,
  description,
  subtitle,
  bgColor,
  textColor,
}: SummaryCardProps) {
  // Convert bgColor to an appropriate style backgroundColor with higher transparency
  const getStyleColor = () => {
    if (bgColor.includes("emerald")) return "rgba(16, 185, 129, 0.05)"; // emerald-500 with 0.05 opacity
    if (bgColor.includes("rose")) return "rgba(244, 63, 94, 0.05)"; // rose-500 with 0.05 opacity
    if (bgColor.includes("blue")) return "rgba(59, 130, 246, 0.05)"; // blue-500 with 0.05 opacity
    if (bgColor.includes("amber")) return "rgba(245, 158, 11, 0.05)"; // amber-500 with 0.05 opacity
    return "rgba(75, 85, 99, 0.05)"; // gray-500 with 0.05 opacity
  };

  return (
    <Card className="overflow-hidden border border-gray-800" style={{ backgroundColor: getStyleColor() }}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm text-muted-foreground">{title}</div>
          <div className={`flex items-center ${change.isPositive ? 'text-emerald-500' : 'text-rose-500'} text-sm font-medium`}>
            {change.isPositive ? (
              <>
                <TrendingUp className="mr-1 h-3 w-3" />
                +{change.value}
              </>
            ) : (
              <>
                <TrendingDown className="mr-1 h-3 w-3" />
                {change.value}
              </>
            )}
          </div>
        </div>
        <div className={`font-bold text-2xl ${textColor} mb-1`}>{value}</div>
        <div className="text-sm text-muted-foreground mb-3">{description}</div>
        <div className="text-xs text-muted-foreground">{subtitle}</div>
      </div>
    </Card>
  );
}

export function TransfersSummaryCards() {
  const { data: summaryData, isLoading, error } = useTransfersSummary();

  const fallbackData = {
    totalVolume: {
      title: "Volume Total",
      value: isLoading ? "..." : formatCurrency(summaryData?.totalVolume?.amount || 0),
      change: {
        value: isLoading ? "..." : `${summaryData?.totalVolume?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.totalVolume?.growth || 0) >= 0
      },
      description: (summaryData?.totalVolume?.growth || 0) >= 0 ? "Crescimento nas transferências" : "Redução nas transferências",
      subtitle: "Acima da meta mensal"
    },
    incomingTransfers: {
      title: "Transferências Recebidas",
      value: isLoading ? "..." : `${summaryData?.incomingTransfers?.count || 0}`,
      change: {
        value: isLoading ? "..." : `${summaryData?.incomingTransfers?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.incomingTransfers?.growth || 0) >= 0
      },
      description: "Aumento em recebimentos",
      subtitle: "Maior volume de clientes"
    },
    outgoingTransfers: {
      title: "Transferências Enviadas",
      value: isLoading ? "..." : `${summaryData?.outgoingTransfers?.count || 0}`,
      change: {
        value: isLoading ? "..." : `${summaryData?.outgoingTransfers?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.outgoingTransfers?.growth || 0) >= 0
      },
      description: (summaryData?.outgoingTransfers?.growth || 0) >= 0 ? "Aumento em envios" : "Redução em envios",
      subtitle: "Menor saída de recursos"
    },
    averageValue: {
      title: "Valor Médio",
      value: isLoading ? "..." : formatCurrency(summaryData?.averageValue?.amount || 0),
      change: {
        value: isLoading ? "..." : `${summaryData?.averageValue?.growth?.toFixed(1) || 0}%`,
        isPositive: (summaryData?.averageValue?.growth || 0) >= 0
      },
      description: "Aumento no ticket médio",
      subtitle: "Clientes com maior poder aquisitivo"
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
      <SummaryCard
        title={fallbackData.totalVolume.title}
        value={fallbackData.totalVolume.value}
        change={fallbackData.totalVolume.change}
        description={fallbackData.totalVolume.description}
        subtitle={fallbackData.totalVolume.subtitle}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
      />

      <SummaryCard
        title={fallbackData.incomingTransfers.title}
        value={fallbackData.incomingTransfers.value}
        change={fallbackData.incomingTransfers.change}
        description={fallbackData.incomingTransfers.description}
        subtitle={fallbackData.incomingTransfers.subtitle}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />

      <SummaryCard
        title={fallbackData.outgoingTransfers.title}
        value={fallbackData.outgoingTransfers.value}
        change={fallbackData.outgoingTransfers.change}
        description={fallbackData.outgoingTransfers.description}
        subtitle={fallbackData.outgoingTransfers.subtitle}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />

      <SummaryCard
        title={fallbackData.averageValue.title}
        value={fallbackData.averageValue.value}
        change={fallbackData.averageValue.change}
        description={fallbackData.averageValue.description}
        subtitle={fallbackData.averageValue.subtitle}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />
    </div>
  );
}
