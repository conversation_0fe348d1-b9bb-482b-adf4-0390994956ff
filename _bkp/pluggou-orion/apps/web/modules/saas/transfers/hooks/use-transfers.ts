"use client";

import { createQueryKeyWithParams } from "@shared/lib/query-client";
import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";

export type TransferStatus = "PENDING" | "APPROVED" | "REJECTED" | "CANCELED" | "PROCESSING";
export type TransferType = "SEND" | "RECEIVE";

export interface Transfer {
  id: string;
  externalId: string | null;
  date: string;
  paymentDate?: string;
  amount: number;
  status: TransferStatus;
  pixKey: string;
  pixKeyType: string;
  description?: string;
  recipient: {
    name: string;
    pixKey: string;
    pixKeyType: string;
  };
  gateway?: {
    name: string;
    type: string;
  } | null;
  updatedAt?: string;
}

export interface TransfersResponse {
  data: Transfer[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface TransfersSummary {
  totalVolume: {
    amount: number;
    growth: number;
  };
  incomingTransfers: {
    count: number;
    growth: number;
  };
  outgoingTransfers: {
    count: number;
    growth: number;
  };
  averageValue: {
    amount: number;
    growth: number;
  };
}

interface UseTransfersParams {
  page?: number;
  limit?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
  searchId?: string;
  enabled?: boolean;
}

export const useTransfers = ({
  page = 1,
  limit = 10,
  status,
  startDate,
  endDate,
  searchId,
  enabled = true,
}: UseTransfersParams = {}) => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<TransfersResponse>({
    queryKey: createQueryKeyWithParams(
      ["transfers", "list"],
      {
        page,
        limit,
        ...(status && { status }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
        ...(searchId && { search: searchId }),
        organizationId: organizationId || "",
      }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        // Convert params to the expected format
        const queryParams: Record<string, string> = {
          organizationId,
          page: page.toString(),
          limit: limit.toString(),
        };

        // Adicionar apenas parâmetros definidos
        if (status) queryParams.status = status;
        if (startDate) queryParams.startDate = startDate;
        if (endDate) queryParams.endDate = endDate;
        if (searchId) queryParams.search = searchId;

        // Using fetch directly as it matches the API route structure
        const response = await fetch(
          `/api/payments/transfers?${new URLSearchParams(queryParams)}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch transfers");
        }

        return await response.json();
      } catch (error) {
        console.error("Error fetching transfers:", error);
        throw error;
      }
    },
    enabled: !!organizationId && enabled,
    retry: 1,
  });
};

export const useTransfersSummary = () => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<TransfersSummary>({
    queryKey: createQueryKeyWithParams(
      ["transfers", "summary"],
      { organizationId: organizationId || "" }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        // Usando fetch diretamente para o endpoint summary
        const response = await fetch(
          `/api/payments/transfers/summary?organizationId=${organizationId}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch transfers summary");
        }

        return await response.json();
      } catch (error) {
        console.error("Error fetching transfers summary:", error);
        throw error;
      }
    },
    enabled: !!organizationId,
    retry: 1,
  });
};

// Function to verify transfer status with the gateway
export const verifyTransferStatus = async (transferId: string, organizationId: string, externalId?: string) => {
  try {
    const response = await fetch(
      `/api/payments/transfers/sync-status`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: transferId,
          organizationId,
          externalId,
        }),
        credentials: 'include'
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API Error:", errorText);
      throw new Error("Failed to verify transfer status");
    }

    return await response.json();
  } catch (error) {
    console.error("Error verifying transfer status:", error);
    throw error;
  }
};

// Function to get balance history for a transaction
export const getTransferBalanceHistory = async (transactionId: string, organizationId: string) => {
  try {
    const response = await fetch(
      `/api/payments/balance/history?transactionId=${transactionId}&organizationId=${organizationId}`,
      { credentials: 'include' }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API Error:", errorText);
      throw new Error("Failed to fetch balance history");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching balance history:", error);
    throw error;
  }
};
