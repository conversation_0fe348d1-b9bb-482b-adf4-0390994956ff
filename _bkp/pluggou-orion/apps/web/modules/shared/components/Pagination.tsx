"use client";

import { But<PERSON> } from "@ui/components/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className = "",
}: PaginationProps) {
  // Lógica para mostrar um número limitado de páginas quando há muitas
  const getVisiblePages = () => {
    // Sempre mostrar no máximo 5 páginas
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Se tiver menos páginas que o máximo, mostrar todas
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Calcular o intervalo de páginas a serem mostradas
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    // Ajustar se ultrapassar o total de páginas
    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
  };

  const visiblePages = getVisiblePages();
  const showFirstPageButton = visiblePages[0] > 1;
  const showLastPageButton = visiblePages[visiblePages.length - 1] < totalPages;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Botão para página anterior */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="h-8 w-8 border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {/* Botão para primeira página */}
      {showFirstPageButton && (
        <>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(1)}
            className="h-8 w-8 border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
          >
            1
          </Button>
          {visiblePages[0] > 2 && (
            <span className="px-1 text-muted-foreground">...</span>
          )}
        </>
      )}

      {/* Páginas visíveis */}
      {visiblePages.map((page) => (
        <Button
          key={page}
          variant={currentPage === page ? "default" : "outline"}
          size="sm"
          onClick={() => onPageChange(page)}
          className={`h-8 w-8 ${currentPage === page
            ? 'bg-primary text-black/80 hover:bg-primary/90'
            : 'border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white'}`}
        >
          {page}
        </Button>
      ))}

      {/* Botão para última página */}
      {showLastPageButton && (
        <>
          {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
            <span className="px-1 text-muted-foreground">...</span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages)}
            className="h-8 w-8 border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
          >
            {totalPages}
          </Button>
        </>
      )}

      {/* Botão para próxima página */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className="h-8 w-8 border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
