{"dependencies": {"@aws-sdk/client-s3": "3.437.0", "@fumadocs/content-collections": "^1.1.6", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@sindresorhus/slugify": "^2.2.1", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.62.10", "@tanstack/react-table": "^8.20.6", "bcryptjs": "^3.0.2", "better-auth": "1.1.3", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "1.6.2", "cuid": "^3.0.0", "date-fns": "^4.1.0", "decimal.js": "^10.4.3", "deepmerge": "^4.3.1", "fumadocs-core": "^14.6.4", "fumadocs-ui": "^14.6.4", "hono": "^4.6.14", "imask": "^7.6.1", "jotai": "2.8.0", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "nanoid": "^5.0.9", "next": "15.1.2", "next-intl": "3.26.3", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nprogress": "^0.2.0", "nuqs": "^2.2.3", "oslo": "^1.2.1", "prettier": "3.3.3", "qrcode.react": "^4.2.0", "react": "19.0.0", "react-cropper": "^2.3.3", "react-day-picker": "^9.9.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-imask": "^7.6.1", "recharts": "^2.15.2", "server-only": "^0.0.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ufo": "^1.5.4", "usehooks-ts": "^3.1.0", "uuid": "^11.0.2", "zod": "^3.24.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@content-collections/core": "^0.8.0", "@content-collections/mdx": "^0.2.0", "@content-collections/next": "^0.2.4", "@mdx-js/mdx": "^3.1.0", "@repo/auth": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@shikijs/rehype": "^1.24.4", "@types/bcryptjs": "^3.0.0", "@types/cookie": "^0.6.0", "@types/js-cookie": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "22.10.2", "@types/nprogress": "^0.2.3", "@types/react": "19.0.2", "@types/react-dom": "19.0.2", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.20", "cypress": "^13.17.0", "dotenv-cli": "^8.0.0", "markdown-toc": "^1.2.0", "mdx": "^0.3.1", "postcss": "8.4.49", "rehype-img-size": "^1.0.1", "start-server-and-test": "^2.0.9", "tailwindcss": "3.4.17"}, "name": "@repo/web", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "e2e": "dotenv -c -e ../../.env -- start-server-and-test dev http://localhost:3000 \"cypress open --e2e\"", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit"}, "version": "0.0.0"}