import type { Config } from "./types";

export const config = {
 
	i18n: {
		enabled: true,
		locales: {
			pt: {
				currency: "BRL",
				label: "Português",
			},
		},
		defaultLocale: "pt",
		defaultCurrency: "BRL",
		localeCookieName: "NEXT_LOCALE",
	},
	// Organizations
	organizations: {
		enable: true,
		enableBilling: true,
		hideOrganization: false,
		enableUsersToCreateOrganizations: false,
		requireOrganization: false,
		forbiddenOrganizationSlugs: [
			"new-organization",
			"admin",
			"settings",
			"ai-demo",
		],
	},
	// Users
	users: {
		enableBilling: true,
		enableOnboarding: true,
	},
 
	auth: {
		enableSignup: true,
		enableMagicLink: true,
		enableSocialLogin: false,
		enablePasskeys: true,
		enablePasswordLogin: false,
		redirectAfterSignIn: "/app",
		redirectAfterLogout: "/",
		sessionCookieMaxAge: 60 * 60 * 24 * 30,
	},
	// Mails
	mails: {
		from: "<EMAIL>",
	},
	ui: {
		enabledThemes: ["dark"],
		defaultTheme: "dark",
		saas: {
			enabled: true,
			useSidebarLayout: true,
		},
		marketing: {
			enabled: false,
		},
	},
	// Storage
	storage: {
		bucketNames: {
			avatars: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME ?? "avatars",
			documents: process.env.NEXT_PUBLIC_DOCUMENTS_BUCKET_NAME ?? "documents",
		},
	},
	// Payments
	payments: {
		plans: {
			free: {
				isFree: true,
			},
			pro: {
				recommended: true,
				prices: [
					{
						type: "recurring",
						productId: "price_1M9kHDFkmmuOs718blksnsHJ",
						interval: "month",
						amount: 29,
						currency: "USD",
						trialPeriodDays: 7,
					},
					{
						type: "recurring",
						productId: "price_1M9kHqFkmmuOs718XRvjZ8l1",
						interval: "year",
						amount: 290,
						currency: "USD",
						trialPeriodDays: 7,
					},
				],
			},
			enterprise: {
				isEnterprise: true,
			},
			// lifetime: {
			// 	prices: [
			// 		{
			// 			type: "one-time",
			// 			productId: "price_1PHjoxFkmmuOs718Orzx98rv",
			// 			amount: 799,
			// 			currency: "USD",
			// 		},
			// 	],
			// },
		},
	},
	// SVIX Webhooks
	svix: {
		apiKey: process.env.SVIX_TOKEN || "",
		apiUrl: process.env.SVIX_API_URL || "https://webhooks.cloud.pluggou.io",
	},
} as const satisfies Config;

export type { Config };
