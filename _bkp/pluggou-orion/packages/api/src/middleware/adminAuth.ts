import { createMiddleware } from "hono/factory";
import { HTTPException } from "hono/http-exception";
import { logger } from "@repo/logs";

export const adminAuthMiddleware = createMiddleware<{
	Variables: {
		session: any;
		user: any;
		organization?: any;
		apiKey?: any;
	};
}>(async (c, next) => {
	try {
		const user = c.get("user");

		if (!user) {
			logger.warn("Admin access denied - no user found");
			throw new HTTPException(401, {
				message: "Não autorizado"
			});
		}

		if (user.role !== "admin") {
			logger.warn("Admin access denied - user is not admin", {
				userId: user.id,
				userRole: user.role
			});
			throw new HTTPException(403, {
				message: "Acesso negado. Apenas administradores podem acessar este recurso."
			});
		}

		logger.debug("Admin access granted", {
			userId: user.id,
			userRole: user.role
		});

		await next();
	} catch (error) {
		logger.error("Error in admin auth middleware", {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined
		});

		if (error instanceof HTTPException) {
			throw error;
		}

		throw new HTTPException(500, {
			message: "Erro interno do servidor"
		});
	}
});