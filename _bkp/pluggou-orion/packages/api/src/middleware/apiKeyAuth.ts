import { db } from "@repo/database";
import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { createHash } from "crypto";
import { logger } from "@repo/logs";

/**
 * Middleware for API key authentication
 * This middleware verifies the API key from the X-API-Key header
 * and adds the organization and API key info to the context
 */
export const apiKeyAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  try {
    logger.info("API key authentication started");

    // Get API key from header
    const apiKey = c.req.header("X-API-Key");
    logger.debug("API key header received", { hasApiKey: !!apiKey });

    if (!apiKey) {
      logger.warn("Missing API key in request");
      throw new HTTPException(401, { message: "API key is required" });
    }

    // Calculate hash of the API key
    const hash = createHash("sha256").update(apiKey).digest("hex");
    logger.debug("API key hash calculated", { hashPrefix: hash.substring(0, 10) });

    // Find API key by hash
    logger.debug("Looking up API key in database");
    const key = await db.api_key.findFirst({
      where: { hash },
      include: {
        organization: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!key) {
      logger.warn("Invalid API key used", { keyPrefix: apiKey.substring(0, 10) });
      throw new HTTPException(401, { message: "Invalid API key" });
    }

    logger.debug("API key found in database", {
      keyId: key.id,
      organizationId: key.organizationId,
      hasCreatedBy: !!key.user
    });

    // Check if API key has expired
    if (key.expiresAt && key.expiresAt < new Date()) {
      logger.warn("Expired API key used", { keyId: key.id, keyPrefix: key.prefix });
      throw new HTTPException(401, { message: "API key has expired" });
    }

    // Update lastUsedAt
    try {
      logger.debug("Updating lastUsedAt for API key", { keyId: key.id });
      await db.api_key.update({
        where: { id: key.id },
        data: { lastUsedAt: new Date() }
      });
    } catch (updateError) {
      logger.warn("Failed to update lastUsedAt for API key", {
        keyId: key.id,
        error: updateError
      });
      // Continue even if update fails
    }

    logger.info("API key authenticated successfully", {
      keyId: key.id,
      organizationId: key.organizationId,
      keyPrefix: key.prefix
    });

    // Check if user exists
    if (!key.user) {
      logger.warn("API key has no associated user", { keyId: key.id });
      throw new HTTPException(401, { message: "Invalid API key configuration" });
    }

    // Add organization and API key info to context
    logger.debug("Setting context variables");
    c.set("organization", key.organization);
    c.set("apiKey", key);
    c.set("userId", key.createdById);

    // Create a mock session for the API key
    c.set("session", {
      id: `api-key-session-${key.id}`,
      userId: key.createdById,
      expiresAt: new Date(Date.now() + 3600 * 1000), // 1 hour from now
      token: `api-key-token-${key.id}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      activeOrganizationId: key.organizationId
    });

    // Create a mock user for the API key
    c.set("user", {
      id: key.createdById,
      name: key.user.name,
      email: key.user.email,
      role: "api-key"
    });

    logger.debug("API key authentication completed, proceeding to next middleware");
    await next();
  } catch (error) {
    if (error instanceof HTTPException) {
      logger.info("HTTP exception in API key authentication", {
        status: error.status,
        message: error.message
      });
      throw error;
    }

    logger.error("Error in API key authentication", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw new HTTPException(500, { message: "Internal server error during authentication" });
  }
};
