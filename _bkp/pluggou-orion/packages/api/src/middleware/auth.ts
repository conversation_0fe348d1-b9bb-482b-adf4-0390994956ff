import { type Session, auth } from "@repo/auth";
import { createMiddleware } from "hono/factory";
import { apiKeyAuthMiddleware } from "./apiKeyAuth";
import { logger } from "@repo/logs";

export const authMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
		organization: any;
		apiKey?: any;
	};
}>(async (c, next) => {
	try {
		// Check if API key is provided
		const apiKey = c.req.header("X-API-Key");

		if (apiKey) {
			// Use API key authentication
			logger.debug("Using API key authentication");
			return apiKeyAuthMiddleware(c, next);
		}

		// Use session authentication
		logger.debug("Using session authentication");
		const session = await auth.api.getSession({
			headers: c.req.raw.headers,
		});

		if (!session) {
			logger.warn("Authentication failed - no session or API key");
			return c.json({
				error: "Unauthorized",
				message: "Authentication required. Provide either a valid session or an API key in the X-API-Key header."
			}, 401);
		}

		logger.debug("Session authentication successful", { userId: session.user.id });
		c.set("session", session.session);
		c.set("user", session.user);

		await next();
	} catch (error) {
		logger.error("Error in auth middleware", {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined
		});

		if (error instanceof Response) {
			return error;
		}

		return c.json({
			error: "Authentication error",
			message: "An error occurred during authentication"
		}, 500);
	}
});
