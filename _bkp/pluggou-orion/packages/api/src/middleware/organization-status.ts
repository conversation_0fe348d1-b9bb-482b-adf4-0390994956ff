import { db } from "@repo/database";
import { Next, Context } from "hono";
import { HTTPException } from "hono/http-exception";

// Definir os possíveis status como um tipo
type OrgStatus = "PENDING_REVIEW" | "APPROVED" | "REJECTED" | "BLOCKED";

/**
 * Middleware para verificar o status da organização.
 * Bloqueia requisições se a organização não estiver aprovada.
 */
export async function organizationStatusMiddleware(
  c: Context,
  next: Next
): Promise<void> {
  const organizationId = c.get("organizationId");

  if (!organizationId) {
    return next();
  }

  // Buscar organização com seleção explícita do status
  const organization = await db.organization.findUnique({
    where: { id: organizationId },
    select: {
      id: true,
      status: true
    }
  });

  if (!organization) {
    throw new HTTPException(404, {
      message: "The organization was not found or no longer exists."
    });
  }

  // Aqui status será reconhecido corretamente
  if (organization.status === "BLOCKED") {
    throw new HTTPException(403, {
      message: "This organization has been blocked. Please contact support for more information."
    });
  }

  if (organization.status === "PENDING_REVIEW") {
    throw new HTTPException(403, {
      message: "This organization is pending review. You will be notified once approved."
    });
  }

  if (organization.status === "REJECTED") {
    throw new HTTPException(403, {
      message: "This organization's registration has been rejected. Please contact support for more information."
    });
  }

  await next();
}
