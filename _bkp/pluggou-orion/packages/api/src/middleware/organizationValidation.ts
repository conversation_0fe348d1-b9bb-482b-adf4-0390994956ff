import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { logger } from "@repo/logs";

/**
 * Middleware global para validar acesso de API keys à organização
 * Este middleware intercepta TODAS as requisições e verifica se a API key
 * tem permissão para acessar a organização especificada
 */
export const organizationValidationMiddleware = async (c: Context, next: () => Promise<void>) => {
  try {
    // Excluir rotas de admin da validação de organização
    const path = c.req.path;
    if (path.startsWith("/api/admin/")) {
      return await next();
    }

    // Verificar se é uma requisição com API key
    const apiKey = c.get("apiKey");
    const organization = c.get("organization");

    if (!apiKey || !organization) {
      // Não é uma requisição com API key, continuar
      return await next();
    }

    // Extrair organizationId da requisição
    let requestedOrganizationId: string | null = null;

    // Verificar diferentes locais onde organizationId pode estar
    const method = c.req.method;

    // 1. Verificar query parameters (GET requests)
    if (method === "GET") {
      requestedOrganizationId = c.req.query("organizationId") || null;
    }

    // 2. Verificar body parameters (POST/PUT/PATCH requests)
    if (method === "POST" || method === "PUT" || method === "PATCH") {
      try {
        const body = await c.req.json();
        requestedOrganizationId = body?.organizationId || null;
      } catch (error) {
        // Body não é JSON válido, continuar
      }
    }

    // 3. Verificar path parameters (ex: /transactions/:id)
    if (!requestedOrganizationId && path.includes("/transactions/")) {
      // Para endpoints que não especificam organizationId, usar a da API key
      requestedOrganizationId = organization.id;
    }

    // Se não encontrou organizationId, usar a da API key (comportamento padrão)
    if (!requestedOrganizationId) {
      requestedOrganizationId = organization.id;
    }

    // 🔒 VALIDAÇÃO CRÍTICA: Verificar se a API key tem acesso à organização solicitada
    if (organization.id !== requestedOrganizationId) {
      logger.warn("🚨 SECURITY ALERT: API key attempting to access different organization", {
        apiKeyId: apiKey.id,
        apiKeyOrganizationId: organization.id,
        requestedOrganizationId,
        method,
        path,
        userAgent: c.req.header("User-Agent"),
        ip: c.req.header("X-Forwarded-For") || c.req.header("X-Real-IP") || "unknown"
      });

      throw new HTTPException(403, {
        message: "API key does not have access to this organization"
      });
    }

    // ✅ Validação passou, continuar
    logger.debug("Organization validation passed", {
      apiKeyId: apiKey.id,
      organizationId: organization.id,
      method,
      path
    });

    await next();
  } catch (error) {
    if (error instanceof HTTPException) {
      throw error;
    }

    logger.error("Error in organization validation middleware", {
      error: error instanceof Error ? error.message : String(error),
      path: c.req.path,
      method: c.req.method
    });

    // Em caso de erro, continuar para não quebrar a aplicação
    await next();
  }
};
