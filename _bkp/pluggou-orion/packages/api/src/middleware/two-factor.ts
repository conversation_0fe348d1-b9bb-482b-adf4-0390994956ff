import { auth } from "@repo/auth";
import { Context, Next } from "hono";
import { HTTPException } from "hono/http-exception";

/**
 * Middleware to check if two-factor authentication is required
 * This middleware should be used in routes that require 2FA verification
 */
export async function twoFactorMiddleware(c: Context, next: Next) {
  try {
    // Get the session from the request
    const session = c.get("session");
    
    if (!session) {
      throw new HTTPException(401, { message: "Unauthorized" });
    }

    // Check if the user has 2FA enabled
    if (session.user.twoFactorEnabled) {
      // Check if the user has verified 2FA for this session
      const twoFactorVerified = session.user.twoFactorVerified;
      
      if (!twoFactorVerified) {
        // If 2FA is not verified, return a specific error
        throw new HTTPException(403, { 
          message: "Two-factor authentication required", 
          cause: { 
            code: "TWO_FACTOR_REQUIRED",
            userId: session.userId
          } 
        });
      }
    }

    // If 2FA is not enabled or is verified, continue
    return next();
  } catch (error) {
    if (error instanceof HTTPException) {
      throw error;
    }
    throw new HTTPException(500, { message: "Internal server error" });
  }
}
