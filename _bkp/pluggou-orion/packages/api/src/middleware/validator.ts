import { Context, MiddlewareHandler } from "hono";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";

export type ValidatorTargets = "query" | "body" | "param";

// Extend the Request type to include our validation method
declare module "hono" {
  interface ContextVariableMap {
    validated: Record<ValidatorTargets, unknown>;
  }

  interface Context {
    req: {
      valid: <T = unknown>(target: ValidatorTargets) => T;
    } & Context["req"];
  }
}

export function validator<T extends z.ZodType>(
  target: ValidatorTargets,
  schema: T
): MiddlewareHandler {
  return async (c: Context, next: () => Promise<void>) => {
    const value = await (async () => {
      if (target === "body") {
        return await c.req.json();
      }
      if (target === "query") {
        const query = Object.fromEntries(new URL(c.req.url).searchParams);
        return query;
      }
      if (target === "param") {
        return c.req.param();
      }
      return {};
    })();

    const result = schema.safeParse(value);

    if (!result.success) {
      const errors = result.error.errors.map((error) => ({
        path: error.path.join("."),
        message: error.message,
      }));

      throw new HTTPException(400, {
        message: `Validation failed: ${errors.map(e => `${e.path}: ${e.message}`).join(", ")}`,
      });
    }

    // Store the validated data
    const validatedData = result.data;

    // Add the validation function to the context
    (c.req as any).valid = function<T = unknown>(requestTarget: ValidatorTargets): T {
      if (requestTarget === target) {
        return validatedData as T;
      }
      throw new Error(`No validated data found for target: ${requestTarget}`);
    };

    await next();
  };
}
