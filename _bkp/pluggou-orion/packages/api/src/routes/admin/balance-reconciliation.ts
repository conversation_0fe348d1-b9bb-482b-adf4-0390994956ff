/**
 * Admin Balance Reconciliation Routes
 * Organizational-level balance reconciliation and error detection
 */

import { Hono } from "hono";
import { organizationalReconciliationService } from "@repo/payments/src/balance/organizational-reconciliation-service";
import { logger } from "@repo/logs";
import { db } from "@repo/database";

export const balanceReconciliationRouter = new Hono()
  /**
   * POST /balance-reconciliation/organizational
   * Get organizational reconciliation data
   */
  .post("/balance-reconciliation/organizational", async (c) => {
    try {
      const body = await c.req.json();
      const { organizationId, startDate, endDate } = body;

      // Validate input
      if (!organizationId) {
        return c.json({
          error: "organizationId is required"
        }, 400);
      }

      if (!startDate || !endDate) {
        return c.json({
          error: "startDate and endDate are required"
        }, 400);
      }

      const start = new Date(startDate);
      const end = new Date(endDate);

      // Validate dates
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({
          error: "Invalid date format"
        }, 400);
      }

      if (start > end) {
        return c.json({
          error: "startDate must be before endDate"
        }, 400);
      }

      logger.info("Organizational reconciliation requested", {
        organizationId,
        startDate: start,
        endDate: end
      });

      // Get reconciliation data
      const reconciliationData = await organizationalReconciliationService.reconcileOrganizationBalance(
        organizationId,
        start,
        end
      );

      return c.json({
        success: true,
        data: reconciliationData
      });
    } catch (error) {
      logger.error("Error in organizational reconciliation endpoint", {
        error,
        body: await c.req.json().catch(() => ({}))
      });

      return c.json({
        error: "Failed to perform organizational reconciliation",
        message: error instanceof Error ? error.message : "Unknown error"
      }, 500);
    }
  })

  /**
   * GET /balance-reconciliation/organizations
   * Get list of organizations for dropdown
   */
  .get("/balance-reconciliation/organizations", async (c) => {
    try {
      const organizations = await db.organization.findMany({
        select: {
          id: true,
          name: true
        },
        orderBy: {
          name: 'asc'
        }
      });

      return c.json({
        success: true,
        data: organizations
      });
    } catch (error) {
      logger.error("Error getting organizations list", { error });

      return c.json({
        error: "Failed to get organizations list",
        message: error instanceof Error ? error.message : "Unknown error"
      }, 500);
    }
  });

