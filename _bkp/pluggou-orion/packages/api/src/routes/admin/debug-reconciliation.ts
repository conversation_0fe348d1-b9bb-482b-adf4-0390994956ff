/**
 * API Route para Debug de Reconciliação
 * 
 * Endpoint: GET /api/admin/debug-reconciliation
 * 
 * Parâmetros:
 * - organizationId: string (obrigatório)
 * - startDate: string (ISO date, obrigatório)
 * - endDate: string (ISO date, obrigatório)
 * 
 * Exemplo:
 * GET /api/admin/debug-reconciliation?organizationId=org_123&startDate=2025-10-04T00:00:00.000Z&endDate=2025-10-04T23:59:59.999Z
 */

import { NextRequest, NextResponse } from "next/server";
import { organizationalReconciliationService } from "@repo/payments/balance/organizational-reconciliation-service";
import { logger } from "@repo/logs";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const organizationId = searchParams.get('organizationId');
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    // Validação dos parâmetros
    if (!organizationId) {
      return NextResponse.json(
        { error: 'organizationId é obrigatório' },
        { status: 400 }
      );
    }

    if (!startDateParam) {
      return NextResponse.json(
        { error: 'startDate é obrigatório' },
        { status: 400 }
      );
    }

    if (!endDateParam) {
      return NextResponse.json(
        { error: 'endDate é obrigatório' },
        { status: 400 }
      );
    }

    // Converter strings para Date
    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);

    // Validar datas
    if (isNaN(startDate.getTime())) {
      return NextResponse.json(
        { error: 'startDate deve ser uma data válida' },
        { status: 400 }
      );
    }

    if (isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'endDate deve ser uma data válida' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'startDate deve ser anterior a endDate' },
        { status: 400 }
      );
    }

    logger.info("Debug reconciliation API called", {
      organizationId,
      startDate,
      endDate
    });

    // Executar debug de reconciliação
    const debugResult = await organizationalReconciliationService.debugReconciliationDiscrepancy(
      organizationId,
      startDate,
      endDate
    );

    // Retornar resultado
    return NextResponse.json({
      success: true,
      data: debugResult,
      message: 'Debug de reconciliação executado com sucesso'
    });

  } catch (error) {
    logger.error("Error in debug reconciliation API", { error });
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

// Método POST para debug com body
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { organizationId, startDate, endDate } = body;

    // Validação dos parâmetros
    if (!organizationId) {
      return NextResponse.json(
        { error: 'organizationId é obrigatório' },
        { status: 400 }
      );
    }

    if (!startDate) {
      return NextResponse.json(
        { error: 'startDate é obrigatório' },
        { status: 400 }
      );
    }

    if (!endDate) {
      return NextResponse.json(
        { error: 'endDate é obrigatório' },
        { status: 400 }
      );
    }

    // Converter strings para Date
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Validar datas
    if (isNaN(startDateObj.getTime())) {
      return NextResponse.json(
        { error: 'startDate deve ser uma data válida' },
        { status: 400 }
      );
    }

    if (isNaN(endDateObj.getTime())) {
      return NextResponse.json(
        { error: 'endDate deve ser uma data válida' },
        { status: 400 }
      );
    }

    if (startDateObj >= endDateObj) {
      return NextResponse.json(
        { error: 'startDate deve ser anterior a endDate' },
        { status: 400 }
      );
    }

    logger.info("Debug reconciliation API called (POST)", {
      organizationId,
      startDate: startDateObj,
      endDate: endDateObj
    });

    // Executar debug de reconciliação
    const debugResult = await organizationalReconciliationService.debugReconciliationDiscrepancy(
      organizationId,
      startDateObj,
      endDateObj
    );

    // Retornar resultado
    return NextResponse.json({
      success: true,
      data: debugResult,
      message: 'Debug de reconciliação executado com sucesso'
    });

  } catch (error) {
    logger.error("Error in debug reconciliation API (POST)", { error });
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
