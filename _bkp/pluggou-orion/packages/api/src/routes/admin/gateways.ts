import { db } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";

export const gatewaysRouter = new Hono()
	.basePath("/gateways")
	.use(adminMiddleware)
	.get(
		"/",
		validator(
			"query",
			z.object({
				query: z.string().optional(),
				limit: z.string().optional().default("10").transform(Number),
				offset: z.string().optional().default("0").transform(Number),
				organizationId: z.string().optional(),
			}),
		),
		describeRoute({
			summary: "Get all payment gateways",
			tags: ["Administration"],
		}),
		async (c) => {
			const { query, limit, offset, organizationId } = c.req.valid("query");

			const whereClause: any = {};
			
			if (query) {
				whereClause.name = { contains: query, mode: "insensitive" };
			}
			
			if (organizationId) {
				whereClause.organizationId = organizationId;
			}

			const gateways = await db.payment_gateway.findMany({
				where: whereClause,
				include: {
					organization: {
						select: {
							name: true,
							slug: true,
						},
					},
				},
				take: limit,
				skip: offset,
				orderBy: [
					{ organizationId: 'asc' },
					{ isDefault: 'desc' },
					{ priority: 'asc' },
				],
			});

			const total = await db.payment_gateway.count({
				where: whereClause,
			});

			return c.json({ gateways, total });
		},
	)
	.get(
		"/:id",
		validator("param", z.object({ id: z.string() })),
		describeRoute({
			summary: "Get a payment gateway by ID",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");

			const gateway = await db.payment_gateway.findUnique({
				where: { id },
				include: {
					organization: {
						select: {
							name: true,
							slug: true,
						},
					},
				},
			});

			if (!gateway) {
				return c.json({ error: "Gateway not found" }, 404);
			}

			return c.json(gateway);
		},
	)
	.post(
		"/",
		validator(
			"json",
			z.object({
				name: z.string(),
				type: z.string(),
				credentials: z.record(z.any()),
				organizationId: z.string(),
				isActive: z.boolean().default(true),
				isDefault: z.boolean().default(false),
				priority: z.number().default(999),
			}),
		),
		describeRoute({
			summary: "Create a new payment gateway",
			tags: ["Administration"],
		}),
		async (c) => {
			const {
				name,
				type,
				credentials,
				organizationId,
				isActive,
				isDefault,
				priority,
			} = c.req.valid("json");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id: organizationId },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// If this gateway is set as default, unset any existing default
			if (isDefault) {
				await db.payment_gateway.updateMany({
					where: {
						organizationId,
						isDefault: true,
					},
					data: {
						isDefault: false,
					},
				});
			}

			// Create the gateway
			const gateway = await db.payment_gateway.create({
				data: {
					name,
					type,
					credentials,
					organizationId,
					isActive,
					isDefault,
					priority,
				},
			});

			return c.json(gateway, 201);
		},
	)
	.patch(
		"/:id",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				name: z.string().optional(),
				credentials: z.record(z.any()).optional(),
				isActive: z.boolean().optional(),
				isDefault: z.boolean().optional(),
				priority: z.number().optional(),
			}),
		),
		describeRoute({
			summary: "Update a payment gateway",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const {
				name,
				credentials,
				isActive,
				isDefault,
				priority,
			} = c.req.valid("json");

			// Check if gateway exists
			const existingGateway = await db.payment_gateway.findUnique({
				where: { id },
			});

			if (!existingGateway) {
				return c.json({ error: "Gateway not found" }, 404);
			}

			// If this gateway is set as default, unset any existing default
			if (isDefault) {
				await db.payment_gateway.updateMany({
					where: {
						organizationId: existingGateway.organizationId,
						isDefault: true,
						id: { not: id },
					},
					data: {
						isDefault: false,
					},
				});
			}

			// Update the gateway
			const gateway = await db.payment_gateway.update({
				where: { id },
				data: {
					...(name && { name }),
					...(credentials && { credentials }),
					...(isActive !== undefined && { isActive }),
					...(isDefault !== undefined && { isDefault }),
					...(priority !== undefined && { priority }),
				},
			});

			return c.json(gateway);
		},
	)
	.delete(
		"/:id",
		validator("param", z.object({ id: z.string() })),
		describeRoute({
			summary: "Delete a payment gateway",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");

			// Check if gateway exists
			const existingGateway = await db.payment_gateway.findUnique({
				where: { id },
			});

			if (!existingGateway) {
				return c.json({ error: "Gateway not found" }, 404);
			}

			// Check if gateway has transactions
			const transactionCount = await db.transaction.count({
				where: { gatewayId: id },
			});

			if (transactionCount > 0) {
				return c.json({
					error: "Cannot delete gateway with transactions. Deactivate it instead.",
				}, 400);
			}

			// Delete the gateway
			await db.payment_gateway.delete({
				where: { id },
			});

			return c.json({ success: true });
		},
	);
