/**
 * Improved Balance Reconciliation Routes
 * Enhanced balance reconciliation with correct cash flow calculations
 */

import { Hono } from "hono";
import { improvedReconciliationService } from "@repo/payments/src/balance/improved-reconciliation-service";
import { logger } from "@repo/logs";
import { db } from "@repo/database";

export const improvedReconciliationRouter = new Hono()
  /**
   * POST /improved-reconciliation/data
   * Get improved reconciliation data with correct calculations
   */
  .post("/improved-reconciliation/data", async (c) => {
    try {
      const body = await c.req.json();
      const { organizationId, startDate, endDate } = body;

      // Validate input
      if (!organizationId) {
        return c.json({
          error: "organizationId is required"
        }, 400);
      }

      if (!startDate || !endDate) {
        return c.json({
          error: "startDate and endDate are required"
        }, 400);
      }

      const start = new Date(startDate);
      const end = new Date(endDate);

      // Validate dates
      if (Number.isNaN(start.getTime()) || Number.isNaN(end.getTime())) {
        return c.json({
          error: "Invalid date format"
        }, 400);
      }

      if (start > end) {
        return c.json({
          error: "startDate must be before endDate"
        }, 400);
      }

      // Limit period to 90 days
      const daysDiff = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff > 90) {
        return c.json({
          error: "Period cannot exceed 90 days"
        }, 400);
      }

      logger.info("Improved reconciliation requested", {
        organizationId,
        startDate: start,
        endDate: end
      });

      // Get reconciliation data
      const reconciliationData = await improvedReconciliationService.getReconciliationData(
        organizationId,
        start,
        end
      );

      return c.json({
        success: true,
        data: reconciliationData
      });
    } catch (error) {
      logger.error("Error getting improved reconciliation data", {
        error,
        body: await c.req.json().catch(() => ({}))
      });

      return c.json({
        error: "Failed to get improved reconciliation data",
        message: error instanceof Error ? error.message : "Unknown error"
      }, 500);
    }
  })

  /**
   * GET /improved-reconciliation/organizations
   * Get list of organizations for dropdown
   */
  .get("/improved-reconciliation/organizations", async (c) => {
    try {
      const organizations = await db.organization.findMany({
        select: {
          id: true,
          name: true,
          slug: true
        },
        where: {
          // Only organizations with balance
          organizationBalance: {
            isNot: null
          }
        },
        orderBy: {
          name: 'asc'
        }
      });

      return c.json({
        success: true,
        data: organizations
      });
    } catch (error) {
      logger.error("Error getting organizations list", { error });

      return c.json({
        error: "Failed to get organizations list",
        message: error instanceof Error ? error.message : "Unknown error"
      }, 500);
    }
  });

