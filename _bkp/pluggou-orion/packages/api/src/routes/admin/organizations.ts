import { db } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";
import { getPaymentProvider } from "@repo/payments";

export const organizationRouter = new Hono()
	.basePath("/organizations")
	.use(adminMiddleware)
	.get(
		"/",
		validator(
			"query",
			z.object({
				query: z.string().optional(),
				limit: z.string().optional().default("10").transform(Number),
				offset: z.string().optional().default("0").transform(Number),
				status: z.enum(["PENDING_REVIEW", "APPROVED", "REJECTED", "BLOCKED"]).optional(),
			}),
		),
		describeRoute({
			summary: "Get all organizations",
			tags: ["Administration"],
		}),
		async (c) => {
			const { query, limit, offset, status } = c.req.valid("query");

			const organizations = await db.organization.findMany({
				where: {
					name: { contains: query, mode: "insensitive" },
					...(status ? { status } : {}),
				},
				include: {
					_count: {
						select: {
							member: true,
						},
					},
					organization_legal_info: status === "PENDING_REVIEW" ? true : false,
				},
				take: limit,
				skip: offset,
			});

			const total = await db.organization.count();

			return c.json({ organizations, total });
		},
	)
	.get(
		"/pending",
		describeRoute({
			summary: "Get organizations pending review",
			tags: ["Administration"],
		}),
		async (c) => {
			const organizations = await db.organization.findMany({
				where: {
					status: "PENDING_REVIEW",
				},
				include: {
					organization_legal_info: true,
				},
				orderBy: {
					createdAt: "desc",
				},
			});

			return c.json({ organizations });
		},
	)
	.get("/:id", async (c) => {
		const id = c.req.param("id");

		const organization = await db.organization.findUnique({
			where: { id },
			include: {
				member: true,
				invitation: true,
				organization_gateway: {
					include: {
						payment_gateway: true,
					},
					orderBy: [
						{ isDefault: 'desc' },
						{ priority: 'asc' },
					],
				},
				organization_legal_info: true,
				organization_taxes: true,
			},
		});

		return c.json(organization);
	})
	.post(
		"/:id/gateways",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				name: z.string(),
				type: z.string(),
				credentials: z.record(z.any()),
				isActive: z.boolean().default(true),
				isDefault: z.boolean().default(false),
				priority: z.number().default(999),
			}),
		),
		describeRoute({
			summary: "Add a payment gateway to an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const {
				name,
				type,
				credentials,
				isActive,
				isDefault,
				priority,
			} = c.req.valid("json");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// If this gateway is set as default, unset any existing default
			if (isDefault) {
				await db.payment_gateway.updateMany({
					where: {
						organizationId: id,
						isDefault: true,
					},
					data: {
						isDefault: false,
					},
				});
			}

			// Create the gateway
			const gateway = await db.payment_gateway.create({
				data: {
					name,
					type,
					credentials,
					organizationId: id,
					isActive,
					isDefault,
					priority,
				},
			});

			return c.json(gateway, 201);
		},
	)
	.get(
		"/:id/gateways",
		validator("param", z.object({ id: z.string() })),
		describeRoute({
			summary: "Get all payment gateways for an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Get all gateways for this organization
			const gateways = await db.payment_gateway.findMany({
				where: {
					organizationId: id,
				},
				orderBy: [
					{ isDefault: 'desc' },
					{ priority: 'asc' },
				],
			});

			return c.json({ gateways });
		},
	)
	.post(
		"/:id/gateways/test",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				gatewayId: z.string().optional(),
				gatewayType: z.string().optional(),
			}),
		),
		describeRoute({
			summary: "Test a payment gateway connection",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const { gatewayId, gatewayType } = c.req.valid("json");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			try {
				// Try to get the payment provider
				const provider = await getPaymentProvider(id, gatewayType);

				// If we get here, the provider was successfully created
				return c.json({ success: true, message: "Gateway connection successful" });
			} catch (error) {
				return c.json({
					success: false,
					message: "Gateway connection failed",
					error: error instanceof Error ? error.message : String(error)
				}, 400);
			}
		},
	)
	.post(
		"/:id/taxes",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				pixChargePercentFee: z.number().min(0).max(100),
				pixTransferPercentFee: z.number().min(0).max(100),
				pixChargeFixedFee: z.number().min(0),
				pixTransferFixedFee: z.number().min(0),
				gatewaySpecificTaxes: z.record(z.any()).optional(),
			}),
		),
		describeRoute({
			summary: "Set tax rates for an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const {
				pixChargePercentFee,
				pixTransferPercentFee,
				pixChargeFixedFee,
				pixTransferFixedFee,
				gatewaySpecificTaxes,
			} = c.req.valid("json");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Upsert the taxes (create or update)
			const taxes = await db.organization_taxes.upsert({
				where: {
					organizationId: id,
				},
				create: {
					organizationId: id,
					pixChargePercentFee,
					pixTransferPercentFee,
					pixChargeFixedFee,
					pixTransferFixedFee,
					...(gatewaySpecificTaxes ? { gatewaySpecificTaxes } : {}),
				},
				update: {
					pixChargePercentFee,
					pixTransferPercentFee,
					pixChargeFixedFee,
					pixTransferFixedFee,
					...(gatewaySpecificTaxes ? { gatewaySpecificTaxes } : {}),
				},
			});

			return c.json(taxes, 201);
		},
	)
	.get(
		"/:id/taxes",
		validator("param", z.object({ id: z.string() })),
		describeRoute({
			summary: "Get tax rates for an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Get taxes for this organization
			const taxes = await db.organization_taxes.findUnique({
				where: {
					organizationId: id,
				},
			});

			if (!taxes) {
				return c.json({
					pixChargePercentFee: 0,
					pixTransferPercentFee: 0,
					pixChargeFixedFee: 0,
					pixTransferFixedFee: 0,
				});
			}

			return c.json(taxes);
		},
	)
	.patch(
		"/:id/taxes",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				pixChargePercentFee: z.number().min(0).max(100).optional(),
				pixTransferPercentFee: z.number().min(0).max(100).optional(),
				pixChargeFixedFee: z.number().min(0).optional(),
				pixTransferFixedFee: z.number().min(0).optional(),
				gatewaySpecificTaxes: z.record(z.any()).optional(),
			}),
		),
		describeRoute({
			summary: "Update tax rates for an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const updateData = c.req.valid("json");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Update taxes
			const taxes = await db.organization_taxes.upsert({
				where: {
					organizationId: id,
				},
				create: {
					organizationId: id,
					...updateData,
				},
				update: updateData,
			});

			return c.json(taxes);
		},
	)
	.post(
		"/:id/approve",
		validator("param", z.object({ id: z.string() })),
		describeRoute({
			summary: "Approve an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const adminId = c.get("user").id;

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
				include: {
					legalInfo: true,
				},
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Update organization status
			const updated = await db.organization.update({
				where: { id },
				data: {
					status: "APPROVED",
				},
			});

			// Update legal info if exists
			if (organization.legalInfo) {
				await db.organizationLegalInfo.update({
					where: { organizationId: id },
					data: {
						reviewedBy: adminId,
						reviewedAt: new Date(),
					},
				});
			}

			// TODO: Send notification to organization members

			return c.json(updated);
		},
	)
	.post(
		"/:id/reject",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				reason: z.string(),
			}),
		),
		describeRoute({
			summary: "Reject an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const { reason } = c.req.valid("json");
			const adminId = c.get("user").id;

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
				include: {
					legalInfo: true,
				},
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Update organization status
			const updated = await db.organization.update({
				where: { id },
				data: {
					status: "REJECTED",
				},
			});

			// Update legal info if exists
			if (organization.legalInfo) {
				await db.organizationLegalInfo.update({
					where: { organizationId: id },
					data: {
						reviewedBy: adminId,
						reviewedAt: new Date(),
						reviewNotes: reason,
					},
				});
			}

			// TODO: Send notification to organization members with rejection reason

			return c.json(updated);
		},
	)
	.post(
		"/:id/block",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				reason: z.string(),
			}),
		),
		describeRoute({
			summary: "Block an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const { reason } = c.req.valid("json");
			const adminId = c.get("user").id;

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
				include: {
					legalInfo: true,
				},
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Update organization status
			const updated = await db.organization.update({
				where: { id },
				data: {
					status: "BLOCKED",
				},
			});

			// Update legal info notes if exists
			if (organization.legalInfo) {
				await db.organizationLegalInfo.update({
					where: { organizationId: id },
					data: {
						reviewedBy: adminId,
						reviewedAt: new Date(),
						reviewNotes: `BLOCKED: ${reason}`,
					},
				});
			}

			// TODO: Send notification to organization members

			return c.json(updated);
		},
	)
	.post(
		"/:id/unblock",
		validator("param", z.object({ id: z.string() })),
		describeRoute({
			summary: "Unblock an organization",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Update organization status
			const updated = await db.organization.update({
				where: { id },
				data: {
					status: "APPROVED",
				},
			});

			// TODO: Send notification to organization members

			return c.json(updated);
		},
	)
	.post(
		"/:id/status",
		validator("param", z.object({ id: z.string() })),
		validator(
			"json",
			z.object({
				status: z.enum(["PENDING_REVIEW", "APPROVED", "REJECTED", "BLOCKED"]),
				reason: z.string().optional(),
			}),
		),
		describeRoute({
			summary: "Update organization status",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const { status, reason } = c.req.valid("json");

			// Check if organization exists
			const organization = await db.organization.findUnique({
				where: { id },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Update organization status
			const updatedOrganization = await db.organization.update({
				where: { id },
				data: {
					status,
					// Add review notes if a reason was provided
					legalInfo: reason ? {
						upsert: {
							create: {
								reviewNotes: reason,
							},
							update: {
								reviewNotes: reason,
							},
						},
					} : undefined
				},
				include: {
					legalInfo: true,
				},
			});

			// Log the status change with reason if provided
			console.log(`Organization ${id} status changed to ${status}${reason ? ` (Reason: ${reason})` : ''}`);

			// Return updated organization
			return c.json(updatedOrganization);
		},
	);
