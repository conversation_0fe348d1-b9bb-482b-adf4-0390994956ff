import { Hono } from "hono";
import { ReserveAnalysisService } from "@repo/payments/src/balance/reserve-analysis-service";

const reserveAnalysisService = new ReserveAnalysisService();

export const reserveAnalysisRouter = new Hono()
  /**
   * GET /reserve-analysis/summary
   * Get reserve analysis summary
   */
  .get("/reserve-analysis/summary", async (c) => {
    try {
      console.log("Fetching reserve summary...");
      const summary = await reserveAnalysisService.getReserveSummary();
      console.log("Reserve summary fetched successfully:", summary);
      
      return c.json({
        success: true,
        data: summary
      });
    } catch (error) {
      console.error("Error fetching reserve summary:", error);
      return c.json(
        { 
          success: false, 
          error: error instanceof Error ? error.message : "Erro interno do servidor" 
        },
        500
      );
    }
  })

  /**
   * GET /reserve-analysis/organizations
   * Get organizations with reserve data
   */
  .get("/reserve-analysis/organizations", async (c) => {
    try {
      const { searchParams } = new URL(c.req.url);
      
      const filters = {
        minReserveAmount: searchParams.get('minReserveAmount') ? Number(searchParams.get('minReserveAmount')) : undefined,
        maxReserveAmount: searchParams.get('maxReserveAmount') ? Number(searchParams.get('maxReserveAmount')) : undefined,
        hasDivergence: searchParams.get('hasDivergence') ? searchParams.get('hasDivergence') === 'true' : undefined,
        status: searchParams.get('status') as 'NORMAL' | 'DIVERGENCE' | 'CRITICAL' | undefined,
        organizationId: searchParams.get('organizationId') || undefined,
        searchTerm: searchParams.get('searchTerm') || undefined,
        startDate: searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined,
        endDate: searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined,
      };

      const organizations = await reserveAnalysisService.getOrganizationsWithReserve(filters);
      
      return c.json({
        success: true,
        data: organizations
      });
    } catch (error) {
      console.error("Error fetching organizations with reserve:", error);
      return c.json(
        { 
          success: false, 
          error: error instanceof Error ? error.message : "Erro interno do servidor" 
        },
        500
      );
    }
  })

  /**
   * GET /reserve-analysis/[orgId]
   * Get organization reserve detail
   */
  .get("/reserve-analysis/:orgId", async (c) => {
    try {
      const orgId = c.req.param('orgId');
      
      if (!orgId) {
        return c.json(
          { success: false, error: "ID da organização é obrigatório" },
          400
        );
      }

      const reserveDetail = await reserveAnalysisService.getOrganizationReserveDetail(orgId);
      
      return c.json({
        success: true,
        data: reserveDetail
      });
    } catch (error) {
      console.error("Error fetching organization reserve detail:", error);
      return c.json(
        { 
          success: false, 
          error: error instanceof Error ? error.message : "Erro interno do servidor" 
        },
        500
      );
    }
  })

  /**
   * POST /reserve-analysis/export
   * Export reserve analysis report
   */
  .post("/reserve-analysis/export", async (c) => {
    try {
      const body = await c.req.json();
      const { filters } = body;

      const reportBuffer = await reserveAnalysisService.exportReserveReport(filters);
      
      return new Response(reportBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="reserve-analysis-report-${new Date().toISOString().split('T')[0]}.xlsx"`
        }
      });
    } catch (error) {
      console.error("Error exporting reserve report:", error);
      return c.json(
        { 
          success: false, 
          error: error instanceof Error ? error.message : "Erro interno do servidor" 
        },
        500
      );
    }
  });
