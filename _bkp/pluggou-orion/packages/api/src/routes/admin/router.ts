import { Hono } from "hono";
import { organizationRouter } from "./organizations";
import { userRouter } from "./users";
import { gatewaysRouter } from "./gateways";
import { setupMediusPagRouter } from "./setup-mediuspag";
import { adminTransactionsRouter } from "./transactions";
import { balanceReconciliationRouter } from "./balance-reconciliation";
import { reserveAnalysisRouter } from "./reserve-analysis-router";
import { improvedReconciliationRouter } from "./improved-reconciliation";

export const adminRouter = new Hono()
	.basePath("/admin")
	.route("/", organizationRouter)
	.route("/", userRouter)
	.route("/", gatewaysRouter)
	.route("/", adminTransactionsRouter)
	.route("/", balanceReconciliationRouter)
	.route("/", reserveAnalysisRouter)
	.route("/", improvedReconciliationRouter)
	.route("/setup-mediuspag", setupMediusPagRouter);
