import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { HTTPException } from "hono/http-exception";

// Credenciais da MediusPag (obtidas da documentação)
const MEDIUSPAG_API_KEY = "sk_like_LwKwZNenESZG6JESjKBk0oBmwBnm6WUTTxjYWvtn2rlHYyqY";
const MEDIUSPAG_COMPANY_ID = "7ec16908-b2a4-41c4-9f6e-b757007b6808";

export const setupMediusPagRouter = new Hono();

setupMediusPagRouter.get(
  "/",
  describeRoute({
    tags: ["Admin"],
    summary: "Configurar gateway MediusPag",
    description: "Configura o gateway MediusPag para uma organização específica",
    responses: {
      200: {
        description: "Gateway configurado com sucesso",
      },
      500: {
        description: "Erro ao configurar gateway",
      },
    },
  }),
  async (c) => {
    try {
      const organizationId = c.req.query("organizationId") || "_0gGPogYBZRPGnZJYbC1w";
      
      logger.info(`Configurando gateway MediusPag para organização ${organizationId}`);
      
      // Verificar se já existe um gateway MediusPag global
      const existingGlobalGateway = await db.payment_gateway.findFirst({
        where: {
          type: "MEDIUSPAG",
          isGlobal: true
        }
      });
      
      let gatewayId;
      
      // Se não existir um gateway global, criar um
      if (!existingGlobalGateway) {
        logger.info("Criando gateway MediusPag global...");
        
        const newGateway = await db.payment_gateway.create({
          data: {
            name: "MediusPag",
            type: "MEDIUSPAG",
            isActive: true,
            isGlobal: true,
            canReceive: true,
            canSend: true,
            priority: 10,
            credentials: {
              apiKey: MEDIUSPAG_API_KEY,
              companyId: MEDIUSPAG_COMPANY_ID,
              environment: "sandbox"
            }
          }
        });
        
        logger.info(`Gateway global criado com ID: ${newGateway.id}`);
        gatewayId = newGateway.id;
      } else {
        logger.info(`Gateway global já existe com ID: ${existingGlobalGateway.id}`);
        
        // Atualizar as credenciais do gateway global
        await db.payment_gateway.update({
          where: { id: existingGlobalGateway.id },
          data: {
            isActive: true,
            credentials: {
              apiKey: MEDIUSPAG_API_KEY,
              companyId: MEDIUSPAG_COMPANY_ID,
              environment: "sandbox"
            }
          }
        });
        
        logger.info("Credenciais do gateway global atualizadas");
        gatewayId = existingGlobalGateway.id;
      }
      
      // Verificar se já existe uma relação entre a organização e o gateway
      const existingRelation = await db.organization_gateway.findFirst({
        where: {
          organizationId: organizationId,
          gatewayId: gatewayId
        }
      });
      
      // Se não existir uma relação, criar uma
      if (!existingRelation) {
        logger.info("Criando relação entre organização e gateway...");
        
        // Desativar qualquer gateway padrão existente
        await db.organization_gateway.updateMany({
          where: {
            organizationId: organizationId,
            isDefault: true
          },
          data: {
            isDefault: false
          }
        });
        
        // Criar a relação
        const newRelation = await db.organization_gateway.create({
          data: {
            organizationId: organizationId,
            gatewayId: gatewayId,
            isActive: true,
            isDefault: true,
            priority: 1
          }
        });
        
        logger.info(`Relação criada com ID: ${newRelation.id}`);
      } else {
        logger.info(`Relação já existe com ID: ${existingRelation.id}`);
        
        // Atualizar a relação
        await db.organization_gateway.update({
          where: { id: existingRelation.id },
          data: {
            isActive: true,
            isDefault: true,
            priority: 1
          }
        });
        
        logger.info("Relação atualizada");
      }
      
      logger.info("Gateway MediusPag configurado com sucesso!");
      
      // Verificar a configuração
      const gateway = await db.payment_gateway.findUnique({
        where: { id: gatewayId }
      });
      
      const relation = await db.organization_gateway.findFirst({
        where: {
          organizationId: organizationId,
          gatewayId: gatewayId
        }
      });
      
      const credentials = gateway?.credentials as Record<string, any>;
      
      return c.json({
        success: true,
        message: "Gateway MediusPag configurado com sucesso",
        gateway: {
          id: gateway?.id,
          isActive: gateway?.isActive,
          isGlobal: gateway?.isGlobal,
          hasCredentials: !!gateway?.credentials,
          apiKey: credentials?.apiKey ? `${credentials.apiKey.substring(0, 5)}...${credentials.apiKey.substring(credentials.apiKey.length - 5)}` : null,
          companyId: credentials?.companyId,
          environment: credentials?.environment
        },
        relation: {
          id: relation?.id,
          isActive: relation?.isActive,
          isDefault: relation?.isDefault,
          priority: relation?.priority
        }
      });
    } catch (error) {
      logger.error("Erro ao configurar gateway MediusPag", { error });
      throw new HTTPException(500, { message: "Erro ao configurar gateway MediusPag" });
    }
  }
);
