import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { TransactionStatus } from "@prisma/client";
import { updateTransactionStatus } from "@repo/payments";
import { authMiddleware } from "../../middleware/auth";
import { adminAuthMiddleware } from "../../middleware/adminAuth";
import { Prisma } from "@prisma/client";

const approveTransactionSchema = z.object({
	reason: z.string().optional()
});

export const adminTransactionsRouter = new Hono()
	.basePath("/transactions")
	.get(
		"/list",
		authMiddleware,
		adminAuthMiddleware,
		validator("query", z.object({
			// Busca unificada por qualquer identificador
			searchId: z.string().optional(),
			// Busca específica por endToEndId
			searchEndToEndId: z.string().optional(),
			// Filtros específicos existentes
			searchClient: z.string().optional(),
			searchOrganization: z.string().optional(),
			// Filtros adicionais
			status: z.enum([
				"PENDING",
				"APPROVED", 
				"REJECTED",
				"CANCELED",
				"PROCESSING",
				"REFUNDED",
				"BLOCKED"
			]).optional(),
			type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
			gatewayId: z.string().optional(),
			startDate: z.string().optional(),
			endDate: z.string().optional(),
			page: z.string().transform(Number).default("1"),
			limit: z.string().transform(Number).default("10"),
		})),
		describeRoute({
			tags: ["Admin - Transactions"],
			summary: "List transactions with unified search",
			description: "Lists transactions with unified search across all identifier fields",
			responses: {
				200: {
					description: "Transactions retrieved successfully"
				},
				401: {
					description: "Unauthorized"
				},
				403: {
					description: "Forbidden - Admin access required"
				},
				500: {
					description: "Internal server error"
				}
			}
		}),
		async (c) => {
			try {
				const { 
					searchId, 
					searchEndToEndId,
					searchClient, 
					searchOrganization, 
					status, 
					type, 
					gatewayId, 
					startDate, 
					endDate, 
					page, 
					limit 
				} = c.req.valid("query");

				// Build the query - versão simplificada baseada na API original
				const where: any = {};

				// Busca específica por endToEndId
				if (searchEndToEndId) {
					where.OR = [
						{ endToEndId: { contains: searchEndToEndId, mode: "insensitive" } },
						// Busca no metadata para endToEndId
						{ 
							metadata: {
								path: ["endToEndId"],
								string_contains: searchEndToEndId
							}
						},
						// Busca no metadata para hubgames.endToEndId
						{ 
							metadata: {
								path: ["hubgames", "endToEndId"],
								string_contains: searchEndToEndId
							}
						},
						// Busca no metadata para pixEndToEndId
						{ 
							metadata: {
								path: ["pixEndToEndId"],
								string_contains: searchEndToEndId
							}
						}
					];
					
					// Log específico para busca por endToEndId
					logger.info("EndToEndId specific search", {
						searchEndToEndId,
						queryType: "endToEndId_search",
						orClauseCount: where.OR.length
					});
				}
				// Busca unificada por qualquer identificador
				else if (searchId) {
					where.OR = [
						{ id: { contains: searchId, mode: "insensitive" } },
						{ externalId: { contains: searchId, mode: "insensitive" } },
						{ endToEndId: { contains: searchId, mode: "insensitive" } },
						{ referenceCode: { contains: searchId, mode: "insensitive" } },
						{ customerDocument: { contains: searchId, mode: "insensitive" } },
						{ customerPhone: { contains: searchId, mode: "insensitive" } },
						// Busca no metadata usando string_contains (busca em todo o JSON)
						{ 
							metadata: {
								string_contains: searchId
							}
						},
						// Busca alternativa usando array_contains
						{ 
							metadata: {
								array_contains: searchId
							}
						}
					];
					
					// Log da query para debug
					logger.info("Search query with metadata", {
						searchId,
						queryType: "metadata_search",
						orClauseCount: where.OR.length
					});
					
					// Log específico para debug da busca
					logger.info("Search query details", {
						searchId,
						whereClause: where.OR,
						searchTerm: searchId
					});
					
					// Teste específico para endToEndId
					const endToEndTest = await db.transaction.findMany({
						where: {
							endToEndId: { contains: searchId, mode: "insensitive" }
						},
						select: {
							id: true,
							endToEndId: true,
							status: true
						},
						take: 3
					});
					
					logger.info("EndToEndId specific search", {
						searchTerm: searchId,
						found: endToEndTest.length,
						results: endToEndTest
					});
					
					// Teste adicional: buscar todas as transações com endToEndId para comparar
					const allEndToEndIds = await db.transaction.findMany({
						where: {
							endToEndId: { not: null },
							status: "APPROVED",
							type: "CHARGE"
						},
						select: {
							id: true,
							endToEndId: true,
							status: true
						},
						take: 5
					});
					
					logger.info("All endToEndIds for comparison", {
						count: allEndToEndIds.length,
						sample: allEndToEndIds
					});
				}

				// Filtros específicos
				if (searchClient) {
					where.OR = [
						...(where.OR || []),
						{ customerName: { contains: searchClient, mode: "insensitive" } },
						{ customerEmail: { contains: searchClient, mode: "insensitive" } },
					];
				}

				if (searchOrganization) {
					where.OR = [
						...(where.OR || []),
						{ 
							organization: {
								OR: [
									{ name: { contains: searchOrganization, mode: "insensitive" } },
									{ slug: { contains: searchOrganization, mode: "insensitive" } }
								]
							}
						}
					];
				}

				// Filtros adicionais - NÃO aplicar status e type se houver searchId (busca unificada)
				if (!searchId) {
					if (status) {
						where.status = status;
					}

					if (type) {
						where.type = type;
					}
				}

				if (gatewayId) {
					where.paymentGatewayId = gatewayId;
				}

				if (startDate || endDate) {
					where.createdAt = {};
					if (startDate) {
						where.createdAt.gte = new Date(startDate);
					}
					if (endDate) {
						where.createdAt.lte = new Date(endDate);
					}
				}

				// Log para debug
				logger.info("Admin transactions query", {
					where: JSON.stringify(where),
					page,
					limit,
					searchId,
					searchClient,
					searchOrganization,
					status,
					type,
					gatewayId,
					startDate,
					endDate
				});

				// Log específico para busca por endToEndId
				if (searchId) {
					logger.info("Searching for endToEndId", {
						searchId,
						searchTerm: searchId,
						query: {
							endToEndId: { contains: searchId, mode: "insensitive" }
						}
					});
				}

				// Teste simples: contar todas as transações
				const totalTransactions = await db.transaction.count();
				logger.info("Total transactions in database", { totalTransactions });

				// Get total count
				const total = await db.transaction.count({ where });

				// Get transactions with related data
				const transactions = await db.transaction.findMany({
					where,
					include: {
						organization: {
							select: {
								id: true,
								name: true,
								slug: true
							}
						},
						payment_gateway: {
							select: {
								id: true,
								name: true
							}
						}
					},
					orderBy: {
						createdAt: "desc"
					},
					skip: (page - 1) * limit,
					take: limit
				});

				logger.info("Admin transactions result", {
					total,
					found: transactions.length,
					page,
					limit
				});

				// Log para debug da organização e endToEndId
				logger.info("Admin transactions - organization and endToEndId data", {
					sample: transactions.slice(0, 2).map(tx => ({
						id: tx.id,
						endToEndId: tx.endToEndId,
						organization: tx.organization
					}))
				});

				return c.json({
					transactions: transactions.map((tx) => {
						// Extrair endToEndId do metadata se o campo estiver null
						const metadata = tx.metadata as any;
						const endToEndId = tx.endToEndId || metadata?.endToEndId || metadata?.pixEndToEndId || null;
						
						return {
							id: tx.id,
							externalId: tx.externalId,
							endToEndId: endToEndId,
							referenceCode: tx.referenceCode,
							customerName: tx.customerName,
							customerEmail: tx.customerEmail,
							customerDocument: tx.customerDocument,
							customerPhone: tx.customerPhone,
							amount: tx.amount,
							status: tx.status,
							type: tx.type,
							createdAt: tx.createdAt,
							paymentAt: tx.paymentAt,
							organization: tx.organization,
							gateway: tx.payment_gateway
						};
					}),
					pagination: {
						total,
						page,
						limit,
						pages: Math.ceil(total / limit)
					}
				});

			} catch (error) {
				logger.error("Error listing admin transactions", { error });
				
				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					message: "Internal server error"
				});
			}
		}
	)
	.get(
		"/test",
		authMiddleware,
		adminAuthMiddleware,
		describeRoute({
			tags: ["Admin - Transactions"],
			summary: "Test admin transactions endpoint",
			description: "Simple test to check if admin transactions endpoint works",
		}),
		async (c) => {
			try {
				// Teste simples: pegar as primeiras 5 transações
				const transactions = await db.transaction.findMany({
					take: 5,
					orderBy: {
						createdAt: "desc"
					}
				});

				const total = await db.transaction.count();

				return c.json({
					message: "Admin transactions test endpoint working",
					total,
					found: transactions.length,
					sample: transactions.map(tx => ({
						id: tx.id,
						status: tx.status,
						type: tx.type,
						amount: tx.amount,
						createdAt: tx.createdAt
					}))
				});

			} catch (error) {
				logger.error("Error in admin transactions test", { error });
				
				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					message: "Internal server error"
				});
			}
		}
	)
	.get(
		"/search-endtoend",
		authMiddleware,
		adminAuthMiddleware,
		validator("query", z.object({
			endToEndId: z.string()
		})),
		describeRoute({
			tags: ["Admin - Transactions"],
			summary: "Search by endToEndId",
			description: "Search transactions by endToEndId for debugging",
		}),
		async (c) => {
			try {
				const { endToEndId } = c.req.valid("query");

				logger.info("Searching by endToEndId", { endToEndId });

				// Busca específica por endToEndId
				const transactions = await db.transaction.findMany({
					where: {
						endToEndId: { contains: endToEndId, mode: "insensitive" }
					},
					include: {
						organization: {
							select: {
								id: true,
								name: true,
								slug: true
							}
						}
					},
					take: 5
				});

				logger.info("EndToEndId search results", {
					searchTerm: endToEndId,
					found: transactions.length,
					results: transactions.map(tx => ({
						id: tx.id,
						endToEndId: tx.endToEndId,
						organization: tx.organization?.name
					}))
				});

				return c.json({
					searchTerm: endToEndId,
					found: transactions.length,
					transactions: transactions.map(tx => ({
						id: tx.id,
						endToEndId: tx.endToEndId,
						organization: tx.organization?.name,
						status: tx.status,
						amount: tx.amount
					}))
				});

			} catch (error) {
				logger.error("Error searching by endToEndId", { error });
				
				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					message: "Internal server error"
				});
			}
		}
	)
	.get(
		"/debug-endtoend",
		authMiddleware,
		adminAuthMiddleware,
		describeRoute({
			tags: ["Admin - Transactions"],
			summary: "Debug endToEndId search",
			description: "Debug endpoint to check endToEndId data",
		}),
		async (c) => {
			try {
				// Buscar todas as transações com endToEndId
				const transactionsWithEndToEnd = await db.transaction.findMany({
					where: {
						endToEndId: { not: null }
					},
					select: {
						id: true,
						endToEndId: true,
						status: true,
						amount: true,
						createdAt: true
					},
					orderBy: {
						createdAt: "desc"
					},
					take: 20
				});

				// Buscar transações que contenham parte do ID
				const partialMatches = await db.transaction.findMany({
					where: {
						OR: [
							{ endToEndId: { contains: "E18236120202510072316", mode: "insensitive" } },
							{ endToEndId: { contains: "s188a4977ed", mode: "insensitive" } },
							{ endToEndId: { contains: "E18236120202510072316s188a4977ed", mode: "insensitive" } }
						]
					},
					select: {
						id: true,
						endToEndId: true,
						status: true
					}
				});

				return c.json({
					totalWithEndToEnd: transactionsWithEndToEnd.length,
					recentTransactions: transactionsWithEndToEnd.slice(0, 5),
					partialMatches: partialMatches,
					searchTerm: "E18236120202510072316s188a4977ed"
				});

			} catch (error) {
				logger.error("Error in debug endpoint", { error });
				throw new HTTPException(500, { message: "Internal server error" });
			}
		}
	)
	.get(
		"/summary",
		authMiddleware,
		adminAuthMiddleware,
		validator("query", z.object({
			type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
			status: z.enum([
				"PENDING",
				"APPROVED", 
				"REJECTED",
				"CANCELED",
				"PROCESSING",
				"REFUNDED",
				"BLOCKED"
			]).optional(),
		})),
		describeRoute({
			tags: ["Admin - Transactions"],
			summary: "Get transactions summary",
			description: "Get summary statistics for admin transactions",
			responses: {
				200: {
					description: "Summary retrieved successfully"
				},
				401: {
					description: "Unauthorized"
				},
				403: {
					description: "Forbidden - Admin access required"
				},
				500: {
					description: "Internal server error"
				}
			}
		}),
		async (c) => {
			try {
				const { type, status } = c.req.valid("query");

				// Build the query
				const where: Prisma.transactionWhereInput = {};

				if (type) {
					where.type = type;
				}

				if (status) {
					where.status = status;
				}

				// Get current period data
				const now = new Date();
				const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
				const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
				const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

				// Current period transactions
				const currentTransactions = await db.transaction.findMany({
					where: {
						...where,
						createdAt: {
							gte: currentMonthStart
						}
					}
				});

				// Previous period transactions
				const previousTransactions = await db.transaction.findMany({
					where: {
						...where,
						createdAt: {
							gte: lastMonthStart,
							lte: lastMonthEnd
						}
					}
				});

				// Calculate metrics
				const totalTransactions = {
					count: currentTransactions.length,
					growth: previousTransactions.length > 0 
						? ((currentTransactions.length - previousTransactions.length) / previousTransactions.length) * 100 
						: 0
				};

				const approvedTransactions = currentTransactions.filter(t => t.status === "APPROVED");
				const previousApproved = previousTransactions.filter(t => t.status === "APPROVED");
				const approvedTransactionsData = {
					count: approvedTransactions.length,
					growth: previousApproved.length > 0 
						? ((approvedTransactions.length - previousApproved.length) / previousApproved.length) * 100 
						: 0,
					approvalRate: currentTransactions.length > 0 
						? (approvedTransactions.length / currentTransactions.length) * 100 
						: 0
				};

				const pendingTransactions = currentTransactions.filter(t => t.status === "PENDING");
				const previousPending = previousTransactions.filter(t => t.status === "PENDING");
				const pendingTransactionsData = {
					count: pendingTransactions.length,
					growth: previousPending.length > 0 
						? ((pendingTransactions.length - previousPending.length) / previousPending.length) * 100 
						: 0
				};

				const financialVolume = {
					amount: currentTransactions.reduce((sum, t) => sum + t.amount, 0),
					growth: previousTransactions.length > 0 
						? ((currentTransactions.reduce((sum, t) => sum + t.amount, 0) - previousTransactions.reduce((sum, t) => sum + t.amount, 0)) / previousTransactions.reduce((sum, t) => sum + t.amount, 0)) * 100 
						: 0,
					averageTicket: currentTransactions.length > 0 
						? currentTransactions.reduce((sum, t) => sum + t.amount, 0) / currentTransactions.length 
						: 0
				};

				// Active organizations
				const currentOrganizations = await db.organization.count({
					where: {
						transaction: {
							some: {
								...where,
								createdAt: {
									gte: currentMonthStart
								}
							}
						}
					}
				});

				const previousOrganizations = await db.organization.count({
					where: {
						transaction: {
							some: {
								...where,
								createdAt: {
									gte: lastMonthStart,
									lte: lastMonthEnd
								}
							}
						}
					}
				});

				const activeOrganizations = {
					count: currentOrganizations,
					growth: previousOrganizations > 0 
						? ((currentOrganizations - previousOrganizations) / previousOrganizations) * 100 
						: 0
				};

				return c.json({
					totalTransactions,
					approvedTransactions: approvedTransactionsData,
					pendingTransactions: pendingTransactionsData,
					financialVolume,
					activeOrganizations
				});

			} catch (error) {
				logger.error("Error getting admin transactions summary", { error });
				
				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					message: "Internal server error"
				});
			}
		}
	)
	.post(
		"/:id/approve",
		authMiddleware,
		adminAuthMiddleware,
		validator("param", z.object({ id: z.string() })),
		validator("json", approveTransactionSchema),
		describeRoute({
			tags: ["Admin - Transactions"],
			summary: "Approve transaction",
			description: "Manually approve a pending transaction (admin only)",
			responses: {
				200: {
					description: "Transaction approved successfully"
				},
				400: {
					description: "Invalid request or transaction cannot be approved"
				},
				401: {
					description: "Unauthorized"
				},
				403: {
					description: "Forbidden - Admin access required"
				},
				404: {
					description: "Transaction not found"
				},
				500: {
					description: "Internal server error"
				}
			}
		}),
		async (c) => {
			try {
				const { id: transactionId } = c.req.valid("param");
				const { reason } = c.req.valid("json");
				const user = c.get("user");

				const transaction = await db.transaction.findUnique({
					where: { id: transactionId },
					include: {
						organization: {
							select: {
								id: true,
								name: true
							}
						}
					}
				});

				if (!transaction) {
					throw new HTTPException(404, {
						message: "Transação não encontrada"
					});
				}

				if (transaction.status !== "PENDING") {
					throw new HTTPException(400, {
						message: `Transação não pode ser aprovada. Status atual: ${transaction.status}. Apenas transações PENDING podem ser aprovadas manualmente.`
					});
				}

				logger.info("Iniciando aprovação manual de transação", {
					transactionId,
					organizationId: transaction.organizationId,
					amount: transaction.amount,
					type: transaction.type,
					currentStatus: transaction.status,
					adminUserId: user.id,
					reason
				});

				await db.transaction.update({
					where: { id: transactionId },
					data: {
						metadata: {
							...(transaction.metadata as any || {}),
							manuallyApprovedAt: new Date().toISOString(),
							manuallyApprovedBy: user.id,
							approvalReason: reason || "Aprovação manual pelo administrador",
							originalStatus: transaction.status,
							adminApproval: true
						}
					}
				});

				const updatedTransaction = await updateTransactionStatus(
					transactionId,
					TransactionStatus.APPROVED,
					new Date()
				);

				logger.info("Transação aprovada manualmente com sucesso", {
					transactionId,
					organizationId: transaction.organizationId,
					amount: transaction.amount,
					type: transaction.type,
					previousStatus: transaction.status,
					newStatus: updatedTransaction.status,
					adminUserId: user.id
				});

				return c.json({
					success: true,
					message: "Transação aprovada com sucesso",
					data: {
						transactionId,
						status: "APPROVED",
						amount: transaction.amount,
						type: transaction.type,
						organizationName: transaction.organization.name,
						approvedAt: updatedTransaction.paymentAt,
						approvedBy: user.id
					}
				});

			} catch (error) {
				logger.error("Erro ao aprovar transação manualmente", {
					transactionId: c.req.param("id"),
					error: error instanceof Error ? error.message : String(error),
					adminUserId: c.get("user")?.id
				});

				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					message: "Erro interno do servidor"
				});
			}
		}
	);
