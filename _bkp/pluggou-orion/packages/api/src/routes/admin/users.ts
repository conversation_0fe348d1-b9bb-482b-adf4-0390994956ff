import { db } from "@repo/database";
import { <PERSON>o } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";

export const userRouter = new Hono()
	.basePath("/users")
	.use(adminMiddleware)
	.get(
		"/",
		validator(
			"query",
			z.object({
				query: z.string().optional(),
				limit: z.string().optional().default("10").transform(Number),
				offset: z.string().optional().default("0").transform(Number),
			}),
		),
		describeRoute({
			summary: "Get all users",
			tags: ["Administration"],
		}),
		async (c) => {
			const { query, limit, offset } = c.req.valid("query");

			// Create a search condition that checks multiple fields
			const searchCondition = query
				? {
						OR: [
							{ name: { contains: query, mode: "insensitive" } },
							{ email: { contains: query, mode: "insensitive" } },
							{ username: { contains: query, mode: "insensitive" } },
						],
					}
				: {};

			const users = await db.user.findMany({
				where: searchCondition,
				take: limit,
				skip: offset,
			});

			// Count should also use the same search condition
			const total = await db.user.count({
				where: searchCondition,
			});

			return c.json({ users, total });
		},
	)
	.get(
		"/:id",
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		describeRoute({
			summary: "Get user by ID",
			tags: ["Administration"],
		}),
		async (c) => {
			const { id } = c.req.valid("param");

			const user = await db.user.findUnique({
				where: { id },
			});

			if (!user) {
				return c.json({ error: "User not found" }, 404);
			}

			return c.json(user);
		},
	);
