from fastapi import APIRouter, HTTPException, Depends, Request
from typing import Dict, Any
from datetime import datetime
import time
from app.models.pix import (
    PixCreateRequest, PixCreateResponse,
    PixStatusRequest, PixStatusResponse,
    PixRefundRequest, PixRefundResponse,
    CashOutCreateRequest, CashOutCreateResponse, CashOutStatusResponse,
    MicrocashTransferRequest, MicrocashTransferResponse,
    NineInBankTransferRequest, NineInBankTransferResponse,
    BalanceResponse, ErrorResponse
)
from app.services.ecomovi import ecomovi_service, cashout_service
from app.services.microcash import microcash_service
from app.services.nineinbank import nineinbank_service
from app.utils.logging import (
    logger, log_error, log_transaction, log_request, log_response
)
from app.config import settings
from app.validators.nineinbank_validator import NineInBankValidationError

router = APIRouter(prefix="/api/v1/pix", tags=["PIX"])

@router.post("/create", response_model=PixCreateResponse)
async def create_pix(request: PixCreateRequest):
    """Criar PIX payment (Cash-In)"""
    try:
        logger.info("Criando PIX (Cash-In)", extra={
            "amount": request.valor.original,
            "customer": request.devedor.nome,
            "provider": request.provider
        })

        # Usar provider padrão se não especificado
        provider = request.provider or settings.DEFAULT_PIX_PROVIDER
        
        # Validar provider
        if provider.lower() not in ["ecomovi", "microcash"]:
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado. Use 'ecomovi' ou 'microcash'"
            )

        if provider.lower() == "microcash":
            # Preparar dados para Microcash
            pix_data = {
                "devedor": {
                    "nome": request.devedor.nome,
                    "documento": request.devedor.documento
                },
                "valor": {
                    "original": request.valor.original
                },
                "calendario": {
                    "expiracao": request.calendario.expiracao
                },
                "solicitacaoPagador": request.solicitacaoPagador
            }
            
            logger.info("Chamando microcash_service.create_pix_payment", extra={
                "pix_data": pix_data
            })
            
            result = await microcash_service.create_pix_payment(pix_data)
            
            logger.info("Resposta do microcash_service", extra={
                "result": result,
                "result_keys": list(result.keys()) if isinstance(result, dict) else "not_dict"
            })
        else:
            # Converter request para formato Ecomovi
            logger.info("Payload recebido do Pluggou", extra={
                "devedor": request.devedor.dict(),
                "valor": request.valor.dict(),
                "provider": request.provider
            })

            devedor = {"nome": request.devedor.nome}
            logger.info("Processando tipo de documento", extra={
                "tipo": request.devedor.tipo,
                "documento": request.devedor.documento
            })

            if request.devedor.tipo == "CPF":
                devedor["cpf"] = request.devedor.documento
                logger.info("Adicionado CPF ao devedor", extra={"cpf": request.devedor.documento})
            elif request.devedor.tipo == "CNPJ":
                devedor["cnpj"] = request.devedor.documento
                logger.info("Adicionado CNPJ ao devedor", extra={"cnpj": request.devedor.documento})
            else:
                logger.error("Tipo de documento inválido", extra={"tipo": request.devedor.tipo})
                raise HTTPException(status_code=400, detail="Tipo de documento inválido")

            ecomovi_data = {
                "calendario": {
                    "expiracao": request.calendario.expiracao
                },
                "devedor": devedor,
                "valor": {
                    "original": request.valor.original
                },
                "chave": settings.ECOMOVI_PIX_KEY,
                "solicitacaoPagador": request.solicitacaoPagador[:137] + "..." if request.solicitacaoPagador and len(request.solicitacaoPagador) > 140 else request.solicitacaoPagador
            }

            logger.info("Payload enviado para Ecomovi", extra={
                "ecomovi_data": ecomovi_data
            })

            if request.infoAdicionais:
                ecomovi_data["infoAdicionais"] = [
                    {"nome": info.nome, "valor": info.valor}
                    for info in request.infoAdicionais
                ]

            # Permitir alternância POST/PUT para debug
            if hasattr(request, "force_post") and request.force_post:
                ecomovi_data["force_post"] = True
            if hasattr(request, "txid") and request.txid:
                ecomovi_data["txid"] = request.txid

            # Chamar serviço Ecomovi (cash-in)
            result = await ecomovi_service.create_pix(ecomovi_data, cash_out=False)

        logger.info("PIX criado com sucesso", extra={
            "transaction_id": result["transactionId"],
            "status": result["status"]
        })

        return PixCreateResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Erro detalhado ao criar PIX", extra={
            "error": str(e),
            "error_type": type(e).__name__,
            "provider": provider,
            "request_data": request.dict()
        })
        
        log_error(e, {"endpoint": "create_pix", "request": request.dict()})
        
        raise HTTPException(
            status_code=500,
            detail={
                "error": f"Erro interno ao criar PIX: {str(e)}",
                "error_type": type(e).__name__,
                "provider": provider,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@router.post("/create-cashout", response_model=CashOutCreateResponse)
async def create_pix_cashout(request: CashOutCreateRequest):
    """Criar PIX payment (Cash-Out) via ONZ Finance"""
    import time
    transaction_id = request.idempotencyKey or f"cashout-api-{int(time.time())}"
    
    try:
        log_transaction(
            "api_request", transaction_id, "RECEIVED",
            amount=request.payment.amount,
            pix_key=request.pixKey
        )

        # Validar provider - apenas ecomovi-cashout para cash-out
        if request.provider.lower() != "ecomovi-cashout":
            log_transaction(
                "api_validation_failed", transaction_id, "REJECTED",
                error_msg=f"Provider inválido: {request.provider}"
            )
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{request.provider}' não suportado para Cash-Out. Use 'ecomovi-cashout'"
            )

        logger.info(
            "Processando solicitação de PIX Cash-Out",
            operation="cashout_api_processing",
            transaction_id=transaction_id,
            pixKey=request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            amount=request.payment.amount,
            provider=request.provider
        )

        # Preparar dados para ONZ Finance conforme documentação
        onz_data = {
            "pixKey": request.pixKey,
            "priority": request.priority,
            "description": request.description,
            "paymentFlow": request.paymentFlow,
            "expiration": request.expiration,
            "payment": {
                "currency": request.payment.currency,
                "amount": request.payment.amount
            }
        }

        # Adicionar creditorDocument apenas se fornecido
        if request.creditorDocument:
            onz_data["creditorDocument"] = request.creditorDocument

        if request.endToEndId:
            onz_data["endToEndId"] = request.endToEndId
        if request.ispbDeny:
            onz_data["ispbDeny"] = request.ispbDeny
        if request.idempotencyKey:
            onz_data["idempotencyKey"] = request.idempotencyKey

        logger.info(
            "Enviando dados para ONZ Finance",
            operation="cashout_api_send_to_onz",
            transaction_id=transaction_id,
            payload_keys=list(onz_data.keys())
        )

        # Chamar serviço ONZ Finance (cash-out)
        result = await cashout_service.create_cashout(onz_data)

        log_transaction(
            "api_success", result["transactionId"], result["status"],
            amount=result["amount"],
            pix_key=result["pixKey"]
        )

        return CashOutCreateResponse(**result)

    except HTTPException as he:
        log_transaction(
            "api_http_error", transaction_id, "FAILED",
            error_msg=f"HTTP {he.status_code}: {he.detail}"
        )
        raise
    except Exception as e:
        log_transaction(
            "api_error", transaction_id, "FAILED",
            error_msg=str(e)
        )
        log_error(e, {
            "operation": "cashout_api",
            "endpoint": "create_pix_cashout", 
            "transaction_id": transaction_id,
            "request_data": {
                "pixKey": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
                "amount": request.payment.amount,
                "provider": request.provider
            }
        })
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao criar PIX Cash-Out: {str(e)}"
        )

@router.get("/status-cashout/{transaction_id}", response_model=CashOutStatusResponse)
async def get_pix_cashout_status(
    transaction_id: str, 
    provider: str = "ecomovi-cashout",
    end_to_end_id: str = None
):
    """Consultar status do PIX Cash-Out
    
    IMPORTANTE: Para consultar o status de transações cash-out na ONZ Finance,
    é obrigatório fornecer o endToEndId. O transaction_id sozinho não é suficiente
    conforme a documentação da API ONZ Finance.
    
    Args:
        transaction_id: ID da transação (usado para logs e referência)
        provider: Provider do cash-out (deve ser 'ecomovi-cashout')
        end_to_end_id: EndToEndId da transação PIX (OBRIGATÓRIO para consulta)
    """
    try:
        logger.info("Consultando status do PIX Cash-Out", extra={
            "transaction_id": transaction_id,
            "end_to_end_id": end_to_end_id,
            "provider": provider
        })

        # Validar provider
        if provider.lower() != "ecomovi-cashout":
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado para Cash-Out. Use 'ecomovi-cashout'"
            )

        # Validar endToEndId
        if not end_to_end_id:
            raise HTTPException(
                status_code=400,
                detail="endToEndId é obrigatório para consultar status de transações cash-out. "
                       "Conforme documentação ONZ Finance, o endpoint /pix/payments/{endToEndId} "
                       "requer o endToEndId como parâmetro (Required scopes: pix.read)."
            )

        # Chamar serviço ONZ Finance
        result = await cashout_service.get_cashout_status(transaction_id, end_to_end_id)

        logger.info("Status Cash-Out obtido com sucesso", extra={
            "transaction_id": transaction_id,
            "end_to_end_id": result["endToEndId"],
            "status": result["status"]
        })

        return CashOutStatusResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        log_error(e, {
            "endpoint": "get_pix_cashout_status", 
            "transaction_id": transaction_id,
            "end_to_end_id": end_to_end_id
        })
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao consultar status Cash-Out: {str(e)}"
        )

@router.get("/status/{transaction_id}", response_model=PixStatusResponse)
async def get_pix_status(transaction_id: str, provider: str = "ecomovi"):
    """Consultar status do PIX"""
    try:
        logger.info("Consultando status do PIX", extra={
            "transaction_id": transaction_id,
            "provider": provider
        })

        # Validar provider
        if provider.lower() not in ["ecomovi", "microcash"]:
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado. Use 'ecomovi' ou 'microcash'"
            )

        if provider.lower() == "microcash":
            result = await microcash_service.get_pix_status(transaction_id)
        else:
            # Chamar serviço Ecomovi
            result = await ecomovi_service.get_pix_status(transaction_id)

        logger.info("Status obtido com sucesso", extra={
            "transaction_id": transaction_id,
            "status": result["status"]
        })

        return PixStatusResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        log_error(e, {"endpoint": "get_pix_status", "transaction_id": transaction_id})
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao consultar status: {str(e)}"
        )

@router.post("/refund", response_model=PixRefundResponse)
async def process_refund(request: PixRefundRequest):
    """Processar devolução do PIX"""
    try:
        logger.info("Processando devolução", extra={
            "transaction_id": request.transactionId,
            "amount": request.amount,
            "provider": request.provider
        })

        # Validar provider
        if request.provider.lower() != "ecomovi":
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{request.provider}' não suportado. Use 'ecomovi'"
            )

        # Chamar serviço Ecomovi
        result = await ecomovi_service.process_refund(
            request.transactionId,
            request.amount,
            request.reason
        )

        logger.info("Devolução processada com sucesso", extra={
            "transaction_id": request.transactionId,
            "refund_id": result["refundId"]
        })

        return PixRefundResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        log_error(e, {"endpoint": "process_refund", "request": request.dict()})
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao processar devolução: {str(e)}"
        )

@router.delete("/cancel/{transaction_id}")
async def cancel_pix(transaction_id: str, provider: str = "ecomovi"):
    """Cancelar PIX"""
    try:
        logger.info("Cancelando PIX", extra={
            "transaction_id": transaction_id,
            "provider": provider
        })

        # Validar provider
        if provider.lower() != "ecomovi":
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado. Use 'ecomovi'"
            )

        # Chamar serviço Ecomovi
        result = await ecomovi_service.cancel_pix(transaction_id)

        logger.info("PIX cancelado com sucesso", extra={
            "transaction_id": transaction_id
        })

        return result

    except HTTPException:
        raise
    except Exception as e:
        log_error(e, {"endpoint": "cancel_pix", "transaction_id": transaction_id})
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao cancelar PIX: {str(e)}"
        )

@router.get("/cashout/health")
async def get_cashout_health():
    """Endpoint de monitoramento de saúde do serviço de cash-out"""
    try:
        logger.info("Consultando saúde do serviço de cash-out")
        
        # Obter métricas de saúde do serviço de autenticação
        health_metrics = cashout_auth_service.get_health_metrics()
        
        # Determinar status geral baseado nas métricas
        overall_status = "healthy" if health_metrics["service_status"] == "healthy" else "unhealthy"
        
        logger.info("Métricas de saúde obtidas com sucesso", extra={
            "auth_service_health": health_metrics,
            "overall_status": overall_status
        })
        
        return {
            "status": overall_status,
            "service": "cashout",
            "metrics": health_metrics,
            "timestamp": int(time.time()),
            "recommendations": _get_health_recommendations(health_metrics)
        }
        
    except Exception as e:
        log_error(e, {"endpoint": "get_cashout_health"})
        return {
            "status": "unhealthy",
            "service": "cashout",
            "error": str(e),
            "timestamp": int(time.time())
        }

@router.post("/cashout/health/reset")
async def reset_cashout_health():
    """Resetar métricas de saúde do serviço de cash-out"""
    try:
        logger.info("Resetando métricas de saúde do serviço de cash-out")
        
        # Obter métricas antes do reset
        old_metrics = cashout_auth_service.get_health_metrics()
        
        # Resetar métricas
        cashout_auth_service.reset_health_metrics()
        
        # Obter métricas após o reset
        new_metrics = cashout_auth_service.get_health_metrics()
        
        logger.info("Métricas de saúde resetadas com sucesso", extra={
            "old_metrics": old_metrics,
            "new_metrics": new_metrics
        })
        
        return {
            "status": "success",
            "message": "Métricas de saúde resetadas com sucesso",
            "old_metrics": old_metrics,
            "new_metrics": new_metrics,
            "timestamp": int(time.time())
        }
        
    except Exception as e:
        log_error(e, {"endpoint": "reset_cashout_health"})
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao resetar métricas de saúde: {str(e)}"
        )

@router.post("/cashout/token/refresh")
async def force_token_refresh():
    """Forçar renovação do token de cash-out"""
    try:
        logger.info("Forçando renovação do token de cash-out")
        
        # Obter informações do token antes da renovação
        old_token_info = cashout_auth_service.get_token_info()
        
        # Forçar renovação do token
        new_token = await cashout_auth_service.get_access_token(force_refresh=True)
        
        # Obter informações do token após a renovação
        new_token_info = cashout_auth_service.get_token_info()
        
        logger.info("Token renovado com sucesso", extra={
            "old_token_info": old_token_info,
            "new_token_info": new_token_info,
            "token_preview": new_token[:20] + "..." if new_token else None
        })
        
        return {
            "status": "success",
            "message": "Token renovado com sucesso",
            "old_token_info": old_token_info,
            "new_token_info": new_token_info,
            "timestamp": int(time.time())
        }
        
    except Exception as e:
        log_error(e, {"endpoint": "force_token_refresh"})
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao renovar token: {str(e)}"
        )

def _get_health_recommendations(metrics: dict) -> list:
    """Gerar recomendações baseadas nas métricas de saúde"""
    recommendations = []
    
    if metrics["consecutive_failures"] >= 3:
        recommendations.append("Considere verificar a conectividade com a API da ONZ Finance")
    
    if metrics["success_rate_percent"] < 80:
        recommendations.append("Taxa de sucesso baixa - verifique configurações de autenticação")
    
    token_info = metrics.get("token_info", {})
    if token_info.get("is_near_expiry", False):
        recommendations.append("Token próximo do vencimento - renovação automática será executada")
    
    if not token_info.get("has_token", False):
        recommendations.append("Nenhum token disponível - execute renovação manual se necessário")
    
    if not recommendations:
        recommendations.append("Serviço operando normalmente")
    
    return recommendations

@router.get("/balance", response_model=BalanceResponse)
async def get_account_balance(provider: str = "microcash", account_id: str = None):
    """Consultar saldo da conta"""
    start_time = time.time()
    
    try:
        logger.info("GET /api/v1/pix/balance - Iniciando consulta de saldo", extra={
            "provider": provider,
            "account_id": account_id,
            "timestamp": datetime.now().isoformat(),
            "endpoint": "get_account_balance"
        })

        # Validar provider
        logger.info("GET /api/v1/pix/balance - Validando provider", extra={
            "provider": provider,
            "provider_lower": provider.lower(),
            "supported_providers": ["ecomovi", "microcash"],
            "timestamp": datetime.now().isoformat()
        })

        if provider.lower() not in ["ecomovi", "microcash"]:
            logger.warning("GET /api/v1/pix/balance - Provider não suportado", extra={
                "provider": provider,
                "supported_providers": ["ecomovi", "microcash"],
                "timestamp": datetime.now().isoformat()
            })
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado. Use 'ecomovi' ou 'microcash'"
            )

        logger.info("GET /api/v1/pix/balance - Provider validado com sucesso", extra={
            "provider": provider,
            "account_id": account_id,
            "timestamp": datetime.now().isoformat()
        })

        if provider.lower() == "microcash":
            logger.info("GET /api/v1/pix/balance - Chamando microcash_service.get_account_balance", extra={
                "provider": provider,
                "account_id": account_id,
                "timestamp": datetime.now().isoformat()
            })
            
            result = await microcash_service.get_account_balance(account_id)
            
            logger.info("GET /api/v1/pix/balance - microcash_service.get_account_balance concluída", extra={
                "provider": provider,
                "account_id": account_id,
                "result_keys": list(result.keys()) if result else [],
                "result_balance": result.get("balance") if result else None,
                "result_balance_type": type(result.get("balance")).__name__ if result and "balance" in result else "missing",
                "result_provider_data": result.get("providerData") if result else None,
                "result_full": result,
                "timestamp": datetime.now().isoformat()
            })
        else:
            # Para Ecomovi, retornar erro pois não implementamos balance ainda
            logger.warning("GET /api/v1/pix/balance - Ecomovi não implementado", extra={
                "provider": provider,
                "account_id": account_id,
                "timestamp": datetime.now().isoformat()
            })
            raise HTTPException(
                status_code=501,
                detail="Endpoint de balance para Ecomovi ainda não implementado"
            )

        duration = time.time() - start_time
        
        logger.info("GET /api/v1/pix/balance - Saldo obtido com sucesso", extra={
            "provider": provider,
            "balance": result["balance"],
            "account_id": result.get("accountId"),
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        response = BalanceResponse(**result)
        
        logger.info("GET /api/v1/pix/balance - Resposta preparada", extra={
            "provider": provider,
            "account_id": account_id,
            "response_balance": response.balance,
            "response_account_id": response.accountId,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        return response

    except HTTPException:
        duration = time.time() - start_time
        logger.error("GET /api/v1/pix/balance - HTTPException", extra={
            "provider": provider,
            "account_id": account_id,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })
        raise
    except Exception as e:
        duration = time.time() - start_time
        logger.error("GET /api/v1/pix/balance - Exception", extra={
            "provider": provider,
            "account_id": account_id,
            "error": str(e),
            "error_type": type(e).__name__,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })
        log_error(e, {"endpoint": "get_account_balance", "provider": provider, "account_id": account_id})
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao consultar saldo: {str(e)}"
        )

@router.post("/transfer", response_model=MicrocashTransferResponse)
async def create_microcash_transfer(request: MicrocashTransferRequest):
    """Criar transferência PIX via Microcash (PIX OUT)"""
    start_time = time.time()
    
    try:
        logger.info("POST /api/v1/pix/transfer - Iniciando transferência Microcash", extra={
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "provider": request.provider,
            "timestamp": datetime.now().isoformat(),
            "endpoint": "create_microcash_transfer"
        })

        # Validar provider
        if request.provider.lower() != "microcash":
            logger.warning("POST /api/v1/pix/transfer - Provider não suportado", extra={
                "provider": request.provider,
                "supported_providers": ["microcash"],
                "timestamp": datetime.now().isoformat()
            })
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{request.provider}' não suportado. Use 'microcash'"
            )

        # Preparar dados para Microcash
        withdrawal_data = {
            "pixKey": request.pixKey,
            "payment": {
                "amount": request.amount
            }
        }

        logger.info("POST /api/v1/pix/transfer - Chamando microcash_service.process_pix_withdrawal", extra={
            "provider": request.provider,
            "withdrawal_data_keys": list(withdrawal_data.keys()),
            "timestamp": datetime.now().isoformat()
        })
        
        result = await microcash_service.process_pix_withdrawal(withdrawal_data)
        
        duration = time.time() - start_time
        
        logger.info("POST /api/v1/pix/transfer - Transferência processada com sucesso", extra={
            "provider": request.provider,
            "transaction_id": result["transactionId"],
            "status": result["status"],
            "amount": result["amount"],
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        response = MicrocashTransferResponse(**result)
        
        logger.info("POST /api/v1/pix/transfer - Resposta preparada", extra={
            "provider": request.provider,
            "transaction_id": response.transactionId,
            "status": response.status,
            "amount": response.amount,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        return response

    except HTTPException:
        duration = time.time() - start_time
        logger.error("POST /api/v1/pix/transfer - HTTPException", extra={
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })
        raise
    except Exception as e:
        duration = time.time() - start_time
        logger.error("POST /api/v1/pix/transfer - Exception", extra={
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "error": str(e),
            "error_type": type(e).__name__,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })
        log_error(e, {
            "endpoint": "create_microcash_transfer", 
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount
        })
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao processar transferência: {str(e)}"
        )

# ============================================================================
# ENDPOINTS PARA 9IN BANK CASHOUT
# ============================================================================

@router.post("/transfer-9inbank", response_model=NineInBankTransferResponse)
async def create_nineinbank_transfer(request: NineInBankTransferRequest, http_request: Request):
    """Criar transferência PIX via 9IN Bank (PIX OUT)"""
    start_time = time.time()
    
    try:
        logger.info("POST /api/v1/pix/transfer-9inbank - Iniciando transferência 9IN Bank", extra={
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "pix_key_type": request.pixKeyType,
            "amount": request.amount,
            "provider": request.provider,
            "timestamp": datetime.now().isoformat(),
            "endpoint": "create_nineinbank_transfer"
        })

        # Validar provider
        if request.provider.lower() != "9inbank":
            logger.warning("POST /api/v1/pix/transfer-9inbank - Provider não suportado", extra={
                "provider": request.provider,
                "supported_providers": ["9inbank"],
                "timestamp": datetime.now().isoformat()
            })
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{request.provider}' não suportado. Use '9inbank'"
            )

        # ENHANCED VALIDATION LAYER - Apply comprehensive validation and sanitization
        logger.info("ENHANCED VALIDATION: Starting validation", extra={
            "has_idempotency_key": bool(request.idempotencyKey),
            "has_pluggou_tx_id": bool(request.pluggouTransactionId),
            "pix_key_type": request.pixKeyType,
            "amount": request.amount
        })

        from app.validators.nineinbank_validator import nineinbank_validator

        # Convert request to dict for validation
        request_data = {
            "pixKey": request.pixKey,
            "pixKeyType": request.pixKeyType,
            "amount": request.amount,
            "customerName": request.customerName,
            "customerDocument": request.customerDocument,
            "customerDocumentType": request.customerDocumentType,
            "customerIp": request.customerIp,
            "postbackUrl": request.postbackUrl,
            "idempotencyKey": request.idempotencyKey,
            "pluggouTransactionId": request.pluggouTransactionId
        }

        logger.info("ENHANCED VALIDATION: About to call validator", extra={
            "request_data_keys": list(request_data.keys())
        })

        # Apply enhanced validation and sanitization
        validated_data, corrections = nineinbank_validator.validate_and_sanitize(request_data)

        logger.info("ENHANCED VALIDATION: Validation completed successfully", extra={
            "corrections_applied": len(corrections) > 0,
            "corrections": corrections
        })

        # Log any corrections applied
        if corrections:
            logger.info("Data corrections applied for 9IN Bank transfer", extra={
                "corrections": corrections,
                "original_amount": request.amount,
                "validated_amount": validated_data.get("amount"),
                "timestamp": datetime.now().isoformat()
            })

        # Use validated and sanitized data
        withdrawal_data = validated_data

        logger.info("POST /api/v1/pix/transfer-9inbank - Chamando nineinbank_service.process_pix_withdrawal", extra={
            "provider": request.provider,
            "withdrawal_data_keys": list(withdrawal_data.keys()),
            "timestamp": datetime.now().isoformat()
        })
        
        # Passar headers da requisição para capturar IP real do cliente
        request_headers = dict(http_request.headers)
        result = await nineinbank_service.process_pix_withdrawal(withdrawal_data, request_headers)
        
        duration = time.time() - start_time
        
        logger.info("POST /api/v1/pix/transfer-9inbank - Transferência processada com sucesso", extra={
            "provider": request.provider,
            "transaction_id": result["transactionId"],
            "status": result["status"],
            "amount": result["amount"],
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        response = NineInBankTransferResponse(**result)
        
        logger.info("POST /api/v1/pix/transfer-9inbank - Resposta preparada", extra={
            "provider": request.provider,
            "transaction_id": response.transactionId,
            "status": response.status,
            "amount": response.amount,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        return response

    except HTTPException:
        duration = time.time() - start_time
        logger.error("POST /api/v1/pix/transfer-9inbank - HTTPException", extra={
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })
        raise
    except NineInBankValidationError as e:
        # Handle validation errors from our enhanced validation layer
        duration = time.time() - start_time
        logger.warning("POST /api/v1/pix/transfer-9inbank - Validation Error", extra={
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "error_code": e.error_code,
            "field": e.field,
            "message": e.message,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat()
        })

        # Return HTTP 400 with specific error details
        raise HTTPException(
            status_code=400,
            detail={
                "success": False,
                "error": e.message,
                "errorCode": e.error_code,
                "field": e.field,
                "timestamp": datetime.now().isoformat()
            }
        )
    except Exception as e:
        duration = time.time() - start_time
        error_message = str(e) if str(e) else "Erro desconhecido"
        error_type = type(e).__name__
        
        logger.error("POST /api/v1/pix/transfer-9inbank - Exception", extra={
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "error": error_message,
            "error_type": error_type,
            "duration_seconds": round(duration, 3),
            "timestamp": datetime.now().isoformat(),
            "request_data": {
                "pixKey": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
                "pixKeyType": request.pixKeyType,
                "amount": request.amount,
                "customerName": request.customerName,
                "customerDocument": request.customerDocument[:4] + "***" if request.customerDocument else None,
                "customerDocumentType": request.customerDocumentType,
                "customerIp": request.customerIp,
                "idempotencyKey": request.idempotencyKey
            }
        })
        
        log_error(e, {
            "endpoint": "create_nineinbank_transfer", 
            "provider": request.provider,
            "pix_key": request.pixKey[:4] + "..." + request.pixKey[-4:] if len(request.pixKey) > 8 else "***",
            "amount": request.amount,
            "error_message": error_message,
            "error_type": error_type
        })
        
        # Retornar erro mais específico baseado no tipo de exceção
        if "Credenciais 9IN Bank não configuradas" in error_message:
            detail = f"Configuração inválida: {error_message}. Verifique as variáveis de ambiente NINEINBANK_PUBLIC_KEY e NINEINBANK_SECRET_KEY"
        elif "JSON" in error_message or "json" in error_message.lower():
            detail = f"Erro ao processar resposta da 9IN Bank: {error_message}"
        elif "timeout" in error_message.lower():
            detail = f"Timeout na comunicação com 9IN Bank: {error_message}"
        elif "connection" in error_message.lower():
            detail = f"Erro de conexão com 9IN Bank: {error_message}"
        else:
            detail = f"Erro interno ao processar transferência: {error_message}"
        
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": detail,
                "errorCode": "HTTP_500",
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@router.get("/status-9inbank/{transaction_id}", response_model=PixStatusResponse)
async def get_nineinbank_status(transaction_id: str, provider: str = "9inbank"):
    """Consultar status do PIX transfer 9IN Bank"""
    try:
        logger.info("Consultando status do PIX transfer 9IN Bank", extra={
            "transaction_id": transaction_id,
            "provider": provider
        })

        # Validar provider
        if provider.lower() != "9inbank":
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado. Use '9inbank'"
            )

        # Chamar serviço 9IN Bank
        result = await nineinbank_service.get_pix_status(transaction_id)

        logger.info("Status 9IN Bank obtido com sucesso", extra={
            "transaction_id": transaction_id,
            "status": result["status"]
        })

        return PixStatusResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        log_error(e, {"endpoint": "get_nineinbank_status", "transaction_id": transaction_id})
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao consultar status 9IN Bank: {str(e)}"
        )

@router.get("/query-9inbank", response_model=Dict[str, Any])
async def query_nineinbank_transfer(
    transfer_id: str = None, 
    client_identifier: str = None,
    provider: str = "9inbank"
):
    """Consultar transferência específica 9IN Bank usando ID ou clientIdentifier"""
    try:
        logger.info("Consultando transferência específica 9IN Bank", extra={
            "transfer_id": transfer_id,
            "client_identifier": client_identifier,
            "provider": provider
        })

        # Validar provider
        if provider.lower() != "9inbank":
            raise HTTPException(
                status_code=400,
                detail=f"Provider '{provider}' não suportado. Use '9inbank'"
            )

        # Validar parâmetros
        if not transfer_id and not client_identifier:
            raise HTTPException(
                status_code=400,
                detail="É necessário fornecer transfer_id ou client_identifier"
            )

        # Chamar serviço 9IN Bank
        result = await nineinbank_service.query_transfer(
            transfer_id=transfer_id,
            client_identifier=client_identifier
        )

        logger.info("Transferência 9IN Bank consultada com sucesso", extra={
            "transfer_id": result.get("id"),
            "client_identifier": result.get("clientIdentifier"),
            "status": result.get("status"),
            "amount": result.get("amount")
        })

        return result

    except HTTPException:
        raise
    except Exception as e:
        log_error(e, {
            "endpoint": "query_nineinbank_transfer", 
            "transfer_id": transfer_id,
            "client_identifier": client_identifier
        })
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao consultar transferência 9IN Bank: {str(e)}"
        )
