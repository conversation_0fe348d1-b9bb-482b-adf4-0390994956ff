from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from decimal import Decimal
from datetime import datetime

class PixCalendar(BaseModel):
    expiracao: int = Field(..., description="Tempo de expiração em segundos", ge=60, le=86400)

class PixDebtor(BaseModel):
    nome: str = Field(..., description="Nome do pagador", min_length=1, max_length=140)
    tipo: str = Field(..., description="Tipo do documento (CPF ou CNPJ)")
    documento: str = Field(..., description="Documento do pagador (apenas dígitos)", min_length=11, max_length=14)

    @validator('tipo')
    def validate_tipo(cls, v):
        if v not in ("CPF", "CNPJ"):
            raise ValueError('Tipo deve ser CPF ou CNPJ')
        return v

    @validator('documento')
    def validate_documento(cls, v, values):
        if not v.isdigit():
            raise ValueError('Documento deve conter apenas dígitos')

        # Validar comprimento baseado no tipo
        if 'tipo' in values:
            if values['tipo'] == 'CPF' and len(v) != 11:
                raise ValueError(f'CPF deve conter 11 dígitos, recebido: {len(v)}')
            if values['tipo'] == 'CNPJ' and len(v) != 14:
                raise ValueError(f'CNPJ deve conter 14 dígitos, recebido: {len(v)}')
        else:
            # Se não tem tipo, validar pelo comprimento
            if len(v) != 11 and len(v) != 14:
                raise ValueError(f'Documento deve conter 11 dígitos (CPF) ou 14 dígitos (CNPJ), recebido: {len(v)}')

        return v

class PixValue(BaseModel):
    original: str = Field(..., description="Valor em reais com 2 casas decimais")

    @validator('original')
    def validate_amount(cls, v):
        try:
            amount = Decimal(v)
            if amount <= 0:
                raise ValueError('Valor deve ser maior que zero')
            return str(amount.quantize(Decimal('0.01')))
        except:
            raise ValueError('Valor deve ser um número válido')

class PixAdditionalInfo(BaseModel):
    nome: str = Field(..., description="Nome da informação adicional", max_length=50)
    valor: str = Field(..., description="Valor da informação adicional", max_length=200)

class PixCreateRequest(BaseModel):
    calendario: PixCalendar
    devedor: PixDebtor
    valor: PixValue
    chave: str = Field(..., description="Chave PIX (EVP)")
    solicitacaoPagador: Optional[str] = Field(None, description="Descrição opcional", max_length=140)
    infoAdicionais: Optional[List[PixAdditionalInfo]] = Field(None, description="Informações adicionais")
    provider: str = Field("ecomovi", description="Provider PIX a ser usado")

    @validator('chave')
    def validate_pix_key(cls, v):
        if not v or len(v) < 1:
            raise ValueError('Chave PIX é obrigatória')
        return v

class PixCreateResponse(BaseModel):
    success: bool
    transactionId: str
    qrCode: str
    qrCodeText: str
    amount: float  # Em reais (não centavos)
    status: str
    expiresAt: int  # Timestamp
    createdAt: int  # Timestamp
    provider: str = "ecomovi"
    providerData: Dict[str, Any]
    error: Optional[str] = None

class PixStatusRequest(BaseModel):
    transactionId: str = Field(..., description="ID da transação")
    provider: str = Field("ecomovi", description="Provider PIX")

class PixStatusResponse(BaseModel):
    success: bool
    transactionId: str
    status: str
    amount: Optional[float] = None  # Em reais (não centavos)
    createdAt: Optional[int] = None
    updatedAt: Optional[int] = None
    provider: str = "ecomovi"
    providerData: Dict[str, Any]
    error: Optional[str] = None

class PixRefundRequest(BaseModel):
    transactionId: str = Field(..., description="ID da transação")
    amount: float = Field(..., description="Valor em reais", gt=0)  # Em reais (não centavos)
    reason: Optional[str] = Field(None, description="Motivo da devolução", max_length=140)
    provider: str = Field("ecomovi", description="Provider PIX")

class PixRefundResponse(BaseModel):
    success: bool
    refundId: str
    transactionId: str
    amount: float  # Em reais (não centavos)
    status: str
    createdAt: int
    provider: str = "ecomovi"
    providerData: Dict[str, Any]
    error: Optional[str] = None

class BalanceResponse(BaseModel):
    success: bool
    balance: float = Field(..., description="Saldo atual da conta")
    timestamp: str = Field(..., description="Timestamp da consulta")
    provider: str = Field(..., description="Provider utilizado")
    accountId: Optional[str] = Field(None, description="ID da conta")
    providerData: Optional[Dict[str, Any]] = Field(None, description="Dados adicionais do provider")

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    errorCode: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str = "1.0.1"
    providers: Dict[str, str]
    auth: str

# ============================================================================
# MODELS PARA CASH-OUT (ONZ Finance)
# ============================================================================

class CashOutPayment(BaseModel):
    currency: str = Field("BRL", description="Moeda do pagamento")
    amount: float = Field(..., description="Valor em reais", gt=0)

class CashOutCreditorAccount(BaseModel):
    ispb: str = Field(..., description="ISPB do banco", min_length=8, max_length=8)
    issuer: str = Field(..., description="Nome do banco")
    number: str = Field(..., description="Número da conta")
    accountType: str = Field(..., description="Tipo da conta (SLRY, CACC, SVGS, etc.)")
    document: str = Field(..., description="CPF/CNPJ do titular")
    name: str = Field(..., description="Nome do titular")

class CashOutCreateRequest(BaseModel):
    pixKey: str = Field(..., description="Chave PIX de destino (CPF, CNPJ, email, telefone, EVP)")
    creditorDocument: Optional[str] = Field(None, description="CPF/CNPJ do recebedor (opcional conforme documentação ONZ Finance)")
    priority: str = Field("HIGH", description="Prioridade da transferência")
    description: str = Field(..., description="Descrição da transferência", max_length=140)
    paymentFlow: str = Field("INSTANT", description="Tipo de fluxo de pagamento")
    expiration: int = Field(600, description="Tempo de expiração em segundos", ge=1, le=10800)
    payment: CashOutPayment
    endToEndId: Optional[str] = Field(None, description="ID único obtido através da consulta de chave PIX")
    ispbDeny: Optional[List[str]] = Field(None, description="Lista de ISPB negados")
    idempotencyKey: Optional[str] = Field(None, description="Chave de idempotência para evitar duplicação")
    provider: str = Field("ecomovi-cashout", description="Provider PIX a ser usado")

    @validator('priority')
    def validate_priority(cls, v):
        valid_priorities = ["HIGH", "NORM"]
        if v not in valid_priorities:
            raise ValueError(f'Prioridade deve ser um dos: {", ".join(valid_priorities)}')
        return v

    @validator('paymentFlow')
    def validate_payment_flow(cls, v):
        valid_flows = ["INSTANT", "APPROVAL_REQUIRED"]
        if v not in valid_flows:
            raise ValueError(f'Fluxo de pagamento deve ser um dos: {", ".join(valid_flows)}')
        return v

class CashOutCreateResponse(BaseModel):
    success: bool
    transactionId: str
    endToEndId: str
    status: str
    amount: float  # Em reais
    createdAt: int  # Timestamp
    provider: str = "ecomovi-cashout"
    providerData: Dict[str, Any]
    error: Optional[str] = None

class CashOutStatusResponse(BaseModel):
    success: bool
    transactionId: str
    endToEndId: str
    status: str
    amount: Optional[float] = None  # Em reais
    createdAt: Optional[int] = None
    updatedAt: Optional[int] = None
    provider: str = "ecomovi-cashout"
    providerData: Dict[str, Any]
    error: Optional[str] = None

# ============================================================================
# MODELS PARA TRANSFERÊNCIA MICROCASH (PIX OUT)
# ============================================================================

class MicrocashTransferRequest(BaseModel):
    pixKey: str = Field(..., description="Chave PIX de destino (CPF, CNPJ, email, telefone, EVP)")
    amount: float = Field(..., description="Valor em reais", gt=0)
    description: Optional[str] = Field(None, description="Descrição da transferência", max_length=140)
    provider: str = Field("microcash", description="Provider PIX a ser usado")

    @validator('pixKey')
    def validate_pix_key(cls, v):
        if not v or len(v) < 1:
            raise ValueError('Chave PIX é obrigatória')
        return v

class MicrocashTransferResponse(BaseModel):
    success: bool
    transactionId: str
    endToEndId: Optional[str] = None
    status: str
    amount: float  # Em reais
    createdAt: int  # Timestamp
    provider: str = "microcash"
    providerData: Dict[str, Any]
    error: Optional[str] = None

# ============================================================================
# MODELS PARA 9IN BANK CASHOUT
# ============================================================================

class NineInBankTransferRequest(BaseModel):
    pixKey: str = Field(..., description="Chave PIX de destino (CPF, CNPJ, email, telefone, EVP)")
    pixKeyType: str = Field("EMAIL", description="Tipo da chave PIX (EMAIL, PHONE, CPF, CNPJ) - RANDOM não suportado")
    amount: float = Field(..., description="Valor em reais (mínimo R$ 10,00)", gt=0)
    customerName: Optional[str] = Field("Cliente", description="Nome do cliente (apenas letras e espaços)", max_length=140)
    customerDocument: Optional[str] = Field("***********", description="CPF/CNPJ do cliente (será usado padrão se vazio)")
    customerDocumentType: Optional[str] = Field("cpf", description="Tipo do documento (cpf/cnpj)")
    customerIp: Optional[str] = Field(None, description="IP do cliente (será determinado automaticamente se não fornecido)")
    postbackUrl: Optional[str] = Field(None, description="URL de callback para notificações")
    idempotencyKey: Optional[str] = Field(None, description="Chave de idempotência (obrigatório se pluggouTransactionId não fornecido)")
    pluggouTransactionId: Optional[str] = Field(None, description="ID da transação no Pluggou (obrigatório se idempotencyKey não fornecido)")
    provider: str = Field("9inbank", description="Provider PIX a ser usado")

    @validator('pixKey')
    def validate_pix_key(cls, v):
        if not v or len(v) < 1:
            raise ValueError('Chave PIX é obrigatória')
        return v.strip()

    @validator('pixKeyType')
    def validate_pix_key_type(cls, v):
        # Basic validation - detailed validation happens in the validator layer
        valid_types = ["EMAIL", "PHONE", "CPF", "CNPJ", "RANDOM"]
        if v not in valid_types:
            raise ValueError(f'Tipo de chave PIX deve ser um dos: {", ".join(valid_types)}')
        return v.upper().strip()

    @validator('customerDocumentType')
    def validate_document_type(cls, v):
        if v and v.lower() not in ["cpf", "cnpj"]:
            raise ValueError('Tipo de documento deve ser "cpf" ou "cnpj"')
        return v.lower() if v else "cpf"

    @validator('amount')
    def validate_amount_basic(cls, v):
        if v <= 0:
            raise ValueError('Valor deve ser maior que zero')
        return round(v, 2)  # Round to 2 decimal places

    @validator('customerName')
    def validate_customer_name_basic(cls, v):
        if v:
            return v.strip()
        return v

class NineInBankTransferResponse(BaseModel):
    success: bool
    transactionId: str  # ID da transação do Pluggou (pluggouTransactionId)
    endToEndId: str     # ID da transação da 9inbank
    status: str
    amount: float  # Em reais
    createdAt: int  # Timestamp
    provider: str = "9inbank"
    providerData: Dict[str, Any]  # Contém nineinbankTransactionId e pluggouTransactionId
    webhookToken: Optional[str] = None
    receiptUrl: Optional[str] = None
    error: Optional[str] = None
