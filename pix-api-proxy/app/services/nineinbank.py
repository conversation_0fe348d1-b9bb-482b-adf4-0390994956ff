import httpx
import asyncio
import uuid
import hashlib
import hmac
import random
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from app.config import settings
from app.utils.logging import logger, log_error, log_transaction

class NineInBankAuthService:
    def __init__(self):
        self._access_token: Optional[str] = None
        self._expires_at: Optional[datetime] = None
        self._token_lock = asyncio.Lock()
    
    def _is_token_valid(self, buffer_minutes: int = 10) -> bool:
        if not self._access_token or not self._expires_at:
            return False
        buffer_time = timedelta(minutes=buffer_minutes)
        return datetime.utcnow() < (self._expires_at - buffer_time)
    
    async def get_access_token(self, force_refresh: bool = False) -> str:
        if not force_refresh and self._is_token_valid():
            return self._access_token
        
        async with self._token_lock:
            if not force_refresh and self._is_token_valid():
                return self._access_token
            
            # Limpar token anterior
            self._access_token = None
            self._expires_at = None
            
            try:
                # Para 9IN Bank, não precisamos de token OAuth2
                # Usamos as chaves diretamente nos headers
                logger.info("9IN Bank usa autenticação via headers (x-public-key, x-secret-key)")
                return "9inbank_auth_via_headers"
                
            except Exception as e:
                logger.error(f"Erro ao obter credenciais 9IN Bank: {str(e)}")
                raise

    async def get_auth_headers(self) -> dict:
        """Obter headers de autenticação para 9IN Bank"""
        # Validar se as credenciais estão configuradas
        if not settings.NINEINBANK_PUBLIC_KEY or not settings.NINEINBANK_SECRET_KEY:
            logger.error("Credenciais 9IN Bank não configuradas", extra={
                "has_public_key": bool(settings.NINEINBANK_PUBLIC_KEY),
                "has_secret_key": bool(settings.NINEINBANK_SECRET_KEY),
                "public_key_prefix": settings.NINEINBANK_PUBLIC_KEY[:8] + "..." if settings.NINEINBANK_PUBLIC_KEY else "vazio",
                "secret_key_prefix": settings.NINEINBANK_SECRET_KEY[:8] + "..." if settings.NINEINBANK_SECRET_KEY else "vazio"
            })
            raise ValueError("Credenciais 9IN Bank não configuradas. Verifique NINEINBANK_PUBLIC_KEY e NINEINBANK_SECRET_KEY")
        
        logger.info("Credenciais 9IN Bank validadas", extra={
            "public_key_prefix": settings.NINEINBANK_PUBLIC_KEY[:8] + "...",
            "secret_key_prefix": settings.NINEINBANK_SECRET_KEY[:8] + "..."
        })
        
        return {
            'x-public-key': settings.NINEINBANK_PUBLIC_KEY,
            'x-secret-key': settings.NINEINBANK_SECRET_KEY,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Pluggou-9INBank/1.0'
        }

# Instância global
nineinbank_auth_service = NineInBankAuthService()

class NineInBankService:
    def __init__(self):
        self.base_url = settings.NINEINBANK_API_URL or "https://portal.9inbank.com.br/api/v1"
        # Lista de IPs brasileiros para rotacionar
        self.brazilian_ips = [
            # Telefônica Brasil
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            
            # Vivo
            "**********", "**********", "**********", "**********", "**********",
            "**********", "**********", "**********", "**********", "**********",
            "**********", "**********", "**********", "**********", "**********",
            
            # Oi
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********", "***********",
            
            # Claro
            "***********", "***********", "***********", "***********", "200.160.0.5",
            "200.160.1.1", "200.160.1.2", "200.160.1.3", "200.160.1.4", "200.160.1.5",
            "200.160.2.1", "200.160.2.2", "200.160.2.3", "200.160.2.4", "200.160.2.5",
            
            # TIM
            "187.45.0.1", "187.45.0.2", "187.45.0.3", "187.45.0.4", "187.45.0.5",
            "187.45.1.1", "187.45.1.2", "187.45.1.3", "187.45.1.4", "187.45.1.5",
            "187.45.2.1", "187.45.2.2", "187.45.2.3", "187.45.2.4", "187.45.2.5",
            
            # Algar Telecom
            "201.17.0.1", "201.17.0.2", "201.17.0.3", "201.17.0.4", "201.17.0.5",
            "201.17.1.1", "201.17.1.2", "201.17.1.3", "201.17.1.4", "201.17.1.5",
            
            # Sercomtel
            "200.160.3.1", "200.160.3.2", "200.160.3.3", "200.160.3.4", "200.160.3.5",
            "200.160.4.1", "200.160.4.2", "200.160.4.3", "200.160.4.4", "200.160.4.5",
            
            # Copel Telecom
            "200.160.5.1", "200.160.5.2", "200.160.5.3", "200.160.5.4", "200.160.5.5",
            "200.160.6.1", "200.160.6.2", "***********", "***********", "***********",
            
            # GVT
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            
            # NET
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            
            # IPs adicionais para maior variedade
            "**********", "**********", "**********", "**********", "**********",
            "**********", "**********", "**********", "**********", "**********",
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********", "***********",
            "**********", "**********", "**********", "**********", "**********",
            "**********", "**********", "**********", "**********", "**********",
        ]
    
    def get_client_ip(self, request_headers: dict = None) -> str:
        """Retorna o IP real do cliente ou um IP brasileiro como fallback"""
        if request_headers:
            # Tentar obter IP real do cliente através dos headers
            client_ip = (
                request_headers.get("x-forwarded-for", "").split(",")[0].strip() or
                request_headers.get("x-real-ip", "").strip() or
                request_headers.get("x-client-ip", "").strip() or
                request_headers.get("cf-connecting-ip", "").strip() or  # Cloudflare
                request_headers.get("x-forwarded", "").strip() or
                request_headers.get("forwarded-for", "").strip() or
                request_headers.get("forwarded", "").strip()
            )
            
            # SEMPRE usar o IP real do cliente se disponível, independente de ser brasileiro ou não
            if client_ip:
                logger.info("IP real do cliente encontrado", extra={
                    "client_ip": client_ip,
                    "is_brazilian": self._is_brazilian_ip(client_ip)
                })
                return client_ip
        
        # Fallback: usar IP brasileiro aleatório da lista apenas se não houver IP real
        logger.warning("Nenhum IP real do cliente encontrado, usando IP brasileiro aleatório")
        return random.choice(self.brazilian_ips)
    
    def _is_brazilian_ip(self, ip: str) -> bool:
        """Verifica se o IP é brasileiro baseado nos ranges conhecidos"""
        try:
            import ipaddress
            ip_obj = ipaddress.ip_address(ip)
            
            # Ranges de IPs brasileiros conhecidos
            brazilian_ranges = [
                "*********/8",      # Vivo
                "*********/8",      # Oi
                "***********/16",   # Claro
                "**********/16",    # Telefônica Brasil
                "*********/8",      # TIM
                "**********/16",    # Algar Telecom
            ]
            
            for range_str in brazilian_ranges:
                if ip_obj in ipaddress.ip_network(range_str):
                    return True
            return False
        except:
            return False
    
    def _map_status_to_internal(self, nineinbank_status: str) -> str:
        """Mapear status do 9IN Bank para status interno"""
        if not nineinbank_status:
            return "PENDING"

        # Mapeamento conforme documentação oficial 9inbank
        status_mapping = {
            "PENDING": "PENDING",
            "PROCESSING": "PROCESSING", 
            "COMPLETED": "APPROVED",
            "CANCELED": "CANCELED",
            "TRANSFERRING": "PROCESSING",  # ✅ TRANSFERRING = PROCESSING
            # Status para withdrawSent
            "FAILED": "CANCELED",
            "REJECTED": "CANCELED",
            # Status legados (manter compatibilidade)
            "OK": "APPROVED"
        }
        return status_mapping.get(nineinbank_status.upper(), "UNKNOWN")
    
    def _validate_nineinbank_status(self, status: str) -> bool:
        """Validar se o status está conforme documentação 9inbank"""
        valid_statuses = {
            "PENDING", "PROCESSING", "COMPLETED", "CANCELED", "TRANSFERRING"
        }
        return status.upper() in valid_statuses
    
    async def create_pix_payment(self, pix_data: Dict[str, Any]) -> Dict[str, Any]:
        """Criar PIX payment (PIX IN) - não implementado para cashout"""
        raise NotImplementedError("PIX IN não é suportado no pix-api-proxy para 9IN Bank")
    
    async def process_pix_withdrawal(self, withdrawal_data: Dict[str, Any], request_headers: dict = None) -> Dict[str, Any]:
        """Processar PIX withdrawal (PIX OUT) - Cashout via 9IN Bank com retry logic"""

        # CRITICAL FIX: Implement retry logic for robustness
        max_retries = 3
        retry_delay = 2  # seconds

        for attempt in range(max_retries):
            try:
                logger.info("Criando PIX withdrawal com 9IN Bank", extra={
                    "amount": withdrawal_data.get("amount"),
                    "pix_key": withdrawal_data.get("pixKey", "")[:4] + "..." if withdrawal_data.get("pixKey") else "",
                    "pix_key_type": withdrawal_data.get("pixKeyType", ""),
                    "attempt": attempt + 1,
                    "max_retries": max_retries
                })

                # Call the actual processing method
                return await self._process_pix_withdrawal_attempt(withdrawal_data, request_headers)

            except Exception as e:
                error_message = str(e)
                error_type = type(e).__name__

                # Determine if this is a retryable error
                is_retryable = (
                    "timeout" in error_message.lower() or
                    "connection" in error_message.lower() or
                    "502" in error_message or
                    "503" in error_message or
                    "504" in error_message or
                    "TimeoutException" in error_type or
                    "ConnectError" in error_type
                )

                logger.warning("Erro na tentativa de PIX withdrawal", extra={
                    "attempt": attempt + 1,
                    "max_retries": max_retries,
                    "error_type": error_type,
                    "error_message": error_message,
                    "is_retryable": is_retryable,
                    "will_retry": is_retryable and attempt < max_retries - 1
                })

                # If this is the last attempt or error is not retryable, re-raise
                if attempt == max_retries - 1 or not is_retryable:
                    logger.error("Falha final no PIX withdrawal após tentativas", extra={
                        "total_attempts": attempt + 1,
                        "error_type": error_type,
                        "error_message": error_message,
                        "is_retryable": is_retryable
                    })
                    raise

                # Wait before retry with exponential backoff
                wait_time = retry_delay * (2 ** attempt)
                logger.info(f"Aguardando {wait_time}s antes da próxima tentativa", extra={
                    "attempt": attempt + 1,
                    "wait_time": wait_time,
                    "next_attempt": attempt + 2
                })
                await asyncio.sleep(wait_time)

    async def _process_pix_withdrawal_attempt(self, withdrawal_data: Dict[str, Any], request_headers: dict = None) -> Dict[str, Any]:
        """Processar uma tentativa individual de PIX withdrawal"""
        try:

            # Obter headers de autenticação
            headers = await nineinbank_auth_service.get_auth_headers()
            
            # Usar idempotencyKey como identifier (ID da transação do Pluggou)
            # NOTE: Validation is now handled by the enhanced validation layer in the API
            identifier = withdrawal_data.get("idempotencyKey")
            # idempotencyKey é obrigatório e já foi validado pela enhanced validation layer
            
            # Identifier validation is now handled by the enhanced validation layer in the API
            
            # Log do identificador usado (sempre idempotencyKey)
            logger.info("Identificador determinado para 9IN Bank", extra={
                "identifier": identifier,
                "source": "idempotencyKey",
                "idempotencyKey": withdrawal_data.get("idempotencyKey")
            })
            
            # DEBUG: Log detalhado para verificar o que está acontecendo
            logger.info("DEBUG - Verificação de identifier", extra={
                "withdrawal_data_keys": list(withdrawal_data.keys()),
                "idempotencyKey_present": "idempotencyKey" in withdrawal_data,
                "idempotencyKey_value": withdrawal_data.get("idempotencyKey"),
                "final_identifier": identifier,
                "identifier_source": "idempotencyKey"
            })
            
            # PIX key validation is now handled by the enhanced validation layer in the API
            pix_key_type = withdrawal_data.get("pixKeyType", "").upper().strip()
            
            # Formatar CPF/CNPJ para o documento do owner
            def format_document(doc_number, doc_type):
                """Formatar documento para o formato esperado pelo 9IN Bank"""
                if doc_type == "CPF":
                    # Remover pontos e traços, depois formatar
                    clean_doc = doc_number.replace(".", "").replace("-", "")
                    if len(clean_doc) == 11:
                        return f"{clean_doc[:3]}.{clean_doc[3:6]}.{clean_doc[6:9]}-{clean_doc[9:]}"
                    return doc_number
                elif doc_type == "CNPJ":
                    # Remover pontos, traços e barras, depois formatar
                    clean_doc = doc_number.replace(".", "").replace("-", "").replace("/", "")
                    if len(clean_doc) == 14:
                        return f"{clean_doc[:2]}.{clean_doc[2:5]}.{clean_doc[5:8]}/{clean_doc[8:12]}-{clean_doc[12:]}"
                    return doc_number
                return doc_number
            
            # Obter documento formatado para o owner - APENAS para CPF/CNPJ
            if pix_key_type in ["CPF", "CNPJ"]:
                owner_document = format_document(withdrawal_data["pixKey"], pix_key_type)
            else:
                owner_document = "465.418.008-71"  # Documento padrão para EMAIL/PHONE
            
            # Sanitizar nome do cliente - apenas letras e espaços
            def sanitize_name(name):
                import re
                if not name:
                    return "Cliente"
                # Remover caracteres especiais, manter apenas letras, espaços e hífen
                sanitized = re.sub(r'[^a-zA-Z\s\-]', '', name)
                # Remover espaços múltiplos
                sanitized = re.sub(r'\s+', ' ', sanitized).strip()
                # Se ficou vazio, usar nome padrão
                return sanitized if sanitized else "Cliente"
            
            customer_name = sanitize_name(withdrawal_data.get("customerName", "Cliente"))
            
            # Mapear tipos de chave PIX - 9IN Bank aceita apenas: cpf, cnpj, email, phone
            pix_type_mapping = {
                'EMAIL': 'email',
                'PHONE': 'phone', 
                'CPF': 'cpf',
                'CNPJ': 'cnpj'
            }
            
            # Determinar IP do cliente (prioridade: payload > headers > fallback)
            customer_ip_from_payload = withdrawal_data.get("customerIp")
            client_ip_from_headers = self.get_client_ip(request_headers)
            
            # Usar IP do payload se disponível e for brasileiro, senão usar IP dos headers se for brasileiro, senão usar IP brasileiro aleatório
            if customer_ip_from_payload and self._is_brazilian_ip(customer_ip_from_payload):
                client_ip = customer_ip_from_payload
                ip_source = "payload_brazilian"
            elif client_ip_from_headers and self._is_brazilian_ip(client_ip_from_headers):
                client_ip = client_ip_from_headers
                ip_source = "headers_brazilian"
            else:
                # Forçar uso de IP brasileiro válido para 9inbank
                client_ip = random.choice(self.brazilian_ips)
                ip_source = "forced_brazilian_fallback"
                logger.warning("IP não-brasileiro detectado, usando IP brasileiro válido para 9inbank", extra={
                    "original_payload_ip": customer_ip_from_payload,
                    "original_headers_ip": client_ip_from_headers,
                    "forced_brazilian_ip": client_ip
                })
            
            logger.info("IP do cliente determinado", extra={
                "customerIp_from_payload": customer_ip_from_payload,
                "client_ip_from_headers": client_ip_from_headers,
                "final_client_ip": client_ip,
                "ip_source": ip_source,
                "is_brazilian": self._is_brazilian_ip(client_ip)
            })
            
            # IP_DEBUG: Log detalhado para rastreamento completo
            logger.info("IP_DEBUG - IP determinado no pix-api-proxy", extra={
                "customerIp_from_payload": customer_ip_from_payload,
                "client_ip_from_headers": client_ip_from_headers,
                "final_client_ip": client_ip,
                "ip_source": ip_source,
                "is_brazilian": self._is_brazilian_ip(client_ip),
                "timestamp": datetime.now().isoformat(),
                "amount": withdrawal_data.get("amount"),
                "pixKey": withdrawal_data.get("pixKey", "")[:4] + "***" if withdrawal_data.get("pixKey") else None,
                "request_headers": {
                    "x-forwarded-for": request_headers.get("x-forwarded-for") if request_headers else None,
                    "x-real-ip": request_headers.get("x-real-ip") if request_headers else None,
                    "user-agent": request_headers.get("user-agent") if request_headers else None
                },
                "withdrawal_data_keys": list(withdrawal_data.keys()) if withdrawal_data else [],
                "transaction_id": withdrawal_data.get("idempotencyKey") if withdrawal_data else None
            })
            
            # Preparar payload conforme documentação 9IN Bank (formato exato da documentação)
            payload = {
                "identifier": identifier,  # ✅ Usar identifier conforme API atual da 9inbank
                "callbackUrl": "https://app.pluggou.io/api/webhooks/9inbank/transferencias",
                "amount": withdrawal_data["amount"],
                "discountFeeOfReceiver": False,  # Nós pagamos a taxa
                "pix": {
                    "type": pix_type_mapping.get(pix_key_type, "email"),
                    "key": withdrawal_data["pixKey"]
                },
                "owner": {
                    "ip": client_ip,  # IP do cliente do payload ou real dos headers
                    "name": customer_name,  # ✅ Usar nome sanitizado do cliente
                    "document": {
                        "type": "cpf" if pix_key_type == "CPF" else ("cnpj" if pix_key_type == "CNPJ" else "cpf"),
                        "number": owner_document
                    }
                }
            }
            
            # IP_DEBUG: Log do payload que será enviado para 9inbank
            logger.info("IP_DEBUG - Payload enviado para 9inbank", extra={
                "owner_ip": client_ip,
                "amount": payload.get("amount"),
                "pix_key": payload.get("pix", {}).get("key", "")[:4] + "***" if payload.get("pix", {}).get("key") else None,
                "customer_name": payload.get("owner", {}).get("name"),
                "customer_document": payload.get("owner", {}).get("document", {}).get("number", "")[:4] + "***" if payload.get("owner", {}).get("document", {}).get("number") else None,
                "identifier": payload.get("identifier"),
                "timestamp": datetime.now().isoformat(),
                "transaction_id": withdrawal_data.get("idempotencyKey") if withdrawal_data else None
            })
            
            logger.info("Payload preparado para 9IN Bank transfer", extra={
                "payload": payload,
                "endpoint": f"{self.base_url}/gateway/transfers"
            })
            
            url = f"{self.base_url}/gateway/transfers"

            # Log the complete request details for debugging
            logger.info("9IN Bank API Request Details", extra={
                "url": url,
                "method": "POST",
                "headers": {k: v for k, v in headers.items() if k not in ['x-secret-key']},  # Hide secret
                "payload_keys": list(payload.keys()),
                "identifier": identifier,
                "amount": payload.get("amount"),
                "timestamp": datetime.now().isoformat()
            })

            # CRITICAL FIX: Increase timeout and add better error handling
            timeout_config = httpx.Timeout(
                connect=10.0,  # Connection timeout
                read=60.0,     # Read timeout - increased for 9inbank processing
                write=10.0,    # Write timeout
                pool=10.0      # Pool timeout
            )

            async with httpx.AsyncClient(timeout=timeout_config) as client:
                try:
                    logger.info("9IN Bank API Request - Starting", extra={
                        "url": url,
                        "identifier": identifier,
                        "timeout_config": {
                            "connect": 10.0,
                            "read": 60.0,
                            "write": 10.0,
                            "pool": 10.0
                        },
                        "timestamp": datetime.now().isoformat()
                    })

                    response = await client.post(
                        url,
                        json=payload,
                        headers=headers
                    )

                    logger.info("9IN Bank API Request - Completed", extra={
                        "identifier": identifier,
                        "status_code": response.status_code,
                        "response_time": "success",
                        "timestamp": datetime.now().isoformat()
                    })

                except httpx.TimeoutException as timeout_error:
                    logger.error("CRITICAL: 9IN Bank API Timeout - Transaction may have been processed successfully", extra={
                        "identifier": identifier,
                        "timeout_type": type(timeout_error).__name__,
                        "timeout_config": {
                            "connect": 10.0,
                            "read": 60.0,
                            "write": 10.0,
                            "pool": 10.0
                        },
                        "critical_note": "9inbank may have processed the transaction despite timeout",
                        "timestamp": datetime.now().isoformat()
                    })

                    # CRITICAL: Don't fail immediately on timeout - 9inbank may have processed successfully
                    # Return a pending status and let webhook update the final status
                    return {
                        "success": True,
                        "transactionId": identifier,
                        "endToEndId": identifier,
                        "status": "pending",  # Conservative status - webhook will update
                        "amount": withdrawal_data["amount"],
                        "currency": "BRL",
                        "provider": "9inbank",
                        "message": "Transaction submitted but response timed out - check webhook for final status",
                        "providerData": {
                            "pluggouTransactionId": identifier,
                            "timeoutError": str(timeout_error),
                            "note": "Timeout occurred - transaction may have been processed successfully by 9inbank"
                        }
                    }

                # Enhanced response logging
                response_text = response.text
                logger.info("9IN Bank API Response Details", extra={
                    "status_code": response.status_code,
                    "response_text": response_text[:2000] if response_text else None,  # Increased limit
                    "response_headers": dict(response.headers),
                    "content_type": response.headers.get("content-type"),
                    "content_length": len(response_text) if response_text else 0,
                    "identifier": identifier,
                    "timestamp": datetime.now().isoformat()
                })
                
                if response.status_code in [200, 201]:
                    try:
                        # CRITICAL FIX: Better JSON parsing with detailed error handling
                        logger.info("9IN Bank API Response - Parsing JSON", extra={
                            "status_code": response.status_code,
                            "content_type": response.headers.get("content-type"),
                            "response_length": len(response_text) if response_text else 0,
                            "identifier": identifier
                        })

                        if not response_text or response_text.strip() == "":
                            logger.error("CRITICAL: 9IN Bank returned empty response with success status", extra={
                                "status_code": response.status_code,
                                "identifier": identifier,
                                "critical_note": "Empty response but success status - transaction likely processed"
                            })
                            # Return conservative success response
                            return {
                                "success": True,
                                "transactionId": identifier,
                                "endToEndId": identifier,
                                "status": "pending",
                                "amount": withdrawal_data["amount"],
                                "currency": "BRL",
                                "provider": "9inbank",
                                "message": "Transaction submitted successfully but response was empty",
                                "providerData": {
                                    "pluggouTransactionId": identifier,
                                    "note": "Empty response with success status - transaction likely successful"
                                }
                            }

                        response_data = response.json()

                        logger.info("9IN Bank API Response - JSON Parsed Successfully", extra={
                            "status_code": response.status_code,
                            "response_keys": list(response_data.keys()) if response_data else [],
                            "has_withdraw": "withdraw" in response_data if response_data else False,
                            "withdraw_status": response_data.get("withdraw", {}).get("status") if response_data else None,
                            "identifier": identifier
                        })

                        # CRITICAL: If 9inbank returned 200/201, the transaction was likely successful
                        # Even if some fields are missing, we should try to return a success response
                        # rather than failing the entire transaction

                        # Validar campos obrigatórios conforme documentação
                        required_fields = ["webhookToken", "withdraw", "payoutAccount"]
                        missing_fields = [field for field in required_fields if field not in response_data]

                        if missing_fields:
                            logger.warning("Campos obrigatórios ausentes na resposta 9IN Bank - mas 9inbank retornou sucesso", extra={
                                "missing_fields": missing_fields,
                                "response_data": response_data,
                                "identifier": identifier,
                                "status_code": response.status_code,
                                "critical_note": "9inbank returned success status, transaction likely processed successfully"
                            })

                            # If withdraw field is missing entirely, this is a critical error
                            if "withdraw" not in response_data:
                                logger.error("CRITICAL: withdraw field missing from 9inbank success response", extra={
                                    "response_data": response_data,
                                    "identifier": identifier,
                                    "status_code": response.status_code
                                })
                                # Still try to create a minimal success response to avoid false failures
                                return {
                                    "success": True,
                                    "transactionId": identifier,
                                    "endToEndId": response_data.get("id", identifier),
                                    "status": "pending",  # Conservative status
                                    "amount": withdrawal_data["amount"],
                                    "currency": "BRL",
                                    "provider": "9inbank",
                                    "message": "Transaction submitted successfully but response format unexpected",
                                    "providerData": {
                                        "rawResponse": response_data,
                                        "pluggouTransactionId": identifier,
                                        "note": "Partial response from 9inbank - transaction likely successful"
                                    }
                                }

                        # Validar estrutura do withdraw
                        withdraw = response_data.get("withdraw", {})
                        withdraw_required_fields = ["id", "amount", "status", "createdAt", "updatedAt"]
                        withdraw_missing = [field for field in withdraw_required_fields if field not in withdraw]

                        if withdraw_missing:
                            logger.warning("Campos obrigatórios ausentes no withdraw", extra={
                                "missing_fields": withdraw_missing,
                                "withdraw": withdraw,
                                "identifier": identifier
                            })
                        
                        logger.info("PIX withdrawal criado com sucesso", extra={
                            "withdraw_id": withdraw.get("id"),
                            "status": withdraw.get("status"),
                            "amount": withdraw.get("amount"),
                            "currency": withdraw.get("currency"),
                            "webhook_token": response_data.get("webhookToken"),
                            "receipt_url": response_data.get("receiptUrl"),
                            "payout_account_id": response_data.get("payoutAccount", {}).get("id")
                        })
                        
                        # Retornar resposta conforme documentação 9inbank
                        # IMPORTANTE: Usar pluggouTransactionId como transactionId para manter vínculo
                        
                        # DEBUG: Log do que está sendo retornado
                        logger.info("DEBUG - Retornando resposta 9IN Bank", extra={
                            "identifier_used": identifier,
                            "withdraw_id": withdraw.get("id"),
                            "transactionId_will_be": identifier,
                            "endToEndId_will_be": withdraw.get("id"),
                            "pluggouTransactionId_in_providerData": identifier
                        })
                        
                        # Converter timestamps ISO para integer se necessário
                        def convert_timestamp(timestamp):
                            if isinstance(timestamp, str):
                                try:
                                    from datetime import datetime
                                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                    return int(dt.timestamp())
                                except:
                                    from datetime import datetime
                                    return int(datetime.now().timestamp())
                            from datetime import datetime
                            return timestamp or int(datetime.now().timestamp())
                        
                        return {
                            "success": True,
                            "transactionId": identifier,  # ✅ Usar identifier (pluggouTransactionId) como transactionId
                            "endToEndId": withdraw.get("id"),  # ID da 9inbank como endToEndId
                            "status": self._map_status_to_internal(withdraw.get("status")),
                            "amount": withdraw.get("amount"),
                            "currency": withdraw.get("currency", "BRL"),
                            "feeAmount": withdraw.get("feeAmount"),
                            "createdAt": convert_timestamp(withdraw.get("createdAt")),
                            "updatedAt": convert_timestamp(withdraw.get("updatedAt")),
                            "provider": "9inbank",
                            "webhookToken": response_data.get("webhookToken"),
                            "receiptUrl": response_data.get("receiptUrl"),
                            "payoutAccount": response_data.get("payoutAccount"),
                            "providerData": {
                                **response_data,
                                "nineinbankTransactionId": withdraw.get("id"),  # ID da 9inbank para referência
                                "pluggouTransactionId": identifier  # ID do Pluggou para referência
                            }
                        }
                    except Exception as json_error:
                        logger.error("CRITICAL: Erro ao processar resposta 9IN Bank - mas status HTTP indica sucesso", extra={
                            "status_code": response.status_code,
                            "response_text": response.text,
                            "json_error": str(json_error),
                            "identifier": identifier,
                            "critical_note": "9inbank returned success HTTP status - transaction likely processed successfully despite parsing error"
                        })

                        # CRITICAL FIX: If 9inbank returned 200/201, don't fail the transaction
                        # Return a conservative success response to prevent false failures
                        return {
                            "success": True,
                            "transactionId": identifier,
                            "endToEndId": identifier,  # Use identifier as fallback
                            "status": "pending",  # Conservative status - webhook will update later
                            "amount": withdrawal_data["amount"],
                            "currency": "BRL",
                            "provider": "9inbank",
                            "message": "Transaction submitted successfully but response parsing failed",
                            "providerData": {
                                "rawResponse": response.text,
                                "pluggouTransactionId": identifier,
                                "parsingError": str(json_error),
                                "note": "9inbank returned success HTTP status - transaction likely successful"
                            }
                        }
                else:
                    # Log detalhado do erro antes de lançar exceção
                    error_text = response.text

                    # Try to parse error response to get more details
                    error_details = None
                    try:
                        error_json = response.json()
                        error_details = error_json
                        logger.error("9IN Bank API Error Response - Parsed JSON", extra={
                            "status_code": response.status_code,
                            "error_json": error_json,
                            "identifier": identifier,
                            "request_payload": payload
                        })
                    except:
                        logger.error("9IN Bank API Error Response - Raw Text", extra={
                            "status_code": response.status_code,
                            "response_text": error_text,
                            "headers": dict(response.headers),
                            "identifier": identifier,
                            "request_payload": payload
                        })

                    # Check if this might be a temporary error vs permanent failure
                    is_temporary_error = response.status_code in [500, 502, 503, 504, 408, 429]

                    error_message = f"Erro ao criar PIX withdrawal: {response.status_code} - {error_text}"
                    if is_temporary_error:
                        error_message = f"Erro temporário na API 9IN Bank: {response.status_code} - {error_text}"

                    logger.error("9IN Bank API Error Classification", extra={
                        "status_code": response.status_code,
                        "is_temporary_error": is_temporary_error,
                        "identifier": identifier,
                        "error_message": error_message
                    })

                    raise Exception(error_message)

        except Exception as e:
            # CRITICAL FIX: Enhanced error logging with specific error classification
            error_type = type(e).__name__
            error_message = str(e)

            error_context = {
                "identifier": identifier if 'identifier' in locals() else None,
                "amount": withdrawal_data.get("amount"),
                "pix_key": withdrawal_data.get("pixKey", "")[:4] + "***" if withdrawal_data.get("pixKey") else None,
                "error_type": error_type,
                "error_message": error_message,
                "timestamp": datetime.now().isoformat()
            }

            # Classify error types for better debugging
            if "timeout" in error_message.lower() or "TimeoutException" in error_type:
                logger.error("CRITICAL: Timeout error in 9IN Bank processing - transaction may have succeeded", extra={
                    **error_context,
                    "error_classification": "TIMEOUT",
                    "critical_note": "9inbank may have processed transaction despite timeout"
                })
                # For timeout errors, return pending status instead of failing
                return {
                    "success": True,
                    "transactionId": identifier if 'identifier' in locals() else withdrawal_data.get("pluggouTransactionId", "unknown"),
                    "endToEndId": identifier if 'identifier' in locals() else withdrawal_data.get("pluggouTransactionId", "unknown"),
                    "status": "pending",
                    "amount": withdrawal_data["amount"],
                    "currency": "BRL",
                    "provider": "9inbank",
                    "message": f"Transaction submitted but timeout occurred: {error_message}",
                    "providerData": {
                        "pluggouTransactionId": identifier if 'identifier' in locals() else withdrawal_data.get("pluggouTransactionId"),
                        "timeoutError": error_message,
                        "note": "Timeout occurred - check webhook for final transaction status"
                    }
                }
            elif "json" in error_message.lower() or "JSONDecodeError" in error_type:
                logger.error("CRITICAL: JSON parsing error in 9IN Bank response", extra={
                    **error_context,
                    "error_classification": "JSON_PARSING",
                    "critical_note": "Response parsing failed but transaction may have succeeded"
                })
                raise Exception(f"Erro ao processar resposta JSON da 9IN Bank: {error_message}")
            elif "connection" in error_message.lower() or "ConnectError" in error_type:
                logger.error("CRITICAL: Connection error with 9IN Bank", extra={
                    **error_context,
                    "error_classification": "CONNECTION",
                    "critical_note": "Connection failed - transaction status unknown"
                })
                raise Exception(f"Erro de conexão com 9IN Bank: {error_message}")
            else:
                logger.error("CRITICAL: Unknown error in 9IN Bank processing", extra={
                    **error_context,
                    "error_classification": "UNKNOWN",
                    "critical_note": "Unexpected error occurred"
                })

            # Re-raise with enhanced error message for better debugging
            if "Erro temporário" in error_message:
                raise Exception(f"Erro temporário ao processar transferência: {error_message}")
            else:
                raise Exception(f"Erro interno ao processar transferência ({error_type}): {error_message}")
    
    async def get_pix_status(self, transaction_id: str) -> Dict[str, Any]:
        """Consultar status do PIX withdrawal"""
        try:
            logger.info(f"Consultando status do PIX withdrawal: {transaction_id}")
            
            # Obter headers de autenticação
            headers = await nineinbank_auth_service.get_auth_headers()
            
            # Endpoint para consultar transferência
            url = f"{self.base_url}/gateway/transfers/{transaction_id}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code in [200, 201]:
                    response_data = response.json()
                    
                    logger.info("Status obtido com sucesso", extra={
                        "transaction_id": transaction_id,
                        "status": response_data.get("status")
                    })
                    
                    return {
                        "success": True,
                        "transactionId": transaction_id,
                        "status": self._map_status_to_internal(response_data.get("status")),
                        "amount": response_data.get("amount"),
                        "createdAt": None,  # 9IN Bank não retorna timestamp de criação
                        "updatedAt": None,
                        "provider": "9inbank",
                        "providerData": response_data
                    }
                else:
                    raise Exception(f"Erro ao consultar PIX withdrawal: {response.status_code} - {response.text}")
                    
        except Exception as e:
            logger.error(f"Erro ao consultar PIX withdrawal 9IN Bank: {str(e)}")
            raise

    async def query_transfer(self, transfer_id: str = None, client_identifier: str = None) -> Dict[str, Any]:
        """Consultar transferência específica usando ID ou clientIdentifier"""
        try:
            if not transfer_id and not client_identifier:
                raise ValueError("É necessário fornecer transfer_id ou client_identifier")
            
            logger.info("Consultando transferência 9IN Bank", extra={
                "transfer_id": transfer_id,
                "client_identifier": client_identifier
            })
            
            # Obter headers de autenticação
            headers = await nineinbank_auth_service.get_auth_headers()
            
            # Construir URL com parâmetros de query
            params = {}
            if transfer_id:
                params["id"] = transfer_id
            if client_identifier:
                params["clientIdentifier"] = client_identifier
            
            # Endpoint para consultar transferência
            url = f"{self.base_url}/gateway/transfers"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers=headers,
                    params=params,
                    timeout=30
                )
                
                logger.info("Resposta da consulta 9IN Bank", extra={
                    "status_code": response.status_code,
                    "response_text": response.text[:1000],
                    "params": params
                })
                
                if response.status_code in [200, 201]:
                    response_data = response.json()
                    
                    # Validar campos obrigatórios conforme documentação
                    required_fields = ["id", "clientIdentifier", "amount", "currency", "status", "withdrawSent", "createdAt", "updatedAt"]
                    missing_fields = [field for field in required_fields if field not in response_data]
                    
                    if missing_fields:
                        logger.warning("Campos obrigatórios ausentes na resposta 9IN Bank", extra={
                            "missing_fields": missing_fields,
                            "response_data": response_data
                        })
                    
                    # Validar status conforme documentação
                    status = response_data.get("status")
                    if status and not self._validate_nineinbank_status(status):
                        logger.warning("Status inválido retornado pela 9IN Bank", extra={
                            "status": status,
                            "valid_statuses": ["PENDING", "PROCESSING", "COMPLETED", "CANCELED", "TRANSFERRING"]
                        })
                    
                    logger.info("Transferência consultada com sucesso", extra={
                        "transfer_id": response_data.get("id"),
                        "client_identifier": response_data.get("clientIdentifier"),
                        "status": response_data.get("status"),
                        "amount": response_data.get("amount"),
                        "currency": response_data.get("currency"),
                        "withdraw_sent_count": len(response_data.get("withdrawSent", []))
                    })
                    
                    # Mapear status da 9IN Bank para status interno
                    internal_status = self._map_status_to_internal(status)
                    
                    # Processar withdrawSent conforme documentação
                    withdraw_sent = response_data.get("withdrawSent", [])
                    processed_withdraw_sent = []
                    
                    for withdraw in withdraw_sent:
                        # Validar campos obrigatórios do withdrawSent
                        withdraw_required_fields = ["id", "amount", "status", "createdAt", "updatedAt"]
                        withdraw_missing = [field for field in withdraw_required_fields if field not in withdraw]
                        
                        if withdraw_missing:
                            logger.warning("Campos obrigatórios ausentes no withdrawSent", extra={
                                "withdraw_id": withdraw.get("id"),
                                "missing_fields": withdraw_missing
                            })
                        
                        # Mapear status do withdrawSent (pode incluir FAILED)
                        withdraw_status = withdraw.get("status")
                        if withdraw_status and not self._validate_nineinbank_status(withdraw_status):
                            # Para withdrawSent, FAILED é válido conforme exemplo da documentação
                            if withdraw_status.upper() not in ["FAILED", "REJECTED"]:
                                logger.warning("Status inválido no withdrawSent", extra={
                                    "withdraw_id": withdraw.get("id"),
                                    "status": withdraw_status
                                })
                        
                        processed_withdraw = {
                            "id": withdraw.get("id"),
                            "amount": withdraw.get("amount"),
                            "status": self._map_status_to_internal(withdraw.get("status")),
                            "message": withdraw.get("message"),
                            "endToEndId": withdraw.get("endToEndId"),
                            "pixMetadata": withdraw.get("pixMetadata"),
                            "createdAt": withdraw.get("createdAt"),
                            "updatedAt": withdraw.get("updatedAt")
                        }
                        processed_withdraw_sent.append(processed_withdraw)
                    
                    # Retornar resposta conforme documentação 9inbank
                    return {
                        "success": True,
                        "id": response_data.get("id"),
                        "clientIdentifier": response_data.get("clientIdentifier"),
                        "amount": response_data.get("amount"),
                        "currency": response_data.get("currency", "BRL"),
                        "status": internal_status,
                        "rejectedReason": response_data.get("rejectedReason"),
                        "withdrawSent": processed_withdraw_sent,
                        "createdAt": response_data.get("createdAt"),
                        "updatedAt": response_data.get("updatedAt"),
                        "provider": "9inbank",
                        "providerData": response_data
                    }
                else:
                    error_text = response.text
                    logger.error("Erro na consulta 9IN Bank", extra={
                        "status_code": response.status_code,
                        "response_text": error_text,
                        "params": params
                    })
                    raise Exception(f"Erro ao consultar transferência: {response.status_code} - {error_text}")
                    
        except Exception as e:
            logger.error(f"Erro ao consultar transferência 9IN Bank: {str(e)}")
            raise

# Instância global
nineinbank_service = NineInBankService()
