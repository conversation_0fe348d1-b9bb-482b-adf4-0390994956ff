"""
Enhanced validation layer for 9IN Bank PIX transfers
Implements fail-fast validation with specific error messages and automatic data correction
"""

import re
import logging
from typing import Dict, Any, <PERSON><PERSON>, Optional
from decimal import Decimal
from pydantic import BaseModel, Field, validator, ValidationError
from fastapi import HTTPException

logger = logging.getLogger(__name__)

class NineInBankValidationError(Exception):
    """Custom validation error with specific error codes"""
    def __init__(self, message: str, error_code: str, field: str = None):
        self.message = message
        self.error_code = error_code
        self.field = field
        super().__init__(message)

class NineInBankValidationConfig:
    """Configuration for 9IN Bank validation rules"""
    
    # Amount limits
    MIN_AMOUNT = 10.00  # R$ 10,00 minimum
    MAX_AMOUNT = 100000.00  # R$ 100,000.00 maximum
    
    # Supported PIX key types
    SUPPORTED_PIX_TYPES = ["EMAIL", "PHONE", "CPF", "CNPJ"]
    
    # Default values for automatic correction
    DEFAULT_CUSTOMER_DOCUMENT = "***********"  # Valid CPF for testing
    DEFAULT_CUSTOMER_NAME = "Cliente Padrao"
    
    # Document validation patterns
    CPF_PATTERN = re.compile(r'^\d{11}$')
    CNPJ_PATTERN = re.compile(r'^\d{14}$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    PHONE_PATTERN = re.compile(r'^\+?55\d{10,11}$')
    
    # Name sanitization pattern (only letters, spaces, and basic accents)
    NAME_ALLOWED_PATTERN = re.compile(r'^[a-zA-ZÀ-ÿ\s]+$')

class NineInBankValidator:
    """Enhanced validator for 9IN Bank PIX transfers"""
    
    def __init__(self):
        self.config = NineInBankValidationConfig()
        
    def validate_and_sanitize(self, data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """
        Validate and sanitize input data for 9IN Bank
        
        Returns:
            Tuple[Dict[str, Any], Dict[str, str]]: (sanitized_data, corrections_applied)
        """
        sanitized_data = data.copy()
        corrections = {}
        
        try:
            # 1. Validate required fields
            self._validate_required_fields(sanitized_data)
            
            # 2. Validate and sanitize PIX key and type
            sanitized_data, pix_corrections = self._validate_pix_key(sanitized_data)
            corrections.update(pix_corrections)
            
            # 3. Validate and sanitize amount
            sanitized_data, amount_corrections = self._validate_amount(sanitized_data)
            corrections.update(amount_corrections)
            
            # 4. Validate and sanitize customer data
            sanitized_data, customer_corrections = self._validate_customer_data(sanitized_data)
            corrections.update(customer_corrections)
            
            # 5. Validate idempotency
            self._validate_idempotency(sanitized_data)
            
            # Log corrections applied
            if corrections:
                logger.info("Automatic corrections applied to 9IN Bank request", extra={
                    "corrections": corrections,
                    "original_data_keys": list(data.keys()),
                    "sanitized_data_keys": list(sanitized_data.keys())
                })
            
            return sanitized_data, corrections
            
        except NineInBankValidationError as e:
            logger.error("9IN Bank validation failed", extra={
                "error_code": e.error_code,
                "field": e.field,
                "message": e.message,
                "input_data": {k: v for k, v in data.items() if k not in ['customerDocument']}  # Don't log sensitive data
            })
            raise HTTPException(
                status_code=400,
                detail={
                    "error": e.message,
                    "errorCode": e.error_code,
                    "field": e.field,
                    "timestamp": logger.handlers[0].formatter.formatTime(logger.makeRecord("", 0, "", 0, "", (), None)) if logger.handlers else None
                }
            )
    
    def _validate_required_fields(self, data: Dict[str, Any]) -> None:
        """Validate required fields are present and not empty"""

        # Check idempotencyKey (ID da transação do Pluggou)
        idempotency_key = data.get('idempotencyKey')

        if not idempotency_key:
            raise NineInBankValidationError(
                "idempotencyKey é obrigatório - deve ser o ID da transação do Pluggou",
                "MISSING_IDEMPOTENCY_KEY",
                "idempotencyKey"
            )
        
        # Check pixKey
        if not data.get('pixKey') or not data.get('pixKey').strip():
            raise NineInBankValidationError(
                "Chave PIX é obrigatória",
                "MISSING_PIX_KEY",
                "pixKey"
            )

        # Check amount
        if not data.get('amount'):
            raise NineInBankValidationError(
                "Valor da transferência é obrigatório",
                "MISSING_AMOUNT",
                "amount"
            )
    
    def _validate_pix_key(self, data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """Validate and sanitize PIX key and type"""
        corrections = {}
        
        pix_key = data.get('pixKey', '').strip()
        pix_key_type = data.get('pixKeyType', '').upper().strip()
        
        # Reject RANDOM type immediately
        if pix_key_type == "RANDOM":
            raise NineInBankValidationError(
                "Tipo de chave PIX 'RANDOM' não é suportado pelo 9IN Bank. Use CPF, CNPJ, email ou telefone",
                "UNSUPPORTED_PIX_TYPE",
                "pixKeyType"
            )

        # Validate supported types
        if pix_key_type not in self.config.SUPPORTED_PIX_TYPES:
            raise NineInBankValidationError(
                f"Tipo de chave PIX '{pix_key_type}' não é suportado. Tipos suportados: {', '.join(self.config.SUPPORTED_PIX_TYPES)}",
                "INVALID_PIX_TYPE",
                "pixKeyType"
            )
        
        # Validate PIX key format based on type
        if pix_key_type == "EMAIL":
            if not self.config.EMAIL_PATTERN.match(pix_key):
                raise NineInBankValidationError(
                    "Formato de email inválido para chave PIX",
                    "INVALID_EMAIL_FORMAT",
                    "pixKey"
                )
        elif pix_key_type == "PHONE":
            # Clean phone number
            clean_phone = re.sub(r'[^\d+]', '', pix_key)
            if not self.config.PHONE_PATTERN.match(clean_phone):
                raise NineInBankValidationError(
                    "Formato de telefone inválido. Use formato +*************",
                    "INVALID_PHONE_FORMAT",
                    "pixKey"
                )
            if clean_phone != pix_key:
                data['pixKey'] = clean_phone
                corrections['pixKey'] = f"Telefone formatado de '{pix_key}' para '{clean_phone}'"
        elif pix_key_type == "CPF":
            # Clean CPF
            clean_cpf = re.sub(r'[^\d]', '', pix_key)
            if not self.config.CPF_PATTERN.match(clean_cpf):
                raise NineInBankValidationError(
                    "CPF deve conter exatamente 11 dígitos",
                    "INVALID_CPF_FORMAT",
                    "pixKey"
                )
            if clean_cpf != pix_key:
                data['pixKey'] = clean_cpf
                corrections['pixKey'] = f"CPF formatado de '{pix_key}' para '{clean_cpf}'"
        elif pix_key_type == "CNPJ":
            # Clean CNPJ
            clean_cnpj = re.sub(r'[^\d]', '', pix_key)
            if not self.config.CNPJ_PATTERN.match(clean_cnpj):
                raise NineInBankValidationError(
                    "CNPJ deve conter exatamente 14 dígitos",
                    "INVALID_CNPJ_FORMAT",
                    "pixKey"
                )
            if clean_cnpj != pix_key:
                data['pixKey'] = clean_cnpj
                corrections['pixKey'] = f"CNPJ formatado de '{pix_key}' para '{clean_cnpj}'"
        
        return data, corrections
    
    def _validate_amount(self, data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """Validate amount is within acceptable limits"""
        corrections = {}
        
        try:
            amount = float(data.get('amount', 0))
        except (ValueError, TypeError):
            raise NineInBankValidationError(
                "Valor deve ser um número válido",
                "INVALID_AMOUNT_FORMAT",
                "amount"
            )

        if amount < self.config.MIN_AMOUNT:
            raise NineInBankValidationError(
                f"Valor mínimo para transferência PIX é R$ {self.config.MIN_AMOUNT:.2f}",
                "AMOUNT_TOO_LOW",
                "amount"
            )

        if amount > self.config.MAX_AMOUNT:
            raise NineInBankValidationError(
                f"Valor máximo para transferência PIX é R$ {self.config.MAX_AMOUNT:.2f}",
                "AMOUNT_TOO_HIGH",
                "amount"
            )
        
        # Round to 2 decimal places
        rounded_amount = round(amount, 2)
        if rounded_amount != amount:
            data['amount'] = rounded_amount
            corrections['amount'] = f"Valor arredondado de {amount} para {rounded_amount}"
        
        return data, corrections
    
    def _validate_customer_data(self, data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """Validate and sanitize customer data"""
        corrections = {}
        
        # Handle customer document
        customer_document = data.get('customerDocument', '').strip()
        if not customer_document:
            data['customerDocument'] = self.config.DEFAULT_CUSTOMER_DOCUMENT
            corrections['customerDocument'] = f"Documento vazio substituído por padrão: {self.config.DEFAULT_CUSTOMER_DOCUMENT}"
        else:
            # Clean document
            clean_document = re.sub(r'[^\d]', '', customer_document)
            if len(clean_document) not in [11, 14]:
                # Use default if invalid
                data['customerDocument'] = self.config.DEFAULT_CUSTOMER_DOCUMENT
                corrections['customerDocument'] = f"Documento inválido '{customer_document}' substituído por padrão"
            elif clean_document != customer_document:
                data['customerDocument'] = clean_document
                corrections['customerDocument'] = f"Documento formatado de '{customer_document}' para '{clean_document}'"
        
        # Handle customer name
        customer_name = data.get('customerName', '').strip()
        if not customer_name:
            data['customerName'] = self.config.DEFAULT_CUSTOMER_NAME
            corrections['customerName'] = f"Nome vazio substituído por padrão: {self.config.DEFAULT_CUSTOMER_NAME}"
        else:
            # Sanitize name (remove special characters)
            sanitized_name = re.sub(r'[^a-zA-ZÀ-ÿ\s]', '', customer_name).strip()
            # Remove multiple spaces
            sanitized_name = re.sub(r'\s+', ' ', sanitized_name)
            
            if not sanitized_name:
                data['customerName'] = self.config.DEFAULT_CUSTOMER_NAME
                corrections['customerName'] = f"Nome inválido '{customer_name}' substituído por padrão"
            elif sanitized_name != customer_name:
                data['customerName'] = sanitized_name
                corrections['customerName'] = f"Nome sanitizado de '{customer_name}' para '{sanitized_name}'"
        
        # Validate document type
        document_type = data.get('customerDocumentType', 'cpf').lower()
        if document_type not in ['cpf', 'cnpj']:
            data['customerDocumentType'] = 'cpf'
            corrections['customerDocumentType'] = f"Tipo de documento inválido '{document_type}' alterado para 'cpf'"
        
        return data, corrections
    
    def _validate_idempotency(self, data: Dict[str, Any]) -> None:
        """Validate idempotency key format (ID da transação do Pluggou)"""
        idempotency_key = data.get('idempotencyKey')

        # idempotencyKey deve estar presente (já verificado em required fields)
        if idempotency_key:
            # Validar comprimento mínimo
            if len(idempotency_key.strip()) < 5:
                raise NineInBankValidationError(
                    "idempotencyKey deve ter pelo menos 5 caracteres",
                    "INVALID_IDEMPOTENCY_KEY",
                    "idempotencyKey"
                )

            # Validar comprimento máximo (limite do 9inbank)
            if len(idempotency_key.strip()) > 64:
                raise NineInBankValidationError(
                    "idempotencyKey deve ter no máximo 64 caracteres",
                    "IDEMPOTENCY_KEY_TOO_LONG",
                    "idempotencyKey"
                )

            # Validar caracteres permitidos (9inbank aceita apenas alfanuméricos, underscore e hífen)
            import re
            if not re.match(r'^[a-zA-Z0-9_\-]+$', idempotency_key.strip()):
                raise NineInBankValidationError(
                    "idempotencyKey deve conter apenas letras, números, underscore (_) e hífen (-)",
                    "INVALID_IDEMPOTENCY_KEY_FORMAT",
                    "idempotencyKey"
                )

# Global validator instance
nineinbank_validator = NineInBankValidator()
