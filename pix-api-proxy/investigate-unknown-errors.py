#!/usr/bin/env python3
"""
Script para investigar os erros "Erro desconhecido" que ainda estão ocorrendo
Foca nos cenários específicos que falharam no teste anterior
"""

import requests
import json
import time
import uuid
from datetime import datetime

# Configurações
PIX_API_PROXY_URL = "https://pix-api-proxy-************.us-central1.run.app"
API_KEY = "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a"
REAL_PIX_KEY = "<EMAIL>"

def detailed_test(scenario_name: str, test_data: dict, expected_issue: str):
    """Executa teste detalhado com análise completa da resposta"""
    print(f"\n🔍 INVESTIGANDO: {scenario_name}")
    print(f"   Problema esperado: {expected_issue}")
    print(f"   Dados: {json.dumps(test_data, indent=2)}")
    
    try:
        start_time = time.time()
        
        response = requests.post(
            f"{PIX_API_PROXY_URL}/api/v1/pix/transfer-9inbank",
            headers={
                "Content-Type": "application/json",
                "x-pluggou-key": API_KEY
            },
            json=test_data,
            timeout=90
        )
        
        duration = time.time() - start_time
        
        print(f"\n   📊 RESPOSTA DETALHADA:")
        print(f"      Status HTTP: {response.status_code}")
        print(f"      Tempo: {duration:.2f}s")
        print(f"      Headers: {dict(response.headers)}")
        print(f"      Tamanho: {len(response.text)} bytes")
        
        if response.text:
            try:
                response_json = response.json()
                print(f"      JSON válido: ✅")
                print(f"      Conteúdo: {json.dumps(response_json, indent=6, ensure_ascii=False)}")
                
                # Análise específica do erro
                if response.status_code == 500:
                    error_detail = response_json.get('detail', 'Sem detalhe')
                    print(f"\n   🚨 ANÁLISE DO ERRO 500:")
                    print(f"      Detalhe: {error_detail}")
                    
                    # Verificar se ainda é o erro genérico
                    if "Erro desconhecido" in error_detail:
                        print(f"      ⚠️  PROBLEMA: Ainda retorna erro genérico!")
                        print(f"      🔧 AÇÃO: Verificar logs do pix-api-proxy")
                    elif "Erro interno ao processar transferência 9IN Bank:" in error_detail:
                        # Extrair o erro específico
                        if ":" in error_detail:
                            specific_error = error_detail.split(":", 2)[-1].strip()
                            print(f"      ✅ MELHORIA: Erro específico detectado")
                            print(f"      📝 Erro específico: {specific_error}")
                        else:
                            print(f"      ⚠️  Erro interno mas sem detalhes específicos")
                    else:
                        print(f"      ✅ MELHORIA: Erro específico (não genérico)")
                
            except json.JSONDecodeError as e:
                print(f"      JSON inválido: ❌")
                print(f"      Erro JSON: {str(e)}")
                print(f"      Texto bruto: {response.text[:500]}")
        else:
            print(f"      Resposta vazia: ❌")
        
        return response.status_code, response.text
        
    except requests.exceptions.Timeout:
        print(f"   ⚠️  TIMEOUT após 90s")
        return None, "TIMEOUT"
        
    except Exception as e:
        print(f"   ❌ EXCEÇÃO: {str(e)}")
        return None, str(e)

def investigate_validation_errors():
    """Investiga erros de validação que deveriam ser 422 mas são 500"""
    print("\n" + "="*80)
    print("🔍 INVESTIGAÇÃO: ERROS DE VALIDAÇÃO")
    print("="*80)
    
    # Caso 1: idempotencyKey ausente
    test_data_1 = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 10.00,
        "customerName": "Teste Sem IdempotencyKey",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "provider": "9inbank"
        # ❌ idempotencyKey ausente
    }
    
    detailed_test(
        "Validação: idempotencyKey ausente",
        test_data_1,
        "Deveria ser erro 422 (validação), mas retorna 500"
    )
    
    # Caso 2: Tipo PIX não suportado
    test_data_2 = {
        "pixKey": "12345678901234567890123456789012345",
        "pixKeyType": "RANDOM",
        "amount": 20.00,
        "customerName": "Teste PIX Random",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "idempotencyKey": f"random-test-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"random-test-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }
    
    detailed_test(
        "Validação: Tipo PIX RANDOM não suportado",
        test_data_2,
        "Deveria ser erro específico sobre chave aleatória"
    )

def investigate_business_logic_errors():
    """Investiga erros de lógica de negócio"""
    print("\n" + "="*80)
    print("🔍 INVESTIGAÇÃO: ERROS DE LÓGICA DE NEGÓCIO")
    print("="*80)
    
    # Caso 3: Valor muito alto
    test_data_3 = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 99999.99,
        "customerName": "Teste Valor Alto",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "idempotencyKey": f"high-amount-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"high-amount-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }
    
    detailed_test(
        "Lógica: Valor muito alto (R$ 99.999,99)",
        test_data_3,
        "Deveria ser erro específico sobre limite de valor"
    )

def investigate_edge_cases():
    """Investiga casos extremos"""
    print("\n" + "="*80)
    print("🔍 INVESTIGAÇÃO: CASOS EXTREMOS")
    print("="*80)
    
    # Caso 4: Caracteres especiais no nome
    test_data_4 = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 15.00,
        "customerName": "José da Silva & Cia. Ltda. (Açúcar & Álcool) - 100%",  # Caracteres especiais
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "idempotencyKey": f"special-chars-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"special-chars-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }
    
    detailed_test(
        "Edge Case: Caracteres especiais no nome",
        test_data_4,
        "Pode causar erro de encoding ou parsing"
    )
    
    # Caso 5: Email PIX muito longo
    long_email = "a" * 50 + "@" + "b" * 50 + ".com"
    test_data_5 = {
        "pixKey": long_email,
        "pixKeyType": "EMAIL",
        "amount": 25.00,
        "customerName": "Teste Email Longo",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "idempotencyKey": f"long-email-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"long-email-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }
    
    detailed_test(
        "Edge Case: Email PIX muito longo",
        test_data_5,
        "Pode causar erro de validação no 9inbank"
    )

def test_successful_case_for_comparison():
    """Testa um caso de sucesso para comparação"""
    print("\n" + "="*80)
    print("🔍 CASO DE SUCESSO (PARA COMPARAÇÃO)")
    print("="*80)
    
    test_data_success = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 5.00,
        "customerName": "Teste Sucesso",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "idempotencyKey": f"success-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"success-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }
    
    detailed_test(
        "Caso de Sucesso: Dados válidos",
        test_data_success,
        "Deveria processar com sucesso"
    )

def main():
    """Função principal"""
    print("🔍 INVESTIGAÇÃO DETALHADA: ERROS DESCONHECIDOS 9IN BANK")
    print("="*80)
    print(f"📅 Iniciado em: {datetime.now().isoformat()}")
    print(f"🎯 Objetivo: Identificar causa dos erros 'Erro desconhecido'")
    print(f"🔗 URL: {PIX_API_PROXY_URL}")
    
    # Executar investigações
    investigate_validation_errors()
    investigate_business_logic_errors()
    investigate_edge_cases()
    test_successful_case_for_comparison()
    
    print("\n" + "="*80)
    print("📋 CONCLUSÕES DA INVESTIGAÇÃO")
    print("="*80)
    print("1. Verificar se os erros 'Erro desconhecido' ainda ocorrem")
    print("2. Identificar padrões nos erros específicos")
    print("3. Comparar com caso de sucesso")
    print("4. Propor correções adicionais se necessário")
    
    print(f"\n🕐 Concluído em: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
