#!/usr/bin/env python3
"""
Script de monitoramento contínuo para a integração 9IN Bank
Detecta problemas antes que causem discrepâncias financeiras
"""

import requests
import json
import time
import uuid
import logging
from datetime import datetime, timedelta
import os

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('9inbank-health.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configurações
PIX_API_PROXY_URL = os.getenv("PIX_API_PROXY_URL", "https://pix-api-proxy-************.us-central1.run.app")
API_KEY = os.getenv("PIX_API_PROXY_KEY", "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a")
REAL_PIX_KEY = "<EMAIL>"

# Thresholds para alertas
MAX_RESPONSE_TIME = 30  # segundos
MAX_ERROR_RATE = 0.1    # 10%
ALERT_WEBHOOK_URL = os.getenv("ALERT_WEBHOOK_URL")  # URL para enviar alertas

class NineBankHealthMonitor:
    def __init__(self):
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "timeout_requests": 0,
            "generic_errors": 0,
            "avg_response_time": 0,
            "last_check": None
        }
    
    def test_health_check(self):
        """Executa um teste de saúde básico"""
        try:
            logger.info("Executando teste de saúde 9IN Bank...")
            
            # Teste de credenciais
            cred_response = requests.get(
                f"{PIX_API_PROXY_URL}/api/v1/validate/nineinbank/config",
                headers={"x-pluggou-key": API_KEY},
                timeout=10
            )
            
            if cred_response.status_code != 200:
                logger.error(f"Falha na verificação de credenciais: {cred_response.status_code}")
                return False
            
            # Teste de transferência com valor baixo
            test_data = {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 1.00,  # Valor mínimo para teste
                "customerName": "Monitor Health Check",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "customerIp": "127.0.0.1",
                "postbackUrl": "https://app.pluggou.io/api/webhooks/nineinbank",
                "idempotencyKey": f"health-{uuid.uuid4().hex[:20]}",
                "pluggouTransactionId": f"health-{uuid.uuid4().hex[:20]}",
                "provider": "9inbank"
            }
            
            start_time = time.time()
            
            response = requests.post(
                f"{PIX_API_PROXY_URL}/api/v1/pix/transfer-9inbank",
                headers={
                    "Content-Type": "application/json",
                    "x-pluggou-key": API_KEY
                },
                json=test_data,
                timeout=60
            )
            
            response_time = time.time() - start_time
            
            # Atualizar estatísticas
            self.stats["total_requests"] += 1
            self.stats["last_check"] = datetime.now()
            
            if response.status_code == 200:
                self.stats["successful_requests"] += 1
                logger.info(f"Teste de saúde OK - Tempo: {response_time:.2f}s")
                
                # Verificar tempo de resposta
                if response_time > MAX_RESPONSE_TIME:
                    self.send_alert(f"Tempo de resposta alto: {response_time:.2f}s")
                
                return True
                
            elif response.status_code == 500:
                self.stats["failed_requests"] += 1
                
                try:
                    error_data = response.json()
                    error_detail = error_data.get('detail', 'Erro desconhecido')
                    
                    # CRÍTICO: Verificar se o erro genérico voltou
                    if "Erro interno ao processar transferência: Erro desconhecido" in error_detail:
                        self.stats["generic_errors"] += 1
                        logger.error("CRÍTICO: Erro genérico detectado novamente!")
                        self.send_alert("CRÍTICO: Erro genérico 9IN Bank voltou - discrepâncias financeiras podem ocorrer!")
                        return False
                    else:
                        logger.warning(f"Erro específico (esperado): {error_detail}")
                        return True  # Erro específico é esperado para teste de saúde
                        
                except:
                    logger.error("Erro 500 com resposta inválida")
                    return False
            else:
                self.stats["failed_requests"] += 1
                logger.error(f"Status inesperado: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            self.stats["timeout_requests"] += 1
            logger.warning("Timeout no teste de saúde")
            self.send_alert("Timeout detectado na integração 9IN Bank")
            return False
            
        except Exception as e:
            self.stats["failed_requests"] += 1
            logger.error(f"Erro no teste de saúde: {str(e)}")
            self.send_alert(f"Erro no monitoramento 9IN Bank: {str(e)}")
            return False
    
    def calculate_error_rate(self):
        """Calcula a taxa de erro atual"""
        if self.stats["total_requests"] == 0:
            return 0
        return self.stats["failed_requests"] / self.stats["total_requests"]
    
    def send_alert(self, message):
        """Envia alerta para webhook configurado"""
        if not ALERT_WEBHOOK_URL:
            logger.warning(f"ALERTA: {message} (webhook não configurado)")
            return
        
        try:
            alert_data = {
                "text": f"🚨 ALERTA 9IN Bank: {message}",
                "timestamp": datetime.now().isoformat(),
                "stats": self.stats
            }
            
            requests.post(ALERT_WEBHOOK_URL, json=alert_data, timeout=10)
            logger.info(f"Alerta enviado: {message}")
            
        except Exception as e:
            logger.error(f"Falha ao enviar alerta: {str(e)}")
    
    def generate_report(self):
        """Gera relatório de saúde"""
        error_rate = self.calculate_error_rate()
        
        report = f"""
=== RELATÓRIO DE SAÚDE 9IN BANK ===
Timestamp: {datetime.now().isoformat()}
Total de requisições: {self.stats['total_requests']}
Sucessos: {self.stats['successful_requests']}
Falhas: {self.stats['failed_requests']}
Timeouts: {self.stats['timeout_requests']}
Erros genéricos: {self.stats['generic_errors']}
Taxa de erro: {error_rate:.2%}
Última verificação: {self.stats['last_check']}

Status: {'🟢 SAUDÁVEL' if error_rate < MAX_ERROR_RATE and self.stats['generic_errors'] == 0 else '🔴 PROBLEMA'}
"""
        
        logger.info(report)
        return report
    
    def run_continuous_monitoring(self, interval_minutes=15):
        """Executa monitoramento contínuo"""
        logger.info(f"Iniciando monitoramento contínuo (intervalo: {interval_minutes} min)")
        
        while True:
            try:
                self.test_health_check()
                
                # Verificar se precisa enviar alerta por taxa de erro alta
                error_rate = self.calculate_error_rate()
                if error_rate > MAX_ERROR_RATE and self.stats["total_requests"] >= 10:
                    self.send_alert(f"Taxa de erro alta: {error_rate:.2%}")
                
                # Gerar relatório a cada 4 horas
                if self.stats["total_requests"] % 16 == 0:  # 4h / 15min = 16 checks
                    self.generate_report()
                
                time.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                logger.info("Monitoramento interrompido pelo usuário")
                break
            except Exception as e:
                logger.error(f"Erro no monitoramento: {str(e)}")
                time.sleep(60)  # Aguardar 1 minuto antes de tentar novamente

def main():
    """Função principal"""
    monitor = NineBankHealthMonitor()
    
    # Executar teste único ou monitoramento contínuo
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        monitor.run_continuous_monitoring()
    else:
        # Teste único
        success = monitor.test_health_check()
        monitor.generate_report()
        
        if not success:
            exit(1)

if __name__ == "__main__":
    main()
