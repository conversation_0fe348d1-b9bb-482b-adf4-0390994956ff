#!/usr/bin/env python3
"""
Monitor de Discrepâncias em Produção - 9IN Bank
Detecta automaticamente casos onde há discrepância entre resultado reportado e processamento real
"""

import asyncio
import aiohttp
import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import sqlite3

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('discrepancy_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TransactionRecord:
    """Registro de transação para monitoramento"""
    identifier: str
    pluggou_transaction_id: str
    status: str
    amount: float
    created_at: datetime
    api_response_status: int
    api_error_message: Optional[str]
    webhook_received: bool = False
    webhook_status: Optional[str] = None
    webhook_timestamp: Optional[datetime] = None
    discrepancy_detected: bool = False

class DiscrepancyMonitor:
    """Monitor de discrepâncias para integração 9IN Bank"""
    
    def __init__(self):
        self.db_path = "discrepancy_monitor.db"
        self.init_database()
        
        # Configurações
        self.pix_api_proxy_url = os.getenv("PIX_API_PROXY_URL", "https://pix-api-proxy-************.us-central1.run.app")
        self.api_key = os.getenv("PIX_API_PROXY_KEY", "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a")
        self.webhook_url = os.getenv("WEBHOOK_MONITOR_URL")  # URL para receber webhooks
        self.alert_webhook = os.getenv("ALERT_WEBHOOK_URL")  # URL para enviar alertas
        
        # Thresholds
        self.discrepancy_window = timedelta(minutes=30)  # Janela para detectar discrepâncias
        self.check_interval = 60  # Intervalo de verificação em segundos
        
    def init_database(self):
        """Inicializa banco de dados SQLite para tracking"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identifier TEXT UNIQUE,
                pluggou_transaction_id TEXT,
                status TEXT,
                amount REAL,
                created_at TIMESTAMP,
                api_response_status INTEGER,
                api_error_message TEXT,
                webhook_received BOOLEAN DEFAULT FALSE,
                webhook_status TEXT,
                webhook_timestamp TIMESTAMP,
                discrepancy_detected BOOLEAN DEFAULT FALSE,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS discrepancies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_identifier TEXT,
                discrepancy_type TEXT,
                description TEXT,
                detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (transaction_identifier) REFERENCES transactions (identifier)
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def record_transaction(self, transaction: TransactionRecord):
        """Registra uma transação no banco"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO transactions 
            (identifier, pluggou_transaction_id, status, amount, created_at, 
             api_response_status, api_error_message, webhook_received, 
             webhook_status, webhook_timestamp, discrepancy_detected)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            transaction.identifier,
            transaction.pluggou_transaction_id,
            transaction.status,
            transaction.amount,
            transaction.created_at,
            transaction.api_response_status,
            transaction.api_error_message,
            transaction.webhook_received,
            transaction.webhook_status,
            transaction.webhook_timestamp,
            transaction.discrepancy_detected
        ))
        
        conn.commit()
        conn.close()
        
    def record_discrepancy(self, identifier: str, discrepancy_type: str, description: str):
        """Registra uma discrepância detectada"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO discrepancies (transaction_identifier, discrepancy_type, description)
            VALUES (?, ?, ?)
        ''', (identifier, discrepancy_type, description))
        
        # Marcar transação como tendo discrepância
        cursor.execute('''
            UPDATE transactions 
            SET discrepancy_detected = TRUE, updated_at = CURRENT_TIMESTAMP
            WHERE identifier = ?
        ''', (identifier,))
        
        conn.commit()
        conn.close()
        
        logger.error(f"DISCREPÂNCIA DETECTADA: {discrepancy_type} - {identifier} - {description}")
        
    def update_webhook_received(self, identifier: str, webhook_status: str):
        """Atualiza quando webhook é recebido"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE transactions 
            SET webhook_received = TRUE, webhook_status = ?, 
                webhook_timestamp = ?, updated_at = CURRENT_TIMESTAMP
            WHERE identifier = ?
        ''', (webhook_status, datetime.now(), identifier))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Webhook recebido: {identifier} - Status: {webhook_status}")
        
    def check_for_discrepancies(self):
        """Verifica discrepâncias nas transações recentes"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Buscar transações com erro na API mas que podem ter webhook de sucesso
        cutoff_time = datetime.now() - self.discrepancy_window
        
        cursor.execute('''
            SELECT identifier, pluggou_transaction_id, api_response_status, 
                   api_error_message, webhook_received, webhook_status, created_at
            FROM transactions 
            WHERE created_at > ? 
            AND api_response_status != 200 
            AND discrepancy_detected = FALSE
        ''', (cutoff_time,))
        
        failed_transactions = cursor.fetchall()
        
        for tx in failed_transactions:
            identifier, pluggou_id, api_status, api_error, webhook_received, webhook_status, created_at = tx
            
            if webhook_received and webhook_status in ['APPROVED', 'PROCESSING', 'COMPLETED']:
                # DISCREPÂNCIA DETECTADA: API falhou mas webhook indica sucesso
                self.record_discrepancy(
                    identifier,
                    "API_FAILURE_WEBHOOK_SUCCESS",
                    f"API retornou {api_status} ({api_error}) mas webhook indica {webhook_status}"
                )
                
                # Enviar alerta
                asyncio.create_task(self.send_alert({
                    "type": "FINANCIAL_DISCREPANCY",
                    "identifier": identifier,
                    "pluggou_transaction_id": pluggou_id,
                    "api_status": api_status,
                    "api_error": api_error,
                    "webhook_status": webhook_status,
                    "created_at": created_at
                }))
        
        # Buscar transações com timeout que podem ter sido processadas
        cursor.execute('''
            SELECT identifier, pluggou_transaction_id, api_error_message, created_at
            FROM transactions 
            WHERE created_at > ? 
            AND api_error_message LIKE '%timeout%'
            AND webhook_received = FALSE
            AND discrepancy_detected = FALSE
        ''', (cutoff_time,))
        
        timeout_transactions = cursor.fetchall()
        
        for tx in timeout_transactions:
            identifier, pluggou_id, api_error, created_at = tx
            
            # Verificar se já passou tempo suficiente para webhook chegar
            if datetime.now() - datetime.fromisoformat(created_at) > timedelta(minutes=10):
                # Consultar status diretamente no 9inbank
                asyncio.create_task(self.check_9inbank_status(identifier, pluggou_id))
        
        conn.close()
        
    async def check_9inbank_status(self, identifier: str, pluggou_id: str):
        """Consulta status diretamente no 9inbank para verificar se foi processado"""
        try:
            async with aiohttp.ClientSession() as session:
                # Implementar consulta de status no 9inbank
                # Esta é uma funcionalidade que pode ser adicionada ao pix-api-proxy
                url = f"{self.pix_api_proxy_url}/api/v1/pix/status-9inbank/{identifier}"
                headers = {"x-pluggou-key": self.api_key}
                
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        status = data.get('status')
                        
                        if status in ['APPROVED', 'PROCESSING', 'COMPLETED']:
                            # DISCREPÂNCIA: Timeout na API mas 9inbank processou
                            self.record_discrepancy(
                                identifier,
                                "TIMEOUT_BUT_PROCESSED",
                                f"API timeout mas 9inbank processou com status {status}"
                            )
                            
                            await self.send_alert({
                                "type": "TIMEOUT_DISCREPANCY",
                                "identifier": identifier,
                                "pluggou_transaction_id": pluggou_id,
                                "nineinbank_status": status
                            })
                            
        except Exception as e:
            logger.error(f"Erro ao verificar status no 9inbank: {identifier} - {str(e)}")
            
    async def send_alert(self, alert_data: Dict):
        """Envia alerta de discrepância"""
        if not self.alert_webhook:
            logger.warning("ALERT_WEBHOOK_URL não configurado - alerta não enviado")
            return
            
        try:
            async with aiohttp.ClientSession() as session:
                alert_payload = {
                    "timestamp": datetime.now().isoformat(),
                    "service": "pix-api-proxy",
                    "provider": "9inbank",
                    "severity": "CRITICAL",
                    "alert_type": "FINANCIAL_DISCREPANCY",
                    "data": alert_data
                }
                
                async with session.post(
                    self.alert_webhook,
                    json=alert_payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"Alerta enviado com sucesso: {alert_data['type']}")
                    else:
                        logger.error(f"Falha ao enviar alerta: {response.status}")
                        
        except Exception as e:
            logger.error(f"Erro ao enviar alerta: {str(e)}")
            
    def get_discrepancy_stats(self, hours: int = 24) -> Dict:
        """Obtém estatísticas de discrepâncias"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Total de transações
        cursor.execute('''
            SELECT COUNT(*) FROM transactions WHERE created_at > ?
        ''', (cutoff_time,))
        total_transactions = cursor.fetchone()[0]
        
        # Transações com discrepância
        cursor.execute('''
            SELECT COUNT(*) FROM transactions 
            WHERE created_at > ? AND discrepancy_detected = TRUE
        ''', (cutoff_time,))
        discrepant_transactions = cursor.fetchone()[0]
        
        # Discrepâncias por tipo
        cursor.execute('''
            SELECT discrepancy_type, COUNT(*) 
            FROM discrepancies d
            JOIN transactions t ON d.transaction_identifier = t.identifier
            WHERE t.created_at > ?
            GROUP BY discrepancy_type
        ''', (cutoff_time,))
        discrepancies_by_type = dict(cursor.fetchall())
        
        conn.close()
        
        return {
            "period_hours": hours,
            "total_transactions": total_transactions,
            "discrepant_transactions": discrepant_transactions,
            "discrepancy_rate": discrepant_transactions / max(total_transactions, 1),
            "discrepancies_by_type": discrepancies_by_type,
            "timestamp": datetime.now().isoformat()
        }
        
    async def run_monitor(self):
        """Executa o monitor continuamente"""
        logger.info("Iniciando monitor de discrepâncias 9IN Bank")
        
        while True:
            try:
                # Verificar discrepâncias
                self.check_for_discrepancies()
                
                # Log de estatísticas a cada hora
                if int(time.time()) % 3600 == 0:  # A cada hora
                    stats = self.get_discrepancy_stats()
                    logger.info(f"Estatísticas (24h): {json.dumps(stats, indent=2)}")
                
                # Aguardar próxima verificação
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Erro no monitor: {str(e)}")
                await asyncio.sleep(60)  # Aguardar 1 minuto em caso de erro

def main():
    """Função principal"""
    monitor = DiscrepancyMonitor()
    
    # Executar monitor
    try:
        asyncio.run(monitor.run_monitor())
    except KeyboardInterrupt:
        logger.info("Monitor interrompido pelo usuário")
    except Exception as e:
        logger.error(f"Erro fatal no monitor: {str(e)}")

if __name__ == "__main__":
    main()
