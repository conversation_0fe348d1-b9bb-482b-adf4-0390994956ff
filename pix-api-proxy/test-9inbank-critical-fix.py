#!/usr/bin/env python3
"""
Script de teste COMPLETO para validar a correção crítica do erro HTTP 500 na integração 9IN Bank
Testa cenários de SUCESSO e ERRO para garantir que a correção funciona em todas as situações.
"""

import requests
import json
import time
import uuid
from datetime import datetime

# Configurações
PIX_API_PROXY_URL = "https://pix-api-proxy-************.us-central1.run.app"
API_KEY = "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a"

# Chave PIX real para testes (conforme documentação 9IN Bank)
REAL_PIX_KEY = "<EMAIL>"

def test_credentials():
    """Testa se as credenciais estão configuradas"""
    print("1️⃣ Verificando configuração das credenciais...")
    try:
        response = requests.get(
            f"{PIX_API_PROXY_URL}/api/v1/validate/nineinbank/config",
            headers={"x-pluggou-key": API_KEY},
            timeout=30
        )

        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ Credenciais configuradas:")
            print(f"      - Public Key: {config.get('public_key_prefix', 'N/A')}")
            print(f"      - Secret Key: {config.get('secret_key_prefix', 'N/A')}")
            print(f"      - API URL: {config.get('api_url', 'N/A')}")
            return True
        else:
            print(f"   ❌ Erro ao verificar credenciais: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erro na verificação: {str(e)}")
        return False

def test_transfer_scenario(scenario_name, test_data, expected_result="success"):
    """Testa um cenário específico de transferência"""
    print(f"   🧪 {scenario_name}")
    print(f"      - PIX Key: {test_data['pixKey']}")
    print(f"      - Valor: R$ {test_data['amount']}")
    print(f"      - CPF: {test_data.get('customerDocument', 'N/A')}")
    print(f"      - Resultado esperado: {expected_result}")

    try:
        start_time = time.time()

        response = requests.post(
            f"{PIX_API_PROXY_URL}/api/v1/pix/transfer-9inbank",
            headers={
                "Content-Type": "application/json",
                "x-pluggou-key": API_KEY
            },
            json=test_data,
            timeout=90
        )

        duration = time.time() - start_time
        print(f"      ⏱️  Tempo: {duration:.2f}s | Status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"      ✅ SUCESSO: {result.get('status', 'N/A')}")
            print(f"         - Transaction ID: {result.get('transactionId', 'N/A')}")
            print(f"         - End-to-End ID: {result.get('endToEndId', 'N/A')}")
            if result.get('message'):
                print(f"         - Mensagem: {result.get('message')}")
            return True, "success", result.get('status', 'unknown')

        elif response.status_code == 500:
            try:
                error_data = response.json()
                error_detail = error_data.get('detail', 'Erro desconhecido')
                print(f"      ❌ ERRO 500: {error_detail}")

                # Verificar se ainda é o erro genérico antigo
                if "Erro interno ao processar transferência 9IN Bank: Erro desconhecido" in error_detail:
                    print(f"      🚨 PROBLEMA: Erro genérico ainda ocorre!")
                    return False, "generic_error", None
                else:
                    print(f"      ℹ️  Erro específico (melhoria detectada)")
                    return True, "specific_error", None

            except:
                print(f"      ❌ Erro 500 - resposta inválida")
                return False, "invalid_response", None

        elif response.status_code == 400:
            try:
                error_data = response.json()
                error_detail = error_data.get('detail', 'Erro de validação')
                print(f"      ⚠️  ERRO 400 (esperado): {error_detail}")
                return True, "validation_error", None
            except:
                print(f"      ❌ Erro 400 - resposta inválida")
                return False, "invalid_response", None
        else:
            print(f"      ❓ Status inesperado: {response.status_code}")
            print(f"         Resposta: {response.text[:200]}")
            return False, "unexpected_status", None

    except requests.exceptions.Timeout:
        print(f"      ⚠️  TIMEOUT (pode ser esperado com a correção)")
        return True, "timeout", None

    except Exception as e:
        print(f"      ❌ Erro na requisição: {str(e)}")
        return False, "request_error", None

def test_9inbank_comprehensive():
    """Teste abrangente da correção 9IN Bank"""

    print("🚨 TESTE CRÍTICO ABRANGENTE: Correção 9IN Bank")
    print("=" * 70)
    print(f"📅 Timestamp: {datetime.now().isoformat()}")
    print(f"🔗 URL: {PIX_API_PROXY_URL}")
    print(f"🔑 PIX Key Real: {REAL_PIX_KEY}")
    print("")

    # Teste 1: Credenciais
    if not test_credentials():
        return False

    print("")

    # Teste 2: Cenários de transferência
    print("2️⃣ Testando cenários de transferência...")

    test_results = []
    
    # Cenário 1: SUCESSO - Chave PIX real com dados válidos
    scenario_1 = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 10.00,
        "customerName": "Cliente Teste Sucesso",
        "customerDocument": "***********",  # CPF válido da documentação
        "customerDocumentType": "cpf",
        "customerIp": "***************",
        "postbackUrl": "https://app.pluggou.io/api/webhooks/nineinbank",
        "idempotencyKey": f"success-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"success-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }

    success, result_type, status = test_transfer_scenario(
        "CENÁRIO SUCESSO - Chave PIX real + dados válidos",
        scenario_1,
        "success"
    )
    test_results.append(("Sucesso Real", success, result_type, status))

    print("")

    # Cenário 2: ERRO - CPF inválido
    scenario_2 = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 15.00,
        "customerName": "Cliente Teste CPF Inválido",
        "customerDocument": "***********",  # CPF inválido
        "customerDocumentType": "cpf",
        "customerIp": "***************",
        "postbackUrl": "https://app.pluggou.io/api/webhooks/nineinbank",
        "idempotencyKey": f"invalid-cpf-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"invalid-cpf-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }

    success, result_type, status = test_transfer_scenario(
        "CENÁRIO ERRO - CPF inválido",
        scenario_2,
        "error"
    )
    test_results.append(("CPF Inválido", success, result_type, status))

    print("")

    # Cenário 3: ERRO - Chave PIX inexistente
    scenario_3 = {
        "pixKey": "<EMAIL>",
        "pixKeyType": "EMAIL",
        "amount": 25.00,
        "customerName": "Cliente Teste PIX Inexistente",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "customerIp": "***************",
        "postbackUrl": "https://app.pluggou.io/api/webhooks/nineinbank",
        "idempotencyKey": f"invalid-pix-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"invalid-pix-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }

    success, result_type, status = test_transfer_scenario(
        "CENÁRIO ERRO - Chave PIX inexistente",
        scenario_3,
        "error"
    )
    test_results.append(("PIX Inexistente", success, result_type, status))

    print("")

    # Cenário 4: ERRO - Valor muito baixo
    scenario_4 = {
        "pixKey": REAL_PIX_KEY,
        "pixKeyType": "EMAIL",
        "amount": 0.50,  # Valor muito baixo
        "customerName": "Cliente Teste Valor Baixo",
        "customerDocument": "***********",
        "customerDocumentType": "cpf",
        "customerIp": "***************",
        "postbackUrl": "https://app.pluggou.io/api/webhooks/nineinbank",
        "idempotencyKey": f"low-amount-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"low-amount-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }

    success, result_type, status = test_transfer_scenario(
        "CENÁRIO ERRO - Valor muito baixo",
        scenario_4,
        "error"
    )
    test_results.append(("Valor Baixo", success, result_type, status))

    print("")

    # Cenário 5: REPRODUÇÃO DO PROBLEMA ORIGINAL - Dados exatos do problema
    scenario_5 = {
        "pixKey": "<EMAIL>",  # Chave do problema original
        "pixKeyType": "EMAIL",
        "amount": 20.00,  # Valor exato do problema
        "customerName": "Cliente",
        "customerDocument": "",  # Documento vazio como no problema
        "customerDocumentType": "cpf",
        "customerIp": "************",  # IP exato do problema
        "postbackUrl": "https://app.pluggou.io/api/webhooks/nineinbank",
        "idempotencyKey": f"original-problem-{uuid.uuid4().hex[:20]}",
        "pluggouTransactionId": f"original-problem-{uuid.uuid4().hex[:20]}",
        "provider": "9inbank"
    }

    success, result_type, status = test_transfer_scenario(
        "CENÁRIO PROBLEMA ORIGINAL - Dados exatos do erro reportado",
        scenario_5,
        "depends"
    )
    test_results.append(("Problema Original", success, result_type, status))
    
    print("")

    # Análise dos resultados
    print("3️⃣ Análise dos resultados...")
    print("=" * 50)

    success_count = 0
    total_tests = len(test_results)
    critical_issues = []

    for test_name, success, result_type, status in test_results:
        status_icon = "✅" if success else "❌"
        print(f"   {status_icon} {test_name}: {result_type}")
        if status:
            print(f"      Status final: {status}")

        if success:
            success_count += 1
        else:
            critical_issues.append(f"{test_name}: {result_type}")

    print("")
    print(f"📊 Resumo: {success_count}/{total_tests} testes bem-sucedidos")

    # Verificar se a correção foi efetiva
    correction_effective = True

    if critical_issues:
        print("🚨 PROBLEMAS CRÍTICOS DETECTADOS:")
        for issue in critical_issues:
            print(f"   - {issue}")
            if "generic_error" in issue:
                correction_effective = False

    return correction_effective, test_results

def main():
    """Função principal"""
    correction_effective, test_results = test_9inbank_comprehensive()

    print("")
    print("=" * 70)

    if correction_effective:
        print("🎉 RESULTADO: Correção da 9IN Bank VALIDADA COM SUCESSO!")
        print("")
        print("✅ A correção resolveu o problema crítico:")
        print("   - Não há mais erros genéricos 'Erro desconhecido'")
        print("   - Timeouts são tratados corretamente")
        print("   - Erros específicos são reportados adequadamente")
        print("   - Transações não falham falsamente")
        print("")
        print("📋 Próximos passos IMEDIATOS:")
        print("   1. 🚀 DEPLOY da correção em produção")
        print("   2. 📊 Monitorar logs de produção por 24h")
        print("   3. 🔔 Verificar webhooks do 9inbank")
        print("   4. 💰 Confirmar resolução das discrepâncias financeiras")
        print("   5. 📈 Implementar alertas para monitoramento contínuo")
    else:
        print("❌ RESULTADO: Problema crítico AINDA EXISTE!")
        print("")
        print("🚨 AÇÃO URGENTE NECESSÁRIA:")
        print("   - O erro genérico ainda está ocorrendo")
        print("   - Discrepâncias financeiras continuarão")
        print("")
        print("🔧 Ações imediatas:")
        print("   1. Verificar logs detalhados do pix-api-proxy")
        print("   2. Analisar resposta específica do 9inbank")
        print("   3. Implementar correções adicionais")
        print("   4. Testar novamente")

    print("")
    print("📊 DETALHES DOS TESTES:")
    for test_name, success, result_type, status in test_results:
        icon = "✅" if success else "❌"
        print(f"   {icon} {test_name}: {result_type}" + (f" ({status})" if status else ""))

    print("")
    print(f"🕐 Teste concluído em: {datetime.now().isoformat()}")
    print("=" * 70)

if __name__ == "__main__":
    main()
