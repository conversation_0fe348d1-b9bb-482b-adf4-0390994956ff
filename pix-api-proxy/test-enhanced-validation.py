#!/usr/bin/env python3
"""
Test script for enhanced validation layer in 9IN Bank integration
Tests all validation scenarios to ensure proper error handling and data sanitization
"""

import requests
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List

# Configurações
PIX_API_PROXY_URL = "https://pix-api-proxy-************.us-central1.run.app"
API_KEY = "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a"
REAL_PIX_KEY = "<EMAIL>"

class ValidationTestResult:
    def __init__(self, scenario: str, expected_status: int, expected_behavior: str):
        self.scenario = scenario
        self.expected_status = expected_status
        self.expected_behavior = expected_behavior
        self.actual_status = None
        self.actual_response = None
        self.success = False
        self.error_message = None
        
    def __str__(self):
        status_icon = "✅" if self.success else "❌"
        return f"{status_icon} {self.scenario} - Expected: {self.expected_status}, Got: {self.actual_status}"

class EnhancedValidationTester:
    def __init__(self):
        self.results: List[ValidationTestResult] = []
        
    def test_scenario(self, scenario: str, test_data: Dict[str, Any], expected_status: int, expected_behavior: str) -> ValidationTestResult:
        """Test a specific validation scenario"""
        result = ValidationTestResult(scenario, expected_status, expected_behavior)
        
        print(f"\n🧪 TESTING: {scenario}")
        print(f"   Expected: HTTP {expected_status} - {expected_behavior}")
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{PIX_API_PROXY_URL}/api/v1/pix/transfer-9inbank",
                headers={
                    "Content-Type": "application/json",
                    "x-pluggou-key": API_KEY
                },
                json=test_data,
                timeout=30
            )
            
            duration = time.time() - start_time
            result.actual_status = response.status_code
            
            try:
                result.actual_response = response.json()
            except:
                result.actual_response = {"raw_text": response.text}
            
            # Check if result matches expectation
            if result.actual_status == expected_status:
                result.success = True
                print(f"   ✅ SUCCESS: Got expected HTTP {result.actual_status}")
                if result.actual_response and isinstance(result.actual_response, dict):
                    error_detail = result.actual_response.get('detail', {})
                    if isinstance(error_detail, dict):
                        print(f"      Error Code: {error_detail.get('errorCode', 'N/A')}")
                        print(f"      Message: {error_detail.get('error', 'N/A')}")
                        print(f"      Field: {error_detail.get('field', 'N/A')}")
                    else:
                        print(f"      Detail: {error_detail}")
            else:
                result.success = False
                print(f"   ❌ FAILED: Expected HTTP {expected_status}, got {result.actual_status}")
                if result.actual_response:
                    print(f"      Response: {json.dumps(result.actual_response, indent=6, ensure_ascii=False)}")
            
            print(f"   Duration: {duration:.2f}s")
            
        except requests.exceptions.Timeout:
            result.error_message = "Request timeout"
            print(f"   ⚠️  TIMEOUT after 30s")
            
        except Exception as e:
            result.error_message = str(e)
            print(f"   ❌ EXCEPTION: {str(e)}")
        
        self.results.append(result)
        return result

    def test_missing_required_fields(self):
        """Test validation of missing required fields"""
        print("\n" + "="*80)
        print("🎯 TESTING: MISSING REQUIRED FIELDS")
        print("="*80)
        
        # Test 1: Missing idempotencyKey AND pluggouTransactionId
        self.test_scenario(
            "Missing both idempotencyKey and pluggouTransactionId",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 15.00,
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "provider": "9inbank"
                # Missing both idempotencyKey and pluggouTransactionId
            },
            400,
            "Specific error about missing idempotency"
        )
        
        # Test 2: Missing pixKey
        self.test_scenario(
            "Missing pixKey",
            {
                "pixKeyType": "EMAIL",
                "amount": 15.00,
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
                # Missing pixKey
            },
            422,  # Pydantic validation error
            "Pydantic validation error for missing field"
        )
        
        # Test 3: Missing amount
        self.test_scenario(
            "Missing amount",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
                # Missing amount
            },
            422,  # Pydantic validation error
            "Pydantic validation error for missing field"
        )

    def test_invalid_pix_types(self):
        """Test validation of invalid PIX key types"""
        print("\n" + "="*80)
        print("🎯 TESTING: INVALID PIX KEY TYPES")
        print("="*80)
        
        # Test 1: RANDOM type (not supported by 9inbank)
        self.test_scenario(
            "PIX key type RANDOM (not supported)",
            {
                "pixKey": "12345678901234567890123456789012345",
                "pixKeyType": "RANDOM",
                "amount": 15.00,
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            400,
            "Specific error about RANDOM type not supported"
        )
        
        # Test 2: Invalid type
        self.test_scenario(
            "Invalid PIX key type",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "INVALID_TYPE",
                "amount": 15.00,
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            422,  # Pydantic validation error
            "Pydantic validation error for invalid enum"
        )

    def test_amount_validation(self):
        """Test amount validation"""
        print("\n" + "="*80)
        print("🎯 TESTING: AMOUNT VALIDATION")
        print("="*80)
        
        # Test 1: Amount too low
        self.test_scenario(
            "Amount below minimum (R$ 5.00)",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 5.00,  # Below minimum of R$ 10.00
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            400,
            "Specific error about minimum amount"
        )
        
        # Test 2: Amount too high
        self.test_scenario(
            "Amount above maximum (R$ 150,000.00)",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 150000.00,  # Above maximum
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            400,
            "Specific error about maximum amount"
        )
        
        # Test 3: Zero amount
        self.test_scenario(
            "Zero amount",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 0.00,
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            422,  # Pydantic validation error (gt=0)
            "Pydantic validation error for zero amount"
        )

    def test_data_sanitization(self):
        """Test automatic data sanitization"""
        print("\n" + "="*80)
        print("🎯 TESTING: DATA SANITIZATION")
        print("="*80)
        
        # Test 1: Empty customer document (should use default)
        self.test_scenario(
            "Empty customer document (should use default)",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 15.00,
                "customerName": "Test User",
                "customerDocument": "",  # Empty - should use default
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            200,  # Should succeed with default document
            "Success with default document applied"
        )
        
        # Test 2: Customer name with special characters (should sanitize)
        self.test_scenario(
            "Customer name with special characters",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 15.00,
                "customerName": "José da Silva & Cia. (100%)",  # Special chars - should sanitize
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"test-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            200,  # Should succeed with sanitized name
            "Success with sanitized customer name"
        )

    def test_valid_scenarios(self):
        """Test scenarios that should succeed"""
        print("\n" + "="*80)
        print("🎯 TESTING: VALID SCENARIOS")
        print("="*80)
        
        # Test 1: Valid request with all fields
        self.test_scenario(
            "Valid request with all required fields",
            {
                "pixKey": REAL_PIX_KEY,
                "pixKeyType": "EMAIL",
                "amount": 15.00,
                "customerName": "Test User",
                "customerDocument": "***********",
                "customerDocumentType": "cpf",
                "idempotencyKey": f"valid-{uuid.uuid4().hex[:15]}",
                "provider": "9inbank"
            },
            200,
            "Successful processing"
        )

    def generate_report(self):
        """Generate final test report"""
        print("\n" + "="*80)
        print("📊 ENHANCED VALIDATION TEST REPORT")
        print("="*80)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - successful_tests
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total tests: {total_tests}")
        print(f"   Successful: {successful_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success rate: {(successful_tests/total_tests)*100:.1f}%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for result in self.results:
            print(f"   {result}")
            if not result.success and result.error_message:
                print(f"      Error: {result.error_message}")
        
        print(f"\n🎯 VALIDATION IMPROVEMENTS:")
        print(f"   ✅ HTTP 400 errors for business logic failures")
        print(f"   ✅ HTTP 422 errors for Pydantic validation failures")
        print(f"   ✅ Specific error messages with error codes")
        print(f"   ✅ Automatic data sanitization where possible")
        print(f"   ✅ Fail-fast validation before reaching 9inbank API")
        
        return {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "success_rate": (successful_tests/total_tests)*100,
            "results": [
                {
                    "scenario": r.scenario,
                    "expected_status": r.expected_status,
                    "actual_status": r.actual_status,
                    "success": r.success,
                    "error_message": r.error_message
                }
                for r in self.results
            ]
        }

def main():
    """Main function"""
    print("🔍 ENHANCED VALIDATION TESTING - 9IN BANK INTEGRATION")
    print("="*80)
    print(f"📅 Started at: {datetime.now().isoformat()}")
    print(f"🔗 URL: {PIX_API_PROXY_URL}")
    print(f"🎯 Objective: Test enhanced validation layer")
    
    tester = EnhancedValidationTester()
    
    # Run all test categories
    tester.test_missing_required_fields()
    tester.test_invalid_pix_types()
    tester.test_amount_validation()
    tester.test_data_sanitization()
    tester.test_valid_scenarios()
    
    # Generate report
    report = tester.generate_report()
    
    print(f"\n🕐 Completed at: {datetime.now().isoformat()}")
    print("="*80)
    
    return report

if __name__ == "__main__":
    main()
