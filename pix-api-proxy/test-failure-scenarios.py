#!/usr/bin/env python3
"""
Script para testar cenários específicos de falha na integração 9IN Bank
Reproduz situações onde há discrepância entre resultado reportado e processamento real
"""

import requests
import json
import time
import uuid
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Tuple

# Configurações
PIX_API_PROXY_URL = "https://pix-api-proxy-************.us-central1.run.app"
API_KEY = "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a"
REAL_PIX_KEY = "<EMAIL>"

class FailureScenarioTester:
    def __init__(self):
        self.results = []
        
    def log_result(self, scenario: str, success: bool, details: Dict[str, Any]):
        """Log resultado de um teste"""
        self.results.append({
            "scenario": scenario,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
        
    def test_scenario(self, scenario_name: str, test_data: Dict[str, Any], expected_behavior: str) -> Tuple[bool, Dict[str, Any]]:
        """Testa um cenário específico"""
        print(f"\n🧪 TESTANDO: {scenario_name}")
        print(f"   Comportamento esperado: {expected_behavior}")
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{PIX_API_PROXY_URL}/api/v1/pix/transfer-9inbank",
                headers={
                    "Content-Type": "application/json",
                    "x-pluggou-key": API_KEY
                },
                json=test_data,
                timeout=90
            )
            
            duration = time.time() - start_time
            
            result_details = {
                "status_code": response.status_code,
                "duration": duration,
                "response_size": len(response.text) if response.text else 0
            }
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    result_details.update({
                        "transaction_id": result.get("transactionId"),
                        "status": result.get("status"),
                        "end_to_end_id": result.get("endToEndId"),
                        "message": result.get("message"),
                        "has_provider_data": bool(result.get("providerData"))
                    })
                    print(f"   ✅ SUCESSO: Status {result.get('status')}")
                    return True, result_details
                except json.JSONDecodeError:
                    result_details["error"] = "Invalid JSON response"
                    print(f"   ❌ ERRO: Resposta JSON inválida")
                    return False, result_details
                    
            else:
                try:
                    error_data = response.json()
                    result_details.update({
                        "error_detail": error_data.get("detail"),
                        "error_code": error_data.get("errorCode")
                    })
                    print(f"   ❌ ERRO {response.status_code}: {error_data.get('detail', 'Erro desconhecido')}")
                except:
                    result_details["error"] = response.text[:200]
                    print(f"   ❌ ERRO {response.status_code}: {response.text[:100]}")
                
                return False, result_details
                
        except requests.exceptions.Timeout:
            result_details = {"error": "Request timeout", "duration": 90}
            print(f"   ⚠️  TIMEOUT após 90s")
            return False, result_details
            
        except Exception as e:
            result_details = {"error": str(e)}
            print(f"   ❌ EXCEÇÃO: {str(e)}")
            return False, result_details

    def test_data_payload_scenarios(self):
        """Testa cenários de dados/payload problemáticos"""
        print("\n" + "="*60)
        print("🎯 A. CENÁRIOS DE DADOS/PAYLOAD")
        print("="*60)
        
        # A1.1 - idempotencyKey ausente
        scenario_data = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 10.00,
            "customerName": "Teste Sem IdempotencyKey",
            "customerDocument": "***********",
            "customerDocumentType": "cpf",
            "provider": "9inbank"
            # ❌ idempotencyKey ausente
        }
        
        success, details = self.test_scenario(
            "A1.1 - idempotencyKey Ausente",
            scenario_data,
            "Erro de validação (422)"
        )
        self.log_result("A1.1_idempotency_key_missing", success, details)
        
        # A1.2 - customerDocument vazio
        scenario_data = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 15.00,
            "customerName": "Teste Documento Vazio",
            "customerDocument": "",  # ❌ Vazio
            "customerDocumentType": "cpf",
            "idempotencyKey": f"empty-doc-{uuid.uuid4().hex[:20]}",
            "pluggouTransactionId": f"empty-doc-{uuid.uuid4().hex[:20]}",
            "provider": "9inbank"
        }
        
        success, details = self.test_scenario(
            "A1.2 - customerDocument Vazio",
            scenario_data,
            "Sucesso ou erro específico (não genérico)"
        )
        self.log_result("A1.2_empty_document", success, details)
        
        # A1.3 - Tipo de chave PIX inválido
        scenario_data = {
            "pixKey": "******************************12345",
            "pixKeyType": "RANDOM",  # ❌ 9inbank não suporta
            "amount": 20.00,
            "customerName": "Teste PIX Random",
            "customerDocument": "***********",
            "customerDocumentType": "cpf",
            "idempotencyKey": f"random-pix-{uuid.uuid4().hex[:20]}",
            "pluggouTransactionId": f"random-pix-{uuid.uuid4().hex[:20]}",
            "provider": "9inbank"
        }
        
        success, details = self.test_scenario(
            "A1.3 - Tipo PIX RANDOM (não suportado)",
            scenario_data,
            "Erro específico sobre chave aleatória"
        )
        self.log_result("A1.3_random_pix_key", success, details)
        
        # A2.1 - CPF com formatação problemática
        scenario_data = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 25.00,
            "customerName": "Teste CPF Inválido",
            "customerDocument": "**********",  # ❌ 10 dígitos (inválido)
            "customerDocumentType": "cpf",
            "idempotencyKey": f"invalid-cpf-{uuid.uuid4().hex[:20]}",
            "pluggouTransactionId": f"invalid-cpf-{uuid.uuid4().hex[:20]}",
            "provider": "9inbank"
        }
        
        success, details = self.test_scenario(
            "A2.1 - CPF com 10 dígitos (formatação problemática)",
            scenario_data,
            "Erro específico ou processamento com documento padrão"
        )
        self.log_result("A2.1_invalid_cpf_format", success, details)

    def test_timing_network_scenarios(self):
        """Testa cenários de timing e rede"""
        print("\n" + "="*60)
        print("⏱️ B. CENÁRIOS DE TIMING/REDE")
        print("="*60)
        
        # B1.1 - Teste com timeout baixo (simular timeout de conexão)
        print("\n🧪 B1.1 - Simulando timeout de conexão")
        print("   (Usando timeout baixo para forçar timeout)")
        
        scenario_data = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 30.00,
            "customerName": "Teste Timeout Conexão",
            "customerDocument": "***********",
            "customerDocumentType": "cpf",
            "idempotencyKey": f"timeout-test-{uuid.uuid4().hex[:20]}",
            "pluggouTransactionId": f"timeout-test-{uuid.uuid4().hex[:20]}",
            "provider": "9inbank"
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{PIX_API_PROXY_URL}/api/v1/pix/transfer-9inbank",
                headers={
                    "Content-Type": "application/json",
                    "x-pluggou-key": API_KEY
                },
                json=scenario_data,
                timeout=5  # ❌ Timeout muito baixo para forçar timeout
            )
            duration = time.time() - start_time
            print(f"   ⚠️  Resposta inesperada em {duration:.2f}s: {response.status_code}")
            
        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            print(f"   ✅ TIMEOUT esperado após {duration:.2f}s")
            print(f"   ℹ️  Transação pode ter sido processada no 9inbank")
            
        self.log_result("B1.1_connection_timeout", False, {
            "timeout_occurred": True,
            "duration": duration,
            "note": "Timeout forçado para teste"
        })

    def test_response_parsing_scenarios(self):
        """Testa cenários de resposta e parsing"""
        print("\n" + "="*60)
        print("📄 C. CENÁRIOS DE RESPOSTA/PARSING")
        print("="*60)
        
        # C2.1 - Teste com valor muito alto (pode causar resposta diferente)
        scenario_data = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 99999.99,  # ❌ Valor muito alto
            "customerName": "Teste Valor Alto",
            "customerDocument": "***********",
            "customerDocumentType": "cpf",
            "idempotencyKey": f"high-amount-{uuid.uuid4().hex[:20]}",
            "pluggouTransactionId": f"high-amount-{uuid.uuid4().hex[:20]}",
            "provider": "9inbank"
        }
        
        success, details = self.test_scenario(
            "C2.1 - Valor muito alto (R$ 99.999,99)",
            scenario_data,
            "Erro de limite ou processamento especial"
        )
        self.log_result("C2.1_high_amount", success, details)

    def test_state_concurrency_scenarios(self):
        """Testa cenários de estado e concorrência"""
        print("\n" + "="*60)
        print("🔄 D. CENÁRIOS DE ESTADO/CONCORRÊNCIA")
        print("="*60)
        
        # D1.2 - Múltiplas requisições com mesmo idempotencyKey
        base_idempotency = f"duplicate-{uuid.uuid4().hex[:15]}"
        
        scenario_data_1 = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 35.00,
            "customerName": "Teste Duplicata 1",
            "customerDocument": "***********",
            "customerDocumentType": "cpf",
            "idempotencyKey": base_idempotency,  # ❌ Mesmo ID
            "pluggouTransactionId": f"dup1-{uuid.uuid4().hex[:15]}",
            "provider": "9inbank"
        }
        
        scenario_data_2 = {
            "pixKey": REAL_PIX_KEY,
            "pixKeyType": "EMAIL",
            "amount": 35.00,
            "customerName": "Teste Duplicata 2",
            "customerDocument": "***********",
            "customerDocumentType": "cpf",
            "idempotencyKey": base_idempotency,  # ❌ Mesmo ID
            "pluggouTransactionId": f"dup2-{uuid.uuid4().hex[:15]}",
            "provider": "9inbank"
        }
        
        print(f"\n🧪 D1.2 - Requisições simultâneas com mesmo idempotencyKey")
        print(f"   idempotencyKey: {base_idempotency}")
        
        # Primeira requisição
        success_1, details_1 = self.test_scenario(
            "D1.2a - Primeira requisição",
            scenario_data_1,
            "Sucesso"
        )
        
        # Segunda requisição (deve detectar duplicata)
        success_2, details_2 = self.test_scenario(
            "D1.2b - Segunda requisição (duplicata)",
            scenario_data_2,
            "Erro de duplicata ou sucesso idempotente"
        )
        
        self.log_result("D1.2_duplicate_idempotency", success_1 and not success_2, {
            "first_request": details_1,
            "second_request": details_2,
            "idempotency_key": base_idempotency
        })

    def generate_report(self):
        """Gera relatório final dos testes"""
        print("\n" + "="*80)
        print("📊 RELATÓRIO FINAL - CENÁRIOS DE FALHA")
        print("="*80)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r["success"])
        
        print(f"\n📈 RESUMO:")
        print(f"   Total de cenários testados: {total_tests}")
        print(f"   Cenários com comportamento esperado: {successful_tests}")
        print(f"   Cenários com comportamento inesperado: {total_tests - successful_tests}")
        
        print(f"\n📋 DETALHES POR CENÁRIO:")
        for result in self.results:
            status_icon = "✅" if result["success"] else "❌"
            print(f"   {status_icon} {result['scenario']}")
            if not result["success"] and "error_detail" in result["details"]:
                print(f"      Erro: {result['details']['error_detail']}")
        
        print(f"\n🎯 ANÁLISE:")
        print(f"   - Cenários A (Dados/Payload): Testam validações de entrada")
        print(f"   - Cenários B (Timing/Rede): Testam robustez de rede")
        print(f"   - Cenários C (Resposta/Parsing): Testam tratamento de respostas")
        print(f"   - Cenários D (Estado/Concorrência): Testam condições de corrida")
        
        return self.results

def main():
    """Função principal"""
    print("🔍 TESTE DE CENÁRIOS DE FALHA - INTEGRAÇÃO 9IN BANK")
    print("="*80)
    print(f"📅 Iniciado em: {datetime.now().isoformat()}")
    print(f"🔗 URL: {PIX_API_PROXY_URL}")
    print(f"🔑 PIX Key: {REAL_PIX_KEY}")
    
    tester = FailureScenarioTester()
    
    # Executar todos os cenários
    tester.test_data_payload_scenarios()
    tester.test_timing_network_scenarios()
    tester.test_response_parsing_scenarios()
    tester.test_state_concurrency_scenarios()
    
    # Gerar relatório
    results = tester.generate_report()
    
    print(f"\n🕐 Concluído em: {datetime.now().isoformat()}")
    print("="*80)
    
    return results

if __name__ == "__main__":
    main()
