# Docker files
Dockerfile*
.dockerignore

# Dependencies
node_modules
**/node_modules
.pnpm-store
**/pnpm-lock.yaml
!pnpm-lock.yaml

# Build outputs
.next
**/dist
**/build
**/.turbo

# Development files
.env*
!.env.example
.env.local*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Version control
.git
.gitignore
**/.git

# Documentation
README.md
**/README.md
**/*.md
!package.json
!**/package.json

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
**/__tests__
**/test
**/tests
**/*.test.*
**/*.spec.*
**/cypress
**/coverage

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Scripts and documentation that aren't needed in production
scripts/
docs/
_docs/
*.md
!package.json
!**/package.json

# Supabase files (not needed in Docker build)
supabase/

# GitHub workflows (not needed in Docker build)
.github/

# Public assets that will be copied separately
# public/

# Cloud Run configuration
cloudrun.yaml

 