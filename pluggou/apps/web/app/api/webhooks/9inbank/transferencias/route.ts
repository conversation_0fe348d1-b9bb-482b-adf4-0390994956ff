import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { TransactionStatus } from "@prisma/client";
import { createHmac } from "crypto";

/**
 * 9IN Bank webhook payload for transfers (cashout)
 */
interface NineInBankTransferWebhookPayload {
  event: "TRANSFER_CREATED" | "TRANSFER_COMPLETED" | "TRANSFER_FAILED";
  token: string;
  withdraw: {
    id: string;
    clientIdentifier: string;
    amount: number;
    receivedAmount: number;
    feeAmount: number;
    currency: string;
    status: "COMPLETED" | "CANCELED" | "PENDING" | "PROCESSING" | "TRANSFERRING";
    createdAt: string;
    updatedAt: string;
  };
  payoutAccount: {
    id: string;
    status: string;
    ownerName: string;
    ownerDocument: string;
    pix: string;
    pixType: string;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
  };
  sents: Array<{
    id: string;
    amount: number;
    endToEndId: string;
    pixMetadata?: {
      payerDocument?: string;
      payerName?: string;
      payerBankName?: string;
      payerBankAccount?: string;
      payerBankBranch?: string;
      receiverDocument?: string;
      receiverName?: string;
      receiverPixKey?: string;
      receiverBankName?: string;
      receiverBankAccount?: string;
      receiverBankBranch?: string;
    };
    createdAt: string;
  }>;
}

/**
 * Validate 9IN Bank transfer webhook signature
 */
async function validateNineInBankTransferWebhook(
  req: NextRequest,
  body: string
): Promise<boolean> {
  try {
    const signature = req.headers.get("x-9inbank-signature") || 
                     req.headers.get("x-signature") || 
                     req.headers.get("signature");

    if (!signature) {
      logger.warn("Missing 9IN Bank transfer webhook signature");
      return false;
    }

    const secretKey = process.env.NINEINBANK_SECRET_KEY;
    if (!secretKey) {
      logger.warn("Missing 9IN Bank secret key in environment variables");
      return false;
    }

    const expectedSignature = createHmac('sha256', secretKey)
      .update(body, 'utf8')
      .digest('hex');

    const isValid = signature === expectedSignature;

    if (!isValid) {
      logger.warn("Invalid 9IN Bank transfer webhook signature");
    }

    return isValid;

  } catch (error) {
    logger.error("Error validating 9IN Bank transfer webhook signature", {
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Find transaction by external ID or client identifier
 */
async function findTransaction(criteria: {
  externalId?: string;
  clientIdentifier?: string;
}): Promise<any> {
  try {
    const { externalId, clientIdentifier } = criteria;

    const transaction = await db.transaction.findFirst({
      where: {
        OR: [
          ...(externalId ? [{ externalId }] : []),
          ...(clientIdentifier ? [
            { externalId: clientIdentifier },
            { id: clientIdentifier }
          ] : [])
        ]
      },
      include: {
        organization: {
          select: { id: true, name: true }
        }
      }
    });

    return transaction;
  } catch (error) {
    logger.error("Error finding transaction", {
      error: error instanceof Error ? error.message : String(error),
      criteria
    });
    return null;
  }
}

/**
 * Map 9IN Bank transfer status to internal status
 */
function mapNineInBankTransferStatusToInternal(status: string): TransactionStatus {
  const statusMap: Record<string, TransactionStatus> = {
    'COMPLETED': TransactionStatus.APPROVED,
    'PENDING': TransactionStatus.PENDING,
    'PROCESSING': TransactionStatus.PENDING,
    'TRANSFERRING': TransactionStatus.PENDING,
    'CANCELED': TransactionStatus.CANCELED
  };

  return statusMap[status] || TransactionStatus.PENDING;
}

/**
 * Map 9IN Bank transfer event to internal status
 * This function considers both event and status for proper mapping
 */
function mapNineInBankTransferEventToInternal(event: string, status: string): TransactionStatus {
  // Validate event and status consistency
  if (event === 'TRANSFER_COMPLETED' && status !== 'COMPLETED') {
    logger.warn("Inconsistent event and status", { event, status });
  }
  
  if (event === 'TRANSFER_FAILED' && status !== 'CANCELED') {
    logger.warn("Inconsistent event and status", { event, status });
  }

 
  switch (event) {
    case 'TRANSFER_COMPLETED':
      return TransactionStatus.APPROVED;
    case 'TRANSFER_FAILED':
      return TransactionStatus.CANCELED;
    case 'TRANSFER_CREATED':
   
      return mapNineInBankTransferStatusToInternal(status);
    default:
      // Fallback to status mapping for unknown events
      return mapNineInBankTransferStatusToInternal(status);
  }
}

/**
 * Main webhook handler for 9IN Bank transfer notifications
 */
export async function POST(req: NextRequest) {
  const requestId = crypto.randomUUID();
  
  try {
    logger.info(`[${requestId}] 9IN Bank transfer webhook received`);

    const rawBody = await req.text();
    
    // Validate webhook signature (unless bypassed)
    const bypassValidation = process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true" ||
                           process.env.NINEINBANK_BYPASS_SIGNATURE_VALIDATION === "true";
    
    if (!bypassValidation) {
      const isValidSignature = await validateNineInBankTransferWebhook(req, rawBody);
      if (!isValidSignature) {
        logger.error(`[${requestId}] Invalid 9IN Bank transfer webhook signature`);
        return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
      }
    } else {
      logger.info(`[${requestId}] Bypassing 9IN Bank transfer webhook signature validation`);
    }

    // Parse and validate payload
    let payload: NineInBankTransferWebhookPayload;
    try {
      payload = JSON.parse(rawBody);
    } catch (error) {
      logger.error(`[${requestId}] Failed to parse 9IN Bank transfer webhook payload`, { error });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    const { event, withdraw } = payload;

    if (!withdraw?.id || !withdraw?.status) {
      logger.error(`[${requestId}] Missing required fields in 9IN Bank transfer webhook`, {
        withdrawId: withdraw?.id,
        status: withdraw?.status
      });
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Validate event and status consistency
    const eventStatusConsistency = {
      'TRANSFER_CREATED': ['PENDING', 'PROCESSING', 'TRANSFERRING'],
      'TRANSFER_COMPLETED': ['COMPLETED'],
      'TRANSFER_FAILED': ['CANCELED']
    };

    const validStatuses = eventStatusConsistency[event];
    if (validStatuses && !validStatuses.includes(withdraw.status)) {
      logger.warn(`[${requestId}] Event and status inconsistency detected`, {
        event,
        status: withdraw.status,
        expectedStatuses: validStatuses,
        withdrawId: withdraw.id
      });
    }

    // Extract endToEndId from sents array
    const endToEndId = payload.sents && payload.sents.length > 0 
      ? payload.sents[0].endToEndId 
      : null;

    // Log specific event handling
    switch (event) {
      case 'TRANSFER_CREATED':
        logger.info(`[${requestId}] Transfer created event received`, {
          withdrawId: withdraw.id,
          clientIdentifier: withdraw.clientIdentifier,
          status: withdraw.status,
          amount: withdraw.amount
        });
        break;
      case 'TRANSFER_COMPLETED':
        logger.info(`[${requestId}] Transfer completed event received`, {
          withdrawId: withdraw.id,
          clientIdentifier: withdraw.clientIdentifier,
          status: withdraw.status,
          amount: withdraw.amount,
          receivedAmount: withdraw.receivedAmount,
          feeAmount: withdraw.feeAmount
        });
        break;
      case 'TRANSFER_FAILED':
        logger.warn(`[${requestId}] Transfer failed event received`, {
          withdrawId: withdraw.id,
          clientIdentifier: withdraw.clientIdentifier,
          status: withdraw.status,
          amount: withdraw.amount
        });
        break;
      default:
        logger.info(`[${requestId}] Unknown transfer event received`, {
          event,
          withdrawId: withdraw.id,
          status: withdraw.status
        });
    }

    logger.info(`[${requestId}] 9IN Bank transfer webhook payload`, { 
      event,
      withdrawId: withdraw.id,
      clientIdentifier: withdraw.clientIdentifier,
      status: withdraw.status,
      amount: withdraw.amount,
      receivedAmount: withdraw.receivedAmount,
      feeAmount: withdraw.feeAmount,
      currency: withdraw.currency,
      endToEndId,
      sentsCount: payload.sents?.length || 0,
      payoutAccount: {
        id: payload.payoutAccount?.id,
        status: payload.payoutAccount?.status,
        pix: payload.payoutAccount?.pix,
        pixType: payload.payoutAccount?.pixType
      }
    });

    // Find transaction
    const transaction = await findTransaction({
      externalId: withdraw.id,
      clientIdentifier: withdraw.clientIdentifier
    });

    if (!transaction) {
      logger.warn(`[${requestId}] Transaction not found for 9IN Bank transfer webhook`, {
        withdrawId: withdraw.id,
        clientIdentifier: withdraw.clientIdentifier
      });
      return NextResponse.json({ 
        success: true, 
        message: "Transaction not found, but webhook processed" 
      });
    }

    logger.info(`[${requestId}] Found transaction for 9IN Bank transfer webhook`, {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      newStatus: withdraw.status,
      organizationId: transaction.organizationId
    });

    // CRITICAL: Check if transaction is already in a final state (CANCELED/REJECTED)
    // to prevent balance discrepancies from late webhooks
    if (transaction.status === 'CANCELED' || transaction.status === 'REJECTED') {
      logger.warn(`[${requestId}] Webhook received for already finalized transaction - ignoring to prevent balance discrepancy`, {
        transactionId: transaction.id,
        currentStatus: transaction.status,
        webhookStatus: withdraw.status,
        event,
        organizationId: transaction.organizationId,
        reason: "Transaction already processed as failed, webhook arrived late"
      });

      // Still return success to prevent webhook retries, but don't process
      return NextResponse.json({
        success: true,
        message: "Webhook ignored - transaction already finalized",
        transactionStatus: transaction.status,
        webhookStatus: withdraw.status
      });
    }

    // Update transaction status using the proper service
    // Use event-based mapping for more accurate status determination
    const { updateTransactionStatus } = await import("@repo/payments/src/transactions/service");

    const internalStatus = mapNineInBankTransferEventToInternal(event, withdraw.status);

    logger.info(`[${requestId}] Mapping 9IN Bank event and status to internal status`, {
      event,
      externalStatus: withdraw.status,
      internalStatus,
      transactionId: transaction.id,
      currentStatus: transaction.status,
      willProcess: true
    });
    
    const updatedTransaction = await updateTransactionStatus(
      transaction.id,
      internalStatus,
      undefined
    );

    // Update metadata and endToEndId
    const updateData: any = {
      metadata: {
        ...transaction.metadata,
        webhookReceived: true,
        webhookReceivedAt: new Date().toISOString(),
        webhookPayload: payload,
        externalStatus: withdraw.status
      }
    };

    // Update endToEndId if available
    if (endToEndId) {
      // Save endToEndId in the dedicated table field
      updateData.endToEndId = endToEndId;
      
      // Update allProviderIds in metadata to include the endToEndId
      updateData.metadata.allProviderIds = {
        ...(transaction.metadata as any)?.allProviderIds,
        endToEndId: endToEndId
      };

      // Update the nineinbank metadata with endToEndId
      updateData.metadata.nineinbank = {
        ...(transaction.metadata as any)?.nineinbank,
        endToEndId: endToEndId,
        allIds: {
          ...(transaction.metadata as any)?.nineinbank?.allIds,
          endToEndId: endToEndId
        }
      };
    }

    await db.transaction.update({
      where: { id: transaction.id },
      data: updateData
    });

    // Monitor balance operations for this transaction to detect any issues
    try {
      const { checkTransactionBalanceConsistency } = await import("@repo/payments/src/balance/balance-monitor");
      await checkTransactionBalanceConsistency(transaction.id);
    } catch (monitorError) {
      logger.error(`[${requestId}] Error monitoring balance operations (non-critical)`, {
        error: monitorError instanceof Error ? monitorError.message : String(monitorError),
        transactionId: transaction.id
      });
    }

    logger.info(`[${requestId}] 9IN Bank transfer webhook processed successfully`, {
      transactionId: transaction.id,
      status: withdraw.status,
      event,
      endToEndId,
      organizationId: transaction.organizationId
    });

    return NextResponse.json({
      success: true,
      message: "Transfer webhook processed successfully"
    });

  } catch (error) {
    logger.error(`[${requestId}] Error processing 9IN Bank transfer webhook`, {
      error: error instanceof Error ? error.message : String(error)
    });
    return NextResponse.json({ 
      error: "Failed to process webhook" 
    }, { status: 500 });
  }
}

/**
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({ 
    status: "ok", 
    timestamp: new Date().toISOString()
  });
}
