apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: pluggou-web
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Configurações de recursos
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/execution-environment: gen2
        
        # Configurações de escalonamento
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Configurações de timeout
        run.googleapis.com/timeout: "300s"
        
        # Configurações de concurrency
        run.googleapis.com/concurrency: "1000"
        
        # Configurações de rede
        run.googleapis.com/port: "3000"
        
    spec:
      containerConcurrency: 1000
      timeoutSeconds: 300
      containers:
      - image: gcr.io/pix-api-proxy-1758593444/pluggou-web:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        
        # Database
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-url
              key: latest
        
        # Auth
        - name: BETTER_AUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: better-auth-secret
              key: latest
        - name: BETTER_AUTH_URL
          value: "https://pluggou-web-1758593444-uc.a.run.app"
        
        # SVIX Webhooks
        - name: SVIX_TOKEN
          valueFrom:
            secretKeyRef:
              name: svix-token
              key: latest
        - name: SVIX_API_URL
          value: "https://webhooks.cloud.pluggou.io"
        - name: SVIX_APP_ID
          value: "app_2xJGtEh9B3sOptpW4thRObvtlh9"
        
        # PIX API Proxy
        - name: PIX_API_PROXY_URL
          valueFrom:
            secretKeyRef:
              name: pix-api-proxy-url
              key: latest
        - name: PIX_API_PROXY_KEY
          valueFrom:
            secretKeyRef:
              name: pix-api-proxy-key
              key: latest
        
        # Webhook Secrets
        - name: ECOMOVI_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: ecomovi-webhook-secret
              key: latest
        - name: HUBGAMES_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: hubgames-webhook-secret
              key: latest
        - name: HUBGAMES_POSTBACK_URL
          value: "https://pluggou-web-1758593444-uc.a.run.app/api/webhooks/hubgames"
        
        # Gateway Configuration
        - name: DEFAULT_GATEWAY_TRANSFER
          value: "CARTWAVE"
        - name: DEFAULT_GATEWAY_CHARGE
          value: "CARTWAVE"
        
        # Postback URL
        - name: NEXT_PUBLIC_POSTBACK_URL
          value: "https://pluggou-web-1758593444-uc.a.run.app"
        - name: NEXT_PUBLIC_SITE_URL
          value: "https://pluggou-web-1758593444-uc.a.run.app"
        
        # Storage
        - name: NEXT_PUBLIC_AVATARS_BUCKET_NAME
          value: "avatars"
        - name: NEXT_PUBLIC_DOCUMENTS_BUCKET_NAME
          value: "documents"
        
        # Feature Flags
        - name: ENABLE_NEW_TRANSACTION_SERVICES
          value: "true"
        - name: ENABLE_STRICT_DUPLICATE_PREVENTION
          value: "true"
        - name: ENABLE_ENHANCED_IDEMPOTENCY
          value: "true"
        - name: ENABLE_NEW_CONSTRAINTS
          value: "true"
        - name: ENABLE_OPTIMIZED_QUERIES
          value: "true"
        - name: ENABLE_CIRCUIT_BREAKER
          value: "true"
        - name: ENABLE_DETAILED_LOGGING
          value: "true"
        
        # Webhook Bypass (for development/testing)
        - name: WEBHOOKS_BYPASS_SIGNATURE_VALIDATION
          value: "false"
        - name: ECOMOVI_BYPASS_SIGNATURE_VALIDATION
          value: "false"
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
  traffic:
  - percent: 100
    latestRevision: true
