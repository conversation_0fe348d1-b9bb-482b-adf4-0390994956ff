{"name": "pluggou", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write", "e2e": "pnpm --filter web e2e", "turbo-ignore": "npx turbo-ignore", "debug:tools": "echo 'Scripts de debug movidos para scripts-debug.json'", "test:security": "node security-test-suite.js", "test:security:dev": "TEST_BASE_URL=http://localhost:3000 node security-test-suite.js", "test:security:prod": "TEST_BASE_URL=https://api.pluggou.com node security-test-suite.js", "analyze-balance-discrepancies": "tsx scripts/analyze-balance-discrepancies.ts", "fix-balance-discrepancies": "tsx scripts/fix-balance-discrepancies.ts", "test-balance-analysis": "tsx scripts/test-analysis.ts"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@paralleldrive/cuid2": "^2.2.2", "@repo/tsconfig": "workspace:*", "@types/node": "^22.10.2", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "node-fetch": "^2.7.0", "tsx": "^4.19.2", "turbo": "^2.3.3", "typescript": "5.7.2"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}, "dependencies": {"axios": "^1.10.0", "https-proxy-agent": "^7.0.6", "zod": "^3.24.1"}}