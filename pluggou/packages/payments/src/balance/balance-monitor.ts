import { db } from "@repo/database";
import { logger } from "@repo/logs";

/**
 * Monitor and report all balance operations for a specific transaction
 * Useful for debugging balance discrepancies
 */
export async function getTransactionBalanceOperations(transactionId: string) {
  try {
    const operations = await db.balance_history.findMany({
      where: {
        transactionId: transactionId
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      select: {
        id: true,
        status: true,
        amount: true,
        totalFee: true,
        organizationId: true,
        type: true,
        gatewayName: true,
        createdAt: true,
        updatedAt: true
      }
    });

    return {
      transaction,
      operations,
      summary: {
        totalOperations: operations.length,
        operationTypes: operations.map(op => op.operation),
        totalAmountMoved: operations.reduce((sum, op) => sum + Number(op.amount), 0),
        netBalanceChange: calculateNetBalanceChange(operations)
      }
    };
  } catch (error) {
    logger.error("Error getting transaction balance operations", {
      error: error instanceof Error ? error.message : String(error),
      transactionId
    });
    throw error;
  }
}

/**
 * Calculate the net balance change from a series of operations
 */
function calculateNetBalanceChange(operations: any[]) {
  let availableChange = 0;
  let reservedChange = 0;
  let pendingChange = 0;

  for (const op of operations) {
    const amount = Number(op.amount);
    
    switch (op.operation) {
      case 'CREDIT':
        availableChange += amount;
        break;
      case 'DEBIT':
      case 'DEBIT_ALLOW_NEGATIVE':
        availableChange -= amount;
        break;
      case 'RESERVE':
        availableChange -= amount;
        reservedChange += amount;
        break;
      case 'UNRESERVE':
        availableChange += amount;
        reservedChange -= amount;
        break;
      case 'DEBIT_RESERVED':
        reservedChange -= amount;
        break;
      case 'PENDING':
        availableChange -= amount;
        pendingChange += amount;
        break;
      case 'CONFIRM_PENDING':
        pendingChange -= amount;
        break;
      case 'CANCEL_PENDING':
        availableChange += amount;
        pendingChange -= amount;
        break;
    }
  }

  return {
    available: availableChange,
    reserved: reservedChange,
    pending: pendingChange
  };
}

/**
 * Log a comprehensive balance operation report for a transaction
 */
export async function logTransactionBalanceReport(transactionId: string, context?: string) {
  try {
    const report = await getTransactionBalanceOperations(transactionId);
    
    logger.info("Transaction Balance Operations Report", {
      context: context || "balance_monitoring",
      transactionId,
      transaction: report.transaction,
      summary: report.summary,
      operations: report.operations.map(op => ({
        id: op.id,
        operation: op.operation,
        amount: op.amount,
        description: op.description,
        createdAt: op.createdAt,
        balanceAfter: op.balanceAfterOperation
      }))
    });

    return report;
  } catch (error) {
    logger.error("Error generating transaction balance report", {
      error: error instanceof Error ? error.message : String(error),
      transactionId,
      context
    });
    throw error;
  }
}

/**
 * Check for potential balance discrepancies in a transaction
 */
export async function checkTransactionBalanceConsistency(transactionId: string) {
  try {
    const report = await getTransactionBalanceOperations(transactionId);
    const { transaction, operations, summary } = report;

    if (!transaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    const issues: string[] = [];
    const expectedTotal = transaction.amount + (transaction.totalFee || 0);

    // Check for common issues
    if (transaction.type === 'SEND') {
      const hasReserve = operations.some(op => op.operation === 'RESERVE');
      const hasDebitReserved = operations.some(op => op.operation === 'DEBIT_RESERVED');
      const hasUnreserve = operations.some(op => op.operation === 'UNRESERVE');

      if (!hasReserve) {
        issues.push("Missing RESERVE operation for SEND transaction");
      }

      if (transaction.status === 'APPROVED' && !hasDebitReserved) {
        issues.push("Missing DEBIT_RESERVED operation for approved SEND transaction");
      }

      if ((transaction.status === 'CANCELED' || transaction.status === 'REJECTED') && !hasUnreserve) {
        issues.push("Missing UNRESERVE operation for failed SEND transaction");
      }

      // Check for duplicate operations
      const reserveOps = operations.filter(op => op.operation === 'RESERVE');
      const unreserveOps = operations.filter(op => op.operation === 'UNRESERVE');
      const debitReservedOps = operations.filter(op => op.operation === 'DEBIT_RESERVED');

      if (reserveOps.length > 1) {
        issues.push(`Multiple RESERVE operations found: ${reserveOps.length}`);
      }
      if (unreserveOps.length > 1) {
        issues.push(`Multiple UNRESERVE operations found: ${unreserveOps.length}`);
      }
      if (debitReservedOps.length > 1) {
        issues.push(`Multiple DEBIT_RESERVED operations found: ${debitReservedOps.length}`);
      }

      // Check for conflicting operations
      if (hasDebitReserved && hasUnreserve) {
        issues.push("Both DEBIT_RESERVED and UNRESERVE operations found - potential double processing");
      }
    }

    const result = {
      transactionId,
      hasIssues: issues.length > 0,
      issues,
      report
    };

    if (issues.length > 0) {
      logger.warn("Balance consistency issues detected", {
        transactionId,
        issues,
        transactionStatus: transaction.status,
        operationCount: operations.length
      });
    } else {
      logger.info("Balance operations appear consistent", {
        transactionId,
        transactionStatus: transaction.status,
        operationCount: operations.length
      });
    }

    return result;
  } catch (error) {
    logger.error("Error checking transaction balance consistency", {
      error: error instanceof Error ? error.message : String(error),
      transactionId
    });
    throw error;
  }
}
