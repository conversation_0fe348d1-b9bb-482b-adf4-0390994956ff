# Balance Discrepancy Analysis & Correction Scripts

This directory contains scripts to identify and fix the critical balance discrepancy pattern in PIX transfers that was causing financial inconsistencies.

## Problem Description

The critical pattern we're detecting:
1. **SEND transaction** (PIX transfer) via 9inbank
2. **Transaction succeeds** on 9inbank's side
3. **pix-api-proxy reports false error** to pluggou
4. **Transaction gets CANCELED** and balance is UNRESERVED
5. **Late success webhook** arrives from 9inbank
6. **DEBIT_RESERVED operation** executes on already-canceled transaction
7. **Result**: Organization loses money (RESERVE + UNRESERVE + DEBIT_RESERVED = net debit)

## Scripts Overview

### 1. `analyze-balance-discrepancies.ts`
**Purpose**: Identifies transactions with the critical discrepancy pattern

**What it detects**:
- SEND transactions with CANCELED/REJECTED status
- Exactly 3 balance operations: RESERVE + UNRESERVE + DEBIT_RESERVED
- 9inbank provider transactions (via pix-api-proxy)
- Transactions from the last 24 hours

**Output**:
- Summary of affected transactions and organizations
- Total financial impact
- Detailed breakdown of balance operations
- Recommended corrective actions

### 2. `fix-balance-discrepancies.ts`
**Purpose**: Executes corrective CREDIT operations for affected organizations

**What it does**:
- Analyzes current discrepancies
- Creates corrective CREDIT operations
- Updates transaction metadata as reconciled
- Generates correction report
- Supports dry-run mode for safety

## Usage

### Step 1: Analyze Current Discrepancies

```bash
# Run analysis to identify affected transactions
npm run analyze-balance-discrepancies
```

This will:
- Scan the last 24 hours for problematic transactions
- Generate a detailed report
- Save results to `balance-discrepancy-report-YYYY-MM-DD.json`
- Show summary in console

### Step 2: Review the Analysis Report

The script will output:
```
📊 ANALYSIS SUMMARY
------------------------------
Time Range: 2025-10-27T19:00:00.000Z to 2025-10-28T19:00:00.000Z
Affected Transactions: 3
Affected Organizations: 2
Total Financial Impact: R$ 63.00

🚨 AFFECTED TRANSACTIONS
------------------------------
1. Transaction ID: cmh9ir22k000fib04a2pmkkw0
   Organization: TeZhbWGR7Rb9ZkVZ2WgIt
   Status: CANCELED
   Amount: R$ 20.00 + Fee: R$ 1.00
   Discrepancy: R$ 21.00
   Gateway: 9inbank
   Created: 2025-10-27T19:16:40.991Z
   Operations: RESERVE → UNRESERVE → DEBIT_RESERVED
```

### Step 3: Execute Corrections (Dry Run First)

```bash
# Dry run to see what would be corrected (RECOMMENDED FIRST)
npm run fix-balance-discrepancies

# Actually execute the corrections
npm run fix-balance-discrepancies -- --execute

# Execute and verify corrections
npm run fix-balance-discrepancies -- --execute --verify
```

### Step 4: Verify Corrections

```bash
# Re-run analysis to confirm no remaining discrepancies
npm run analyze-balance-discrepancies
```

## Example Transaction Analysis

For the reference transaction `cmh9ir22k000fib04a2pmkkw0`:

**Balance Operations Detected**:
1. `RESERVE` - R$ 21.00 (when transfer initiated)
2. `UNRESERVE` - R$ 21.00 (when proxy reported false error)  
3. `DEBIT_RESERVED` - R$ 21.00 (when late webhook processed)

**Net Effect**: Organization lost R$ 21.00 (should have been R$ 0.00)

**Correction**: Credit R$ 21.00 back to organization `TeZhbWGR7Rb9ZkVZ2WgIt`

## Safety Features

### Dry Run Mode
- Default mode shows what would be corrected without making changes
- Use `--execute` flag only after reviewing dry run results

### Idempotency
- Scripts check for existing corrections to prevent double-crediting
- Transaction metadata tracks reconciliation status

### Verification
- `--verify` flag re-runs analysis after corrections
- Confirms no remaining discrepancies

### Detailed Logging
- All operations logged with full context
- Reports saved to timestamped JSON files
- Error handling with rollback capabilities

## Files Generated

- `balance-discrepancy-report-YYYY-MM-DD.json` - Analysis results
- `correction-report-YYYY-MM-DD.json` - Correction execution results

## Monitoring Integration

The scripts integrate with the balance monitoring system we implemented:
- Uses the same detection logic as the webhook monitoring
- Leverages the balance consistency checker
- Provides actionable data for financial reconciliation

## Emergency Usage

If you need to quickly check for and fix discrepancies:

```bash
# Quick analysis and correction (with verification)
npm run analyze-balance-discrepancies && \
npm run fix-balance-discrepancies -- --execute --verify
```

## Support

For issues or questions about these scripts:
1. Check the generated JSON reports for detailed information
2. Review the console output for specific error messages
3. Verify database connectivity and permissions
4. Ensure all dependencies are installed (`tsx`, database packages)

## Related Fixes

These scripts work in conjunction with the preventive fixes we implemented:
1. Enhanced error handling in pix-api-proxy
2. Webhook validation for canceled transactions  
3. Idempotency checks in balance operations
4. Real-time balance monitoring

The goal is to both fix existing discrepancies and prevent future ones.
