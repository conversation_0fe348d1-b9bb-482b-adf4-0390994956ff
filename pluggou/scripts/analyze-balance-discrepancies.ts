#!/usr/bin/env tsx

/**
 * Comprehensive Analysis Script for PIX Transfer Balance Discrepancies
 * 
 * Identifies transactions with the critical pattern:
 * 1. SEND transaction (PIX transfer)
 * 2. Status: CANCELED or REJECTED  
 * 3. Exactly 3 balance operations: RESERVE + UNRESERVE + DEBIT_RESERVED
 * 4. Provider: 9inbank (via pix-api-proxy)
 * 
 * This pattern indicates: Success on 9inbank → False error from proxy → 
 * Transaction canceled → Late success webhook → Balance discrepancy
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";

interface BalanceOperation {
  id: string;
  operation: string;
  amount: number;
  description: string;
  createdAt: Date;
  balanceAfterOperation: any;
}

interface TransactionAnalysis {
  transactionId: string;
  organizationId: string;
  status: string;
  amount: number;
  totalFee: number;
  gatewayName: string;
  createdAt: Date;
  updatedAt: Date;
  operations: BalanceOperation[];
  hasDiscrepancy: boolean;
  discrepancyAmount: number;
  operationPattern: string[];
}

interface OrganizationImpact {
  organizationId: string;
  affectedTransactions: number;
  totalDiscrepancy: number;
  transactionIds: string[];
}

interface AnalysisReport {
  summary: {
    totalAffectedTransactions: number;
    totalAffectedOrganizations: number;
    totalFinancialImpact: number;
    analysisTimeRange: {
      from: Date;
      to: Date;
    };
  };
  affectedTransactions: TransactionAnalysis[];
  organizationImpacts: OrganizationImpact[];
  recommendedActions: string[];
}

async function analyzeBalanceDiscrepancies(): Promise<AnalysisReport> {
  const startTime = new Date();
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  
  logger.info("Starting balance discrepancy analysis", {
    timeRange: { from: twentyFourHoursAgo, to: startTime },
    targetPattern: "RESERVE + UNRESERVE + DEBIT_RESERVED"
  });

  // Step 1: Find all SEND transactions from last 24 hours that are CANCELED or REJECTED
  const suspiciousTransactions = await db.transaction.findMany({
    where: {
      type: "SEND",
      status: {
        in: ["CANCELED", "REJECTED"]
      },
      createdAt: {
        gte: twentyFourHoursAgo
      },
      // Focus on 9inbank transactions (they use pix-api-proxy)
      OR: [
        { gatewayName: { contains: "9inbank" } },
        { gatewayName: { contains: "NINEINBANK" } },
        { metadata: { path: ["provider"], equals: "9inbank" } },
        { metadata: { path: ["gatewayType"], equals: "9inbank" } }
      ]
    },
    select: {
      id: true,
      organizationId: true,
      status: true,
      amount: true,
      totalFee: true,
      gatewayName: true,
      createdAt: true,
      updatedAt: true,
      metadata: true
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  logger.info(`Found ${suspiciousTransactions.length} suspicious transactions to analyze`);

  const affectedTransactions: TransactionAnalysis[] = [];
  const organizationImpacts = new Map<string, OrganizationImpact>();

  // Step 2: Analyze balance operations for each transaction
  for (const transaction of suspiciousTransactions) {
    const operations = await db.balance_history.findMany({
      where: {
        transactionId: transaction.id
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Check if this transaction has the critical discrepancy pattern
    const operationTypes = operations.map(op => op.operation);
    const hasReserve = operationTypes.includes('RESERVE');
    const hasUnreserve = operationTypes.includes('UNRESERVE');
    const hasDebitReserved = operationTypes.includes('DEBIT_RESERVED');
    
    // Critical pattern: All three operations present
    const hasCriticalPattern = hasReserve && hasUnreserve && hasDebitReserved;
    
    if (hasCriticalPattern) {
      // Calculate discrepancy amount
      const reserveOp = operations.find(op => op.operation === 'RESERVE');
      const unreserveOp = operations.find(op => op.operation === 'UNRESERVE');
      const debitReservedOp = operations.find(op => op.operation === 'DEBIT_RESERVED');
      
      // The discrepancy is the DEBIT_RESERVED amount (money incorrectly taken)
      const discrepancyAmount = debitReservedOp ? Number(debitReservedOp.amount) : 0;

      const analysis: TransactionAnalysis = {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        status: transaction.status,
        amount: transaction.amount,
        totalFee: transaction.totalFee || 0,
        gatewayName: transaction.gatewayName || 'unknown',
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
        operations: operations.map(op => ({
          id: op.id,
          operation: op.operation,
          amount: Number(op.amount),
          description: op.description,
          createdAt: op.createdAt,
          balanceAfterOperation: op.balanceAfterOperation
        })),
        hasDiscrepancy: true,
        discrepancyAmount,
        operationPattern: operationTypes
      };

      affectedTransactions.push(analysis);

      // Track organization impact
      const orgImpact = organizationImpacts.get(transaction.organizationId) || {
        organizationId: transaction.organizationId,
        affectedTransactions: 0,
        totalDiscrepancy: 0,
        transactionIds: []
      };

      orgImpact.affectedTransactions += 1;
      orgImpact.totalDiscrepancy += discrepancyAmount;
      orgImpact.transactionIds.push(transaction.id);
      
      organizationImpacts.set(transaction.organizationId, orgImpact);

      logger.warn("Critical balance discrepancy detected", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        discrepancyAmount,
        operationPattern: operationTypes,
        operationCount: operations.length
      });
    }
  }

  // Step 3: Generate comprehensive report
  const totalFinancialImpact = affectedTransactions.reduce(
    (sum, t) => sum + t.discrepancyAmount, 0
  );

  const report: AnalysisReport = {
    summary: {
      totalAffectedTransactions: affectedTransactions.length,
      totalAffectedOrganizations: organizationImpacts.size,
      totalFinancialImpact,
      analysisTimeRange: {
        from: twentyFourHoursAgo,
        to: startTime
      }
    },
    affectedTransactions,
    organizationImpacts: Array.from(organizationImpacts.values()),
    recommendedActions: generateRecommendedActions(affectedTransactions, organizationImpacts)
  };

  return report;
}

function generateRecommendedActions(
  transactions: TransactionAnalysis[], 
  orgImpacts: Map<string, OrganizationImpact>
): string[] {
  const actions: string[] = [];

  if (transactions.length === 0) {
    actions.push("✅ No balance discrepancies detected in the last 24 hours");
    actions.push("Continue monitoring for the pattern: RESERVE + UNRESERVE + DEBIT_RESERVED");
    return actions;
  }

  actions.push("🚨 CRITICAL: Balance discrepancies detected requiring immediate action");
  actions.push("");
  actions.push("IMMEDIATE CORRECTIVE ACTIONS:");
  
  // For each affected organization
  orgImpacts.forEach((impact) => {
    actions.push(`📋 Organization ${impact.organizationId}:`);
    actions.push(`   • Credit back: R$ ${impact.totalDiscrepancy.toFixed(2)}`);
    actions.push(`   • Affected transactions: ${impact.affectedTransactions}`);
    actions.push(`   • Transaction IDs: ${impact.transactionIds.join(', ')}`);
    actions.push("");
  });

  actions.push("TECHNICAL ACTIONS:");
  actions.push("1. Deploy the fixes we implemented to prevent future occurrences");
  actions.push("2. Monitor webhook processing for 9inbank transactions");
  actions.push("3. Verify pix-api-proxy error handling improvements");
  actions.push("4. Set up alerts for the RESERVE+UNRESERVE+DEBIT_RESERVED pattern");
  
  actions.push("");
  actions.push("FINANCIAL RECONCILIATION:");
  actions.push("1. Execute corrective CREDIT operations for each affected organization");
  actions.push("2. Update transaction metadata to mark as reconciled");
  actions.push("3. Notify affected organizations of the correction");

  return actions;
}

async function main() {
  try {
    console.log("🔍 PIX Transfer Balance Discrepancy Analysis");
    console.log("=" .repeat(60));
    console.log();

    const report = await analyzeBalanceDiscrepancies();

    // Display summary
    console.log("📊 ANALYSIS SUMMARY");
    console.log("-".repeat(30));
    console.log(`Time Range: ${report.summary.analysisTimeRange.from.toISOString()} to ${report.summary.analysisTimeRange.to.toISOString()}`);
    console.log(`Affected Transactions: ${report.summary.totalAffectedTransactions}`);
    console.log(`Affected Organizations: ${report.summary.totalAffectedOrganizations}`);
    console.log(`Total Financial Impact: R$ ${report.summary.totalFinancialImpact.toFixed(2)}`);
    console.log();

    // Display affected transactions
    if (report.affectedTransactions.length > 0) {
      console.log("🚨 AFFECTED TRANSACTIONS");
      console.log("-".repeat(30));
      
      report.affectedTransactions.forEach((transaction, index) => {
        console.log(`${index + 1}. Transaction ID: ${transaction.transactionId}`);
        console.log(`   Organization: ${transaction.organizationId}`);
        console.log(`   Status: ${transaction.status}`);
        console.log(`   Amount: R$ ${transaction.amount.toFixed(2)} + Fee: R$ ${transaction.totalFee.toFixed(2)}`);
        console.log(`   Discrepancy: R$ ${transaction.discrepancyAmount.toFixed(2)}`);
        console.log(`   Gateway: ${transaction.gatewayName}`);
        console.log(`   Created: ${transaction.createdAt.toISOString()}`);
        console.log(`   Operations: ${transaction.operationPattern.join(' → ')}`);
        console.log();
      });

      // Display organization impacts
      console.log("🏢 ORGANIZATION IMPACTS");
      console.log("-".repeat(30));
      
      report.organizationImpacts.forEach((impact, index) => {
        console.log(`${index + 1}. Organization: ${impact.organizationId}`);
        console.log(`   Total Discrepancy: R$ ${impact.totalDiscrepancy.toFixed(2)}`);
        console.log(`   Affected Transactions: ${impact.affectedTransactions}`);
        console.log(`   Transaction IDs: ${impact.transactionIds.join(', ')}`);
        console.log();
      });
    }

    // Display recommended actions
    console.log("💡 RECOMMENDED ACTIONS");
    console.log("-".repeat(30));
    report.recommendedActions.forEach(action => {
      console.log(action);
    });

    // Save detailed report to file
    const reportFile = `balance-discrepancy-report-${new Date().toISOString().split('T')[0]}.json`;
    await require('fs').promises.writeFile(reportFile, JSON.stringify(report, null, 2));
    console.log();
    console.log(`📄 Detailed report saved to: ${reportFile}`);

  } catch (error) {
    logger.error("Error during balance discrepancy analysis", {
      error: error instanceof Error ? error.message : String(error)
    });
    console.error("❌ Analysis failed:", error);
    process.exit(1);
  }
}

// Run the analysis
if (require.main === module) {
  main();
}

export { analyzeBalanceDiscrepancies, type AnalysisReport, type TransactionAnalysis };
