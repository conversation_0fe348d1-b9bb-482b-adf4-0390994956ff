#!/usr/bin/env tsx

/**
 * Balance Discrepancy Correction Script
 * 
 * Executes corrective actions for organizations affected by the
 * critical balance discrepancy pattern (RESERVE + UNRESERVE + DEBIT_RESERVED)
 * 
 * This script:
 * 1. Identifies affected organizations and amounts
 * 2. Creates corrective CREDIT operations
 * 3. Updates transaction metadata to mark as reconciled
 * 4. Generates reconciliation report
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { createId } from "@paralleldrive/cuid2";
import { analyzeBalanceDiscrepancies, type AnalysisReport } from "./analyze-balance-discrepancies";

interface CorrectionResult {
  organizationId: string;
  correctionAmount: number;
  affectedTransactions: string[];
  balanceHistoryId: string;
  success: boolean;
  error?: string;
}

interface CorrectionReport {
  executedAt: Date;
  totalCorrections: number;
  totalAmountCorrected: number;
  results: CorrectionResult[];
  summary: {
    successful: number;
    failed: number;
    totalImpact: number;
  };
}

async function executeBalanceCorrections(dryRun: boolean = true): Promise<CorrectionReport> {
  logger.info("Starting balance discrepancy corrections", { dryRun });

  // First, analyze current discrepancies
  const analysis = await analyzeBalanceDiscrepancies();
  
  if (analysis.affectedTransactions.length === 0) {
    logger.info("No balance discrepancies found to correct");
    return {
      executedAt: new Date(),
      totalCorrections: 0,
      totalAmountCorrected: 0,
      results: [],
      summary: { successful: 0, failed: 0, totalImpact: 0 }
    };
  }

  const results: CorrectionResult[] = [];
  let totalAmountCorrected = 0;
  let successful = 0;
  let failed = 0;

  console.log(`\n🔧 ${dryRun ? 'DRY RUN' : 'EXECUTING'} BALANCE CORRECTIONS`);
  console.log("=".repeat(60));

  // Process each affected organization
  for (const orgImpact of analysis.organizationImpacts) {
    const { organizationId, totalDiscrepancy, transactionIds } = orgImpact;

    console.log(`\n📋 Processing Organization: ${organizationId}`);
    console.log(`   Correction Amount: R$ ${totalDiscrepancy.toFixed(2)}`);
    console.log(`   Affected Transactions: ${transactionIds.length}`);

    if (dryRun) {
      console.log(`   🔍 DRY RUN: Would credit R$ ${totalDiscrepancy.toFixed(2)} to organization ${organizationId}`);
      
      results.push({
        organizationId,
        correctionAmount: totalDiscrepancy,
        affectedTransactions: transactionIds,
        balanceHistoryId: 'dry-run-id',
        success: true
      });
      
      successful++;
      totalAmountCorrected += totalDiscrepancy;
      continue;
    }

    try {
      // Execute the corrective credit operation
      const correctionId = createId();
      const description = `Correção de discrepância de saldo - Transações: ${transactionIds.join(', ')} - Padrão crítico detectado: RESERVE+UNRESERVE+DEBIT_RESERVED`;

      // Use the balance service to credit the organization
      await updateOrganizationBalance(
        organizationId,
        totalDiscrepancy,
        BalanceOperationType.CREDIT,
        correctionId,
        description
      );

      // Mark all affected transactions as reconciled
      await db.transaction.updateMany({
        where: {
          id: { in: transactionIds }
        },
        data: {
          metadata: {
            // Preserve existing metadata and add reconciliation info
            reconciled: true,
            reconciledAt: new Date().toISOString(),
            reconciliationType: 'balance_discrepancy_correction',
            correctionAmount: totalDiscrepancy,
            correctionId: correctionId,
            correctionReason: 'Critical pattern: RESERVE+UNRESERVE+DEBIT_RESERVED detected',
            correctedBy: 'automated_correction_script'
          }
        }
      });

      console.log(`   ✅ Successfully credited R$ ${totalDiscrepancy.toFixed(2)} to organization ${organizationId}`);
      console.log(`   📝 Marked ${transactionIds.length} transactions as reconciled`);

      results.push({
        organizationId,
        correctionAmount: totalDiscrepancy,
        affectedTransactions: transactionIds,
        balanceHistoryId: correctionId,
        success: true
      });

      successful++;
      totalAmountCorrected += totalDiscrepancy;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.error("Failed to execute balance correction", {
        organizationId,
        correctionAmount: totalDiscrepancy,
        error: errorMessage
      });

      console.log(`   ❌ Failed to correct balance for organization ${organizationId}: ${errorMessage}`);

      results.push({
        organizationId,
        correctionAmount: totalDiscrepancy,
        affectedTransactions: transactionIds,
        balanceHistoryId: '',
        success: false,
        error: errorMessage
      });

      failed++;
    }
  }

  const report: CorrectionReport = {
    executedAt: new Date(),
    totalCorrections: results.length,
    totalAmountCorrected,
    results,
    summary: {
      successful,
      failed,
      totalImpact: totalAmountCorrected
    }
  };

  return report;
}

async function verifyCorrections(): Promise<void> {
  console.log("\n🔍 VERIFYING CORRECTIONS");
  console.log("-".repeat(30));

  // Re-run analysis to check if discrepancies still exist
  const postCorrectionAnalysis = await analyzeBalanceDiscrepancies();

  if (postCorrectionAnalysis.affectedTransactions.length === 0) {
    console.log("✅ No remaining balance discrepancies detected");
  } else {
    console.log(`⚠️  ${postCorrectionAnalysis.affectedTransactions.length} discrepancies still detected`);
    console.log("This may indicate:");
    console.log("- New discrepancies occurred during correction");
    console.log("- Some corrections failed");
    console.log("- Additional manual review required");
  }
}

async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const verify = args.includes('--verify');

  if (dryRun) {
    console.log("🔍 RUNNING IN DRY RUN MODE");
    console.log("Use --execute flag to actually perform corrections");
    console.log("Use --verify flag to verify corrections after execution");
  }

  try {
    // Execute corrections
    const report = await executeBalanceCorrections(dryRun);

    // Display results
    console.log(`\n📊 CORRECTION SUMMARY`);
    console.log("-".repeat(30));
    console.log(`Execution Time: ${report.executedAt.toISOString()}`);
    console.log(`Total Corrections: ${report.totalCorrections}`);
    console.log(`Total Amount Corrected: R$ ${report.totalAmountCorrected.toFixed(2)}`);
    console.log(`Successful: ${report.summary.successful}`);
    console.log(`Failed: ${report.summary.failed}`);

    if (report.results.length > 0) {
      console.log(`\n📋 DETAILED RESULTS`);
      console.log("-".repeat(30));
      
      report.results.forEach((result, index) => {
        const status = result.success ? "✅" : "❌";
        console.log(`${index + 1}. ${status} Organization: ${result.organizationId}`);
        console.log(`   Amount: R$ ${result.correctionAmount.toFixed(2)}`);
        console.log(`   Transactions: ${result.affectedTransactions.length}`);
        if (result.error) {
          console.log(`   Error: ${result.error}`);
        }
        console.log();
      });
    }

    // Save report
    const reportFile = `correction-report-${new Date().toISOString().split('T')[0]}.json`;
    await require('fs').promises.writeFile(reportFile, JSON.stringify(report, null, 2));
    console.log(`📄 Correction report saved to: ${reportFile}`);

    // Verify corrections if requested and not dry run
    if (verify && !dryRun) {
      await verifyCorrections();
    }

    if (dryRun) {
      console.log("\n💡 To execute these corrections, run:");
      console.log("npm run fix-balance-discrepancies -- --execute");
    } else if (report.summary.successful > 0) {
      console.log("\n✅ Corrections completed successfully!");
      console.log("💡 Run with --verify flag to confirm no remaining discrepancies");
    }

  } catch (error) {
    logger.error("Error during balance correction execution", {
      error: error instanceof Error ? error.message : String(error)
    });
    console.error("❌ Correction execution failed:", error);
    process.exit(1);
  }
}

// Run the correction script
if (require.main === module) {
  main();
}

export { executeBalanceCorrections, verifyCorrections, type CorrectionReport };
