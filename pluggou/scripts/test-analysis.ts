#!/usr/bin/env tsx

/**
 * Test Script for Balance Discrepancy Analysis
 * 
 * Validates the analysis logic using the known problematic transaction
 * Transaction ID: cmh9ir22k000fib04a2pmkkw0
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";

async function testKnownTransaction() {
  const knownTransactionId = "cmh9ir22k000fib04a2pmkkw0";
  
  console.log("🧪 Testing Analysis Logic with Known Transaction");
  console.log("=".repeat(60));
  console.log(`Transaction ID: ${knownTransactionId}`);
  console.log();

  try {
    // Get the transaction details
    const transaction = await db.transaction.findUnique({
      where: { id: knownTransactionId },
      select: {
        id: true,
        organizationId: true,
        status: true,
        type: true,
        amount: true,
        totalFee: true,
        gatewayName: true,
        createdAt: true,
        metadata: true
      }
    });

    if (!transaction) {
      console.log("❌ Transaction not found in database");
      return;
    }

    console.log("📋 Transaction Details:");
    console.log(`   ID: ${transaction.id}`);
    console.log(`   Organization: ${transaction.organizationId}`);
    console.log(`   Status: ${transaction.status}`);
    console.log(`   Type: ${transaction.type}`);
    console.log(`   Amount: R$ ${transaction.amount.toFixed(2)}`);
    console.log(`   Fee: R$ ${(transaction.totalFee || 0).toFixed(2)}`);
    console.log(`   Gateway: ${transaction.gatewayName || 'unknown'}`);
    console.log(`   Created: ${transaction.createdAt.toISOString()}`);
    console.log();

    // Get balance operations
    const operations = await db.balance_history.findMany({
      where: { transactionId: knownTransactionId },
      orderBy: { createdAt: 'asc' },
      select: {
        id: true,
        operation: true,
        amount: true,
        description: true,
        createdAt: true,
        balanceAfterOperation: true
      }
    });

    console.log("💰 Balance Operations:");
    if (operations.length === 0) {
      console.log("   No balance operations found");
    } else {
      operations.forEach((op, index) => {
        console.log(`   ${index + 1}. ${op.operation} - R$ ${Number(op.amount).toFixed(2)}`);
        console.log(`      Created: ${op.createdAt.toISOString()}`);
        console.log(`      Description: ${op.description}`);
        console.log(`      Balance After: ${JSON.stringify(op.balanceAfterOperation)}`);
        console.log();
      });
    }

    // Analyze the pattern
    const operationTypes = operations.map(op => op.operation);
    const hasReserve = operationTypes.includes('RESERVE');
    const hasUnreserve = operationTypes.includes('UNRESERVE');
    const hasDebitReserved = operationTypes.includes('DEBIT_RESERVED');
    
    console.log("🔍 Pattern Analysis:");
    console.log(`   Has RESERVE: ${hasReserve ? '✅' : '❌'}`);
    console.log(`   Has UNRESERVE: ${hasUnreserve ? '✅' : '❌'}`);
    console.log(`   Has DEBIT_RESERVED: ${hasDebitReserved ? '✅' : '❌'}`);
    console.log(`   Operation Count: ${operations.length}`);
    console.log(`   Operation Pattern: ${operationTypes.join(' → ')}`);
    console.log();

    // Check if this matches our critical pattern
    const hasCriticalPattern = hasReserve && hasUnreserve && hasDebitReserved;
    const isCorrectTransactionType = transaction.type === 'SEND';
    const isCorrectStatus = transaction.status === 'CANCELED' || transaction.status === 'REJECTED';
    
    console.log("🚨 Critical Pattern Detection:");
    console.log(`   Is SEND transaction: ${isCorrectTransactionType ? '✅' : '❌'}`);
    console.log(`   Is CANCELED/REJECTED: ${isCorrectStatus ? '✅' : '❌'}`);
    console.log(`   Has critical pattern: ${hasCriticalPattern ? '✅' : '❌'}`);
    console.log(`   Should be detected: ${hasCriticalPattern && isCorrectTransactionType && isCorrectStatus ? '✅ YES' : '❌ NO'}`);
    console.log();

    if (hasCriticalPattern && isCorrectTransactionType && isCorrectStatus) {
      // Calculate discrepancy
      const debitReservedOp = operations.find(op => op.operation === 'DEBIT_RESERVED');
      const discrepancyAmount = debitReservedOp ? Number(debitReservedOp.amount) : 0;
      
      console.log("💸 Financial Impact:");
      console.log(`   Discrepancy Amount: R$ ${discrepancyAmount.toFixed(2)}`);
      console.log(`   Organization Affected: ${transaction.organizationId}`);
      console.log(`   Correction Needed: Credit R$ ${discrepancyAmount.toFixed(2)} to organization`);
      console.log();
      
      console.log("✅ This transaction SHOULD be detected by the analysis script");
    } else {
      console.log("ℹ️  This transaction would NOT be detected (missing critical pattern)");
    }

    // Test current organization balance
    const currentBalance = await db.organization_balance.findUnique({
      where: { organizationId: transaction.organizationId },
      select: {
        availableBalance: true,
        reservedBalance: true,
        pendingBalance: true,
        updatedAt: true
      }
    });

    if (currentBalance) {
      console.log("🏦 Current Organization Balance:");
      console.log(`   Available: R$ ${Number(currentBalance.availableBalance).toFixed(2)}`);
      console.log(`   Reserved: R$ ${Number(currentBalance.reservedBalance).toFixed(2)}`);
      console.log(`   Pending: R$ ${Number(currentBalance.pendingBalance).toFixed(2)}`);
      console.log(`   Last Updated: ${currentBalance.updatedAt.toISOString()}`);
    }

  } catch (error) {
    console.error("❌ Error during test:", error);
    logger.error("Test analysis failed", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: knownTransactionId
    });
  }
}

async function main() {
  await testKnownTransaction();
  
  console.log("\n💡 Next Steps:");
  console.log("1. If the pattern is detected correctly, run the full analysis:");
  console.log("   npm run analyze-balance-discrepancies");
  console.log();
  console.log("2. If discrepancies are found, run corrections in dry-run mode:");
  console.log("   npm run fix-balance-discrepancies");
  console.log();
  console.log("3. Execute actual corrections after reviewing dry-run results:");
  console.log("   npm run fix-balance-discrepancies -- --execute");
}

if (require.main === module) {
  main();
}
